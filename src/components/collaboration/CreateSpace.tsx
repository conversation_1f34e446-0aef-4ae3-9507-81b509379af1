import React, { useState, useMemo } from 'react';
import { UserPlusIcon, TrashIcon, MagnifyingGlassIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../../contexts/UserContext';
import { useLanguage } from '../../contexts/LanguageContext';

interface Member {
  id: number;
  username: string;
  skills: string[];
  experiencePercent: number;
  rating: number;
  isActive: boolean;
  payCash?: number;
}

interface RecommendedMember {
  id: number;
  name: string;
  level: number;
  skills: string[];
  experiencePercent: number;
  rating: number;
  avatar: string;
}

const CreateSpace: React.FC = () => {
  const { t, language } = useLanguage();
  const [spaceName, setSpaceName] = useState('');
  const [spaceDescription, setSpaceDescription] = useState('');
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [roleAssignments, setRoleAssignments] = useState<{[role: string]: {count: number, members: Member[]}}>({});
  const [privacy, setPrivacy] = useState('public');
  const [revenueShares, setRevenueShares] = useState<{[key: string]: number}>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const navigate = useNavigate();
  const { user } = useUser();
  const [invitedMembers, setInvitedMembers] = useState<Member[]>([
    { id: 1, username: 'XXX', skills: ['作词', '作曲'], experiencePercent: 15, rating: 4.6, isActive: true, payCash: 2000 },
    { id: 2, username: 'XXX', skills: ['编曲', '混音'], experiencePercent: 20, rating: 4.5, isActive: false, payCash: 3000 }
  ]);
  const [selectedMembers, setSelectedMembers] = useState<number[]>([]);
  const [showMemberDetail, setShowMemberDetail] = useState<any>(null);
  const [showInviteModal, setShowInviteModal] = useState<any>(null);
  const [showBatchInviteModal, setShowBatchInviteModal] = useState(false);

  const availableRoles = useMemo(() => [
    t('createspace.role.lyrics'), t('createspace.role.composition'), t('createspace.role.arrangement'), 
    t('createspace.role.vocals'), t('createspace.role.mixing'), t('createspace.role.production'), t('createspace.role.other')
  ], [t]);

  const searchCategories = useMemo(() => [t('createspace.category.all'), t('createspace.role.lyrics'), t('createspace.role.composition'), t('createspace.role.arrangement'), t('createspace.role.singing'), t('createspace.role.mixing'), t('createspace.role.production')], [t]);

  const recommendedMembers: RecommendedMember[] = useMemo(() => [
    {
      id: 1,
      name: t('createspace.member.lin.yuwei'),
      level: 8,
      skills: [t('createspace.role.lyrics'), t('createspace.role.composition')],
      experiencePercent: 15,
      rating: 4.9,
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=60&h=60&fit=crop&crop=face'
    },
    {
      id: 2,
      name: t('createspace.member.chen.junjie'),
      level: 7,
      skills: [t('createspace.role.arrangement'), t('createspace.role.mixing')],
      experiencePercent: 12,
      rating: 4.8,
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face'
    },
    {
      id: 3,
      name: t('createspace.member.zhang.mengqi'),
      level: 9,
      skills: [t('createspace.role.singing'), t('createspace.role.lyrics')],
      experiencePercent: 20,
      rating: 4.9,
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face'
    }
  ], [t]);

  const handleRoleToggle = (role: string) => {
    setSelectedRoles(prev => {
      const isRemoving = prev.includes(role);
      if (isRemoving) {
        // Remove role and its assignments
        setRoleAssignments(prevAssignments => {
          const newAssignments = { ...prevAssignments };
          delete newAssignments[role];
          return newAssignments;
        });
        return prev.filter(r => r !== role);
      } else {
        // Add role and initialize with 1 person
        setRoleAssignments(prevAssignments => ({
          ...prevAssignments,
          [role]: { count: 1, members: [] }
        }));
        return [...prev, role];
      }
    });
  };

  // Function to update role member count
  const updateRoleMemberCount = (role: string, count: number) => {
    setRoleAssignments(prev => ({
      ...prev,
      [role]: { ...prev[role], count: Math.max(1, count) }
    }));
  };

  const getStandardRevenueShare = (role: string): number => {
    const standardShares: {[key: string]: number} = {
      [t('createspace.role.lyrics')]: 25,
      [t('createspace.role.composition')]: 35,
      [t('createspace.role.arrangement')]: 20,
      [t('createspace.role.vocals')]: 15,
      [t('createspace.role.mixing')]: 10,
      [t('createspace.role.production')]: 15,
      [t('createspace.role.other')]: 5
    };
    return standardShares[role] || 10;
  };

  const handleInviteMember = (member: RecommendedMember) => {
    setInvitedMembers(prev => [...prev, {
      id: member.id,
      username: member.name,
      skills: member.skills,
      experiencePercent: member.experiencePercent,
      rating: member.rating,
      isActive: true
    }]);
  };

  const handleRemoveMember = (memberId: number) => {
    setInvitedMembers(prev => prev.filter(m => m.id !== memberId));
  };

  const getTotalPercentage = () => {
    return selectedRoles.reduce((sum, role) => {
      return sum + (revenueShares[role] || getStandardRevenueShare(role));
    }, 0);
  };

  const getCurrentRevenueShare = (role: string) => {
    return revenueShares[role] || getStandardRevenueShare(role);
  };

  const handleMemberSelect = (memberId: number) => {
    setSelectedMembers(prev => 
      prev.includes(memberId) 
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedMembers(invitedMembers.map(m => m.id));
    } else {
      setSelectedMembers([]);
    }
  };

  const handleViewMember = (member: Member) => {
    setShowMemberDetail(member);
  };

  const handleInviteSingleMember = (member: Member) => {
    setShowInviteModal(member);
  };

  const handleBatchInvite = () => {
    if (selectedMembers.length === 0) {
      alert(t('createspace.validation.select.members'));
      return;
    }
    setShowBatchInviteModal(true);
  };

  const handleCreateSpace = () => {
    if (!spaceName.trim()) {
      alert(t('createspace.validation.space.name'));
      return;
    }
    if (selectedRoles.length === 0) {
      alert(t('createspace.validation.select.roles'));
      return;
    }
    
    const totalPercentage = getTotalPercentage();
    if (totalPercentage !== 100) {
      alert(t('createspace.validation.revenue.total').replace('{percentage}', totalPercentage.toString()));
      return;
    }
    
    const finalRevenueShares = selectedRoles.reduce((shares, role) => {
      shares[role] = getCurrentRevenueShare(role);
      return shares;
    }, {} as {[key: string]: number});

    // 生成新的协创空间ID
    const newSpaceId = Date.now();
    
    const spaceData = {
      id: newSpaceId,
      spaceName: spaceName.trim(),
      description: spaceDescription.trim(),
      roles: selectedRoles,
      roleAssignments: roleAssignments, // Include role member counts
      privacy,
      revenueShares: finalRevenueShares,
      creator: user?.email || t('createspace.current.user'),
      invitedMembers,
      status: t('createspace.status.recruiting'),
      createdAt: new Date().toISOString()
    };
    
    // 将新创建的空间数据存储到 localStorage
    const existingSpaces = JSON.parse(localStorage.getItem('myCreatedSpaces') || '[]');
    existingSpaces.push(spaceData);
    localStorage.setItem('myCreatedSpaces', JSON.stringify(existingSpaces));
    
    console.log('Creating collaboration space:', spaceData);
    alert(t('createspace.success.created'));
    
    // 跳转到新创建的空间详情页，传递空间数据
    navigate('/collaboration', { 
      state: { 
        newSpaceId: newSpaceId,
        showNewSpace: true,
        spaceData: spaceData
      } 
    });
  };

  // 分成比例数据
  const revenueDistribution = useMemo(() => [
    { category: t('createspace.role.lyrics'), percentage: 20, color: '#3B82F6' },
    { category: t('createspace.role.composition'), percentage: 20, color: '#10B981' },
    { category: t('createspace.role.arrangement'), percentage: 15, color: '#F59E0B' },
    { category: t('createspace.role.singing'), percentage: 25, color: '#EF4444' },
    { category: t('createspace.role.production'), percentage: 20, color: '#8B5CF6' }
  ], [t]);

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* 返回按钮和页面标题 */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <button
            onClick={() => navigate('/collaboration')}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeftIcon className="w-5 h-5" />
            {t('createspace.back')}
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{t('createspace.title')}</h1>
            <p className="text-gray-600">{t('createspace.subtitle')}</p>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧进度栏 */}
        <div className="col-span-4">
          {/* 管理员信息 */}
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <div className="flex items-center mb-4">
              <img
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face"
                alt={t('createspace.admin')}
                className="w-15 h-15 rounded-full mr-4"
              />
              <div>
                <h3 className="text-lg font-medium text-gray-900">XXX XXX</h3>
                <p className="text-gray-600">{t('createspace.admin')}</p>
              </div>
            </div>

            {/* 项目进度 */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">{t('createspace.progress.title')}</span>
                <span className="text-sm text-gray-500">{t('createspace.progress.completed')}</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-green-600">{t('createspace.progress.team.building')}</span>
                  <span className="text-green-600">{t('createspace.progress.completed')}</span>
                </div>
                <div className="w-full bg-green-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full w-full"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-blue-600">{t('createspace.progress.revenue.confirmation')}</span>
                  <span className="text-blue-600">{t('createspace.progress.ongoing')}</span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-3/4"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">{t('createspace.progress.contract.signing')}</span>
                  <span className="text-gray-400">{t('createspace.progress.pending')}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gray-400 h-2 rounded-full w-0"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="col-span-8">

      {/* 空间基本信息 */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">{t('createspace.basic.info')}</h2>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('createspace.space.name')} *</label>
          <input
            type="text"
            placeholder={t('createspace.space.name.placeholder')}
            value={spaceName}
            onChange={(e) => setSpaceName(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('createspace.space.description')}</label>
          <textarea
            placeholder={t('createspace.space.description.placeholder')}
            value={spaceDescription}
            onChange={(e) => setSpaceDescription(e.target.value)}
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* 需要的人员 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">{t('createspace.required.roles')} *</h3>
          <div className="space-y-3">
            {availableRoles.map((role) => (
              <div key={role} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <label className="flex items-center flex-1">
                    <input
                      type="checkbox"
                      checked={selectedRoles.includes(role)}
                      onChange={() => handleRoleToggle(role)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-gray-700 font-medium">{role}</span>
                  </label>
                  
                  {selectedRoles.includes(role) && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">{t('createspace.member.count')}:</span>
                      <div className="flex items-center border border-gray-300 rounded">
                        <button
                          type="button"
                          onClick={() => updateRoleMemberCount(role, (roleAssignments[role]?.count || 1) - 1)}
                          className="px-2 py-1 text-gray-600 hover:bg-gray-100 rounded-l"
                          disabled={(roleAssignments[role]?.count || 1) <= 1}
                        >
                          −
                        </button>
                        <span className="px-3 py-1 text-sm font-medium min-w-[2rem] text-center">
                          {roleAssignments[role]?.count || 1}
                        </span>
                        <button
                          type="button"
                          onClick={() => updateRoleMemberCount(role, (roleAssignments[role]?.count || 1) + 1)}
                          className="px-2 py-1 text-gray-600 hover:bg-gray-100 rounded-r"
                          disabled={(roleAssignments[role]?.count || 1) >= 5}
                        >
                          +
                        </button>
                      </div>
                      <span className="text-xs text-gray-500">{t('createspace.member.count.hint')}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 隐私设置 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">{t('createspace.privacy.settings')} *</h3>
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="radio"
                name="privacy"
                value="public"
                checked={privacy === 'public'}
                onChange={(e) => setPrivacy(e.target.value)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <div className="ml-2">
                <span className="text-gray-700 font-medium">{t('createspace.privacy.public')}</span>
                <p className="text-sm text-gray-500">{t('createspace.privacy.public.desc')}</p>
              </div>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="privacy"
                value="participants"
                checked={privacy === 'participants'}
                onChange={(e) => setPrivacy(e.target.value)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <div className="ml-2">
                <span className="text-gray-700 font-medium">{t('createspace.privacy.participants')}</span>
                <p className="text-sm text-gray-500">{t('createspace.privacy.participants.desc')}</p>
              </div>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="privacy"
                value="searchable"
                checked={privacy === 'searchable'}
                onChange={(e) => setPrivacy(e.target.value)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <div className="ml-2">
                <span className="text-gray-700 font-medium">{t('createspace.privacy.searchable')}</span>
                <p className="text-sm text-gray-500">{t('createspace.privacy.searchable.desc')}</p>
              </div>
            </label>
          </div>
        </div>

        {/* 分成比例设置 */}
        {selectedRoles.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">{t('createspace.revenue.settings')}</h3>
            <p className="text-sm text-gray-600 mb-4">{t('createspace.revenue.description')}</p>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="space-y-4">
                {selectedRoles.map((role) => {
                  const currentShare = getCurrentRevenueShare(role);
                  return (
                    <div key={role} className="flex items-center justify-between">
                      <span className="text-gray-700 font-medium flex-1">{role}：</span>
                      <div className="flex items-center gap-3 flex-1">
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={currentShare}
                          onChange={(e) => setRevenueShares(prev => ({
                            ...prev,
                            [role]: parseInt(e.target.value)
                          }))}
                          className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                        />
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={currentShare}
                          onChange={(e) => setRevenueShares(prev => ({
                            ...prev,
                            [role]: parseInt(e.target.value) || 0
                          }))}
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded text-center"
                        />
                        <span className="text-gray-500 text-sm">%</span>
                      </div>
                    </div>
                  );
                })}
                <div className="border-t pt-3 mt-3">
                  <div className="flex items-center justify-between font-medium">
                    <span className="text-gray-700">{t('createspace.revenue.total')}</span>
                    <span className={`text-lg font-bold ${
                      getTotalPercentage() === 100 ? 'text-green-600' : 
                      getTotalPercentage() > 100 ? 'text-red-600' : 'text-yellow-600'
                    }`}>
                      {getTotalPercentage()}%
                    </span>
                  </div>
                </div>
              </div>
              {getTotalPercentage() !== 100 && (
                <div className={`mt-3 p-2 rounded text-sm ${
                  getTotalPercentage() > 100 ? 'bg-red-50 text-red-700' : 'bg-yellow-50 text-yellow-700'
                }`}>
                  {getTotalPercentage() > 100 ? t('createspace.revenue.warning') : t('createspace.revenue.hint')}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 分成比例可视化 */}
      {selectedRoles.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">{t('createspace.revenue.visualization')}</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 饼图区域 */}
            <div className="flex justify-center">
              <div className="relative w-64 h-64">
                <svg viewBox="0 0 100 100" className="w-full h-full transform -rotate-90">
                  {selectedRoles.map((role, index) => {
                    const percentage = getCurrentRevenueShare(role);
                    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#F97316'];
                    const offset = selectedRoles.slice(0, index).reduce((sum, prevRole) => sum + getCurrentRevenueShare(prevRole), 0);
                    const strokeDasharray = `${percentage} ${100 - percentage}`;
                    const strokeDashoffset = -offset;
                    
                    return (
                      <circle
                        key={role}
                        cx="50"
                        cy="50"
                        r="15.915"
                        fill="transparent"
                        stroke={colors[index % colors.length]}
                        strokeWidth="4"
                        strokeDasharray={strokeDasharray}
                        strokeDashoffset={strokeDashoffset}
                        className="transition-all duration-300"
                      />
                    );
                  })}
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${
                      getTotalPercentage() === 100 ? 'text-green-600' : 
                      getTotalPercentage() > 100 ? 'text-red-600' : 'text-yellow-600'
                    }`}>
                      {getTotalPercentage()}%
                    </div>
                    <div className="text-sm text-gray-500">总计</div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 图例和详情 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">{t('createspace.revenue.details')}</h3>
              <div className="space-y-3">
                {selectedRoles.map((role, index) => {
                  const percentage = getCurrentRevenueShare(role);
                  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#F97316'];
                  return (
                    <div key={role} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div
                          className="w-4 h-4 rounded"
                          style={{ backgroundColor: colors[index % colors.length] }}
                        ></div>
                        <span className="text-gray-700 font-medium">{role}</span>
                      </div>
                      <span className="font-bold text-gray-900">{percentage}%</span>
                    </div>
                  );
                })}
              </div>
              
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800">
                  {t('createspace.revenue.platform.standard')}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 邀请成员 */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">{t('createspace.invite.members')}</h2>
        
        {/* 平台推荐 */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">{t('createspace.platform.recommendations')}</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {recommendedMembers.map((member) => (
            <div key={member.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <img
                  src={member.avatar}
                  alt={member.name}
                  className="w-12 h-12 rounded-full mr-3"
                />
                <div>
                  <h4 className="font-medium text-gray-900">{member.name}</h4>
                  <p className="text-sm text-gray-600">Lv.{member.level}</p>
                </div>
              </div>
              
              <div className="mb-3">
                <p className="text-sm text-gray-700 mb-1">
                  {t('createspace.member.specialties')}{member.skills.join('、')}
                </p>
                <p className="text-sm text-gray-700 mb-1">
                  {t('createspace.member.expected.share')}{member.experiencePercent}%
                </p>
                <p className="text-sm text-gray-700">
                  {t('createspace.member.rating')}{member.rating}
                </p>
              </div>
              
              <button
                onClick={() => handleInviteMember(member)}
                className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                {t('createspace.invite.join')}
              </button>
            </div>
          ))}
        </div>

        </div>
        
        {/* 条件搜索 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">{t('createspace.conditional.search')}</h3>
          
          <div className="flex gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder={t('createspace.search.placeholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {searchCategories.map((category) => (
                <option key={category} value={category.toLowerCase()}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          {/* 成员表格 */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 px-4 py-2 text-left">
                    <input
                      type="checkbox"
                      checked={selectedMembers.length === invitedMembers.length && invitedMembers.length > 0}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th className="border border-gray-300 px-4 py-2 text-left">{t('createspace.table.number')}</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">{t('createspace.table.username')}</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">{t('createspace.table.role')}</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">{t('createspace.table.expected.share')}</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">{language === 'zh' ? '付现金' : 'Pay Cash'}</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">{t('createspace.table.rating')}</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">{t('createspace.table.portfolio')}</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">{t('createspace.table.actions')}</th>
                </tr>
              </thead>
              <tbody>
                {invitedMembers.map((member, index) => (
                  <tr key={member.id} className={selectedMembers.includes(member.id) ? 'bg-blue-50' : ''}>
                    <td className="border border-gray-300 px-4 py-2">
                      <input
                        type="checkbox"
                        checked={selectedMembers.includes(member.id)}
                        onChange={() => handleMemberSelect(member.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="border border-gray-300 px-4 py-2">{index + 1}</td>
                    <td className="border border-gray-300 px-4 py-2">{member.username}</td>
                    <td className="border border-gray-300 px-4 py-2">{member.skills.join(', ')}</td>
                    <td className="border border-gray-300 px-4 py-2">{member.experiencePercent}%</td>
                    <td className="border border-gray-300 px-4 py-2">
                      {member.payCash ? `¥${member.payCash.toLocaleString()}` : '-'}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">{member.rating}</td>
                    <td className="border border-gray-300 px-4 py-2">
                      {member.isActive ? t('createspace.table.yes') : t('createspace.table.no')}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      <div className="flex space-x-2">
                        <button 
                          onClick={() => handleViewMember(member)}
                          className="text-blue-600 hover:text-blue-800 text-sm"
                        >
                          {t('createspace.table.view')}
                        </button>
                        <button
                          onClick={() => handleInviteSingleMember(member)}
                          className="text-blue-600 hover:text-blue-800 text-sm"
                        >
                          {t('createspace.invite.join')}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
                {/* 示例空行 */}
                {[3, 4, 5].map((num) => (
                  <tr key={`empty-${num}`}>
                    <td className="border border-gray-300 px-4 py-2">
                      <input
                        type="checkbox"
                        disabled
                        className="rounded border-gray-300 text-blue-600 opacity-50"
                      />
                    </td>
                    <td className="border border-gray-300 px-4 py-2">{num}</td>
                    <td className="border border-gray-300 px-4 py-2 text-gray-400">{t('createspace.table.awaiting.users')}</td>
                    <td className="border border-gray-300 px-4 py-2"></td>
                    <td className="border border-gray-300 px-4 py-2"></td>
                    <td className="border border-gray-300 px-4 py-2"></td>
                    <td className="border border-gray-300 px-4 py-2"></td>
                    <td className="border border-gray-300 px-4 py-2"></td>
                    <td className="border border-gray-300 px-4 py-2">
                      <div className="flex space-x-2">
                        <button disabled className="text-gray-400 text-sm">{t('createspace.table.view')}</button>
                        <button disabled className="text-gray-400 text-sm">{t('createspace.table.invite')}</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="mt-4 flex items-center justify-between">
            <span className="text-sm text-gray-600">
              {t('createspace.selected.members').replace('{count}', selectedMembers.length.toString())}
            </span>
            <button 
              onClick={handleBatchInvite}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400"
              disabled={selectedMembers.length === 0}
            >
              {t('createspace.send.invitations').replace('{count}', selectedMembers.length.toString())}
            </button>
          </div>
        </div>
      </div>



        {/* 创建按钮 */}
        <div className="text-center">
          <button
            onClick={handleCreateSpace}
            className="bg-blue-600 text-white px-12 py-3 rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium"
          >
            {t('createspace.create.button')}
          </button>
        </div>
        </div>
      </div>

      {/* 成员详情模态框 */}
      {showMemberDetail && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{t('createspace.modal.member.info')}</h3>
              <button
                onClick={() => setShowMemberDetail(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-xl">
                  {showMemberDetail.username.charAt(0)}
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{showMemberDetail.username}</h4>
                  <p className="text-gray-600">{t('createspace.modal.platform.rating')}{showMemberDetail.rating}/5.0</p>
                </div>
              </div>
              <div>
                <p className="font-medium text-gray-700 mb-1">{t('createspace.modal.specialties.label')}</p>
                <div className="flex flex-wrap gap-2">
                  {showMemberDetail.skills.map((skill: string, index: number) => (
                    <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <p className="font-medium text-gray-700 mb-1">期望分成比例：</p>
                <p className="text-gray-900">{showMemberDetail.experiencePercent}%</p>
              </div>
              <div>
                <p className="font-medium text-gray-700 mb-1">{t('createspace.modal.portfolio.label')}</p>
                <p className="text-gray-900">{showMemberDetail.isActive ? '有' : '无'}</p>
              </div>
            </div>
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowMemberDetail(null)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                {t('createspace.modal.close')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 单个邀请模态框 */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{t('createspace.modal.invite.member')}</h3>
              <button
                onClick={() => setShowInviteModal(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold">
                    {showInviteModal.username.charAt(0)}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{showInviteModal.username}</h4>
                    <p className="text-gray-600">{showInviteModal.skills.join('、')}</p>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  <p>角色分成比例：{showInviteModal.experiencePercent}%</p>
                  <p>平台评分：{showInviteModal.rating}/5.0</p>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邀请消息
                </label>
                <textarea
                  placeholder="写一些邀请消息，让对方了解项目详情..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={4}
                  defaultValue={`您好！我正在创建一个音乐协创空间"${spaceName}"，希望邀请您以${showInviteModal.skills.join('、')}的角色参与项目。根据您的期望，建议分成比例为${showInviteModal.experiencePercent}%。期待与您合作！`}
                />
              </div>
            </div>
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowInviteModal(null)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                {t('createspace.modal.close')}
              </button>
              <button
                onClick={() => {
                  alert('邀请已发送！');
                  setShowInviteModal(null);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                邀请
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 批量邀请模态框 */}
      {showBatchInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">批量邀请成员</h3>
              <button
                onClick={() => setShowBatchInviteModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">邀请对象 ({selectedMembers.length}人)</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {invitedMembers
                    .filter(member => selectedMembers.includes(member.id))
                    .map(member => (
                      <div key={member.id} className="flex items-center gap-3 p-2 bg-white rounded border">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium text-sm">
                          {member.username.charAt(0)}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-gray-900 text-sm">{member.username}</p>
                          <p className="text-gray-600 text-xs">{member.skills.join('、')} - {member.experiencePercent}%</p>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邀请消息
                </label>
                <textarea
                  placeholder="写一些邀请消息，让对方了解项目详情..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={4}
                  defaultValue={`您好！我正在创建一个音乐协创空间"${spaceName}"，希望邀请您参与这个项目。我们需要${selectedRoles.join('、')}等角色，期待与您合作！`}
                />
              </div>
            </div>
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowBatchInviteModal(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                {t('createspace.modal.close')}
              </button>
              <button
                onClick={() => {
                  alert(`已向${selectedMembers.length}位成员发送邀请！`);
                  setShowBatchInviteModal(false);
                  setSelectedMembers([]);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                邀请 ({selectedMembers.length}人)
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateSpace;