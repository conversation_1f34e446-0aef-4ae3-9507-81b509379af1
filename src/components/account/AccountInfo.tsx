import React, { useState } from 'react';
import { UserCircleIcon, CogIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

const AccountInfo: React.FC = () => {
  const [activeSection, setActiveSection] = useState('basic');
  const { t } = useLanguage();

  const sections = [
    { id: 'basic', name: t('account.basic.info'), icon: UserCircleIcon },
    { id: 'security', name: t('account.security'), icon: CogIcon },
    { id: 'privacy', name: t('account.privacy'), icon: CogIcon },
    { id: 'notifications', name: t('account.notifications'), icon: CogIcon }
  ];

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <img 
          src="https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face" 
          alt="头像" 
          className="w-20 h-20 rounded-full"
        />
        <div>
          <h3 className="text-lg font-semibold">{t('account.demo.user')}</h3>
          <p className="text-gray-600">demo_user</p>
          <button className="mt-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
            {t('account.change.avatar')}
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('account.username')}</label>
          <input 
            type="text" 
            value="demo_user"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('account.nickname')}</label>
          <input 
            type="text" 
            value={t('account.demo.user')}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('account.email')}</label>
          <input 
            type="email" 
            value="<EMAIL>"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('account.phone')}</label>
          <input 
            type="tel" 
            value="138****8888"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{t('account.bio')}</label>
        <textarea 
          rows={4}
          value={t('account.bio.demo')}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
      </div>
      
      <div className="flex justify-end">
        <button className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
          {t('account.save.changes')}
        </button>
      </div>
    </div>
  );

  const renderSecurity = () => (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 className="font-semibold text-yellow-800">{t('account.security.tip.title')}</h4>
        <p className="text-yellow-700 mt-1">{t('account.security.tip.desc')}</p>
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('account.password')}</h4>
            <p className="text-gray-600">{t('account.password.last.modified')}</p>
          </div>
          <button className="px-4 py-2 text-indigo-600 border border-indigo-600 rounded-lg hover:bg-indigo-50">
            {t('account.change.password')}
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('account.two.factor.title')}</h4>
            <p className="text-gray-600">{t('account.two.factor.desc')}</p>
          </div>
          <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
            {t('account.enable')}
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('account.device.management.title')}</h4>
            <p className="text-gray-600">{t('account.device.management.desc')}</p>
          </div>
          <button className="px-4 py-2 text-indigo-600 border border-indigo-600 rounded-lg hover:bg-indigo-50">
            {t('account.view.devices')}
          </button>
        </div>
      </div>
    </div>
  );

  const renderPrivacy = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('account.profile.visibility.title')}</h4>
            <p className="text-gray-600">{t('account.profile.visibility.desc')}</p>
          </div>
          <select className="px-3 py-2 border border-gray-300 rounded-lg">
            <option>{t('account.visibility.everyone')}</option>
            <option>{t('account.visibility.following')}</option>
            <option>{t('account.visibility.self')}</option>
          </select>
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('account.works.visibility.title')}</h4>
            <p className="text-gray-600">{t('account.works.visibility.desc')}</p>
          </div>
          <select className="px-3 py-2 border border-gray-300 rounded-lg">
            <option>{t('account.visibility.everyone')}</option>
            <option>{t('account.visibility.following')}</option>
            <option>{t('account.visibility.self')}</option>
          </select>
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('account.allow.messages.title')}</h4>
            <p className="text-gray-600">{t('account.allow.messages.desc')}</p>
          </div>
          <select className="px-3 py-2 border border-gray-300 rounded-lg">
            <option>{t('account.visibility.everyone')}</option>
            <option>{t('account.visibility.following')}</option>
            <option>{t('account.visibility.none')}</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderNotifications = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('account.notification.followers.title')}</h4>
            <p className="text-gray-600">{t('account.notification.followers.desc')}</p>
          </div>
          <input type="checkbox" className="w-5 h-5 text-indigo-600" defaultChecked />
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('account.notification.comments.title')}</h4>
            <p className="text-gray-600">{t('account.notification.comments.desc')}</p>
          </div>
          <input type="checkbox" className="w-5 h-5 text-indigo-600" defaultChecked />
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('account.notification.system.title')}</h4>
            <p className="text-gray-600">{t('account.notification.system.desc')}</p>
          </div>
          <input type="checkbox" className="w-5 h-5 text-indigo-600" defaultChecked />
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('account.notification.email.title')}</h4>
            <p className="text-gray-600">{t('account.notification.email.desc')}</p>
          </div>
          <input type="checkbox" className="w-5 h-5 text-indigo-600" />
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'basic':
        return renderBasicInfo();
      case 'security':
        return renderSecurity();
      case 'privacy':
        return renderPrivacy();
      case 'notifications':
        return renderNotifications();
      default:
        return renderBasicInfo();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="flex">
        {/* 左侧导航 */}
        <div className="w-64 border-r border-gray-200 p-4">
          <h3 className="font-semibold text-gray-900 mb-4">{t('account.settings')}</h3>
          <nav className="space-y-2">
            {sections.map((section) => {
              const Icon = section.icon;
              const isActive = activeSection === section.id;
              
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                    isActive
                      ? 'bg-indigo-50 text-indigo-700'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Icon className={`h-5 w-5 mr-3 ${isActive ? 'text-indigo-500' : 'text-gray-400'}`} />
                  {section.name}
                </button>
              );
            })}
          </nav>
        </div>
        
        {/* 右侧内容 */}
        <div className="flex-1 p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default AccountInfo;