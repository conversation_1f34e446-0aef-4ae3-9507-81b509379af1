import React, { useState, useEffect } from 'react';
import { PlayIcon, UsersIcon, MusicalNoteIcon, VideoCameraIcon, HeartIcon, EyeIcon, ChatBubbleLeftIcon, ChevronLeftIcon, ChevronRightIcon, ArrowTrendingUpIcon } from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

const Homepage: React.FC = () => {
  const navigate = useNavigate();
  const { t, language } = useLanguage();
  const [currentBanner, setCurrentBanner] = useState(0);
  
  // Banner data for horizontal scroll
  const banners = [
    {
      id: 1,
      title: { en: "QUIT QUIETLY", zh: "悄然离场" },
      artist: "Artist Name",
      image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      category: { en: "Pop", zh: "流行" }
    },
    {
      id: 2,
      title: { en: "ELECTRONIC DREAMS", zh: "电子梦境" },
      artist: "DJ Producer",
      image: "https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      category: { en: "Electronic", zh: "电子" }
    },
    {
      id: 3,
      title: { en: "URBAN BEATS", zh: "都市节拍" },
      artist: "Hip Hop Artist",
      image: "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      category: { en: "Hip Hop", zh: "嘻哈" }
    },
    {
      id: 4,
      title: { en: "JAZZ VIBES", zh: "爵士氛围" },
      artist: "Jazz Ensemble",
      image: "https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      category: { en: "Jazz", zh: "爵士" }
    },
    {
      id: 5,
      title: { en: "ROCK ANTHEM", zh: "摇滚颂歌" },
      artist: "Rock Band",
      image: "https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      category: { en: "Rock", zh: "摇滚" }
    }
  ];
  
  // Trending songs data
  const trendingSongs = [
    {
      id: 1,
      title: "A Little More",
      artist: "Ed Sheeran",
      price: "HK$ 8.00",
      cover: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      rank: 1
    },
    {
      id: 2,
      title: "Fading Out",
      artist: "QUIT QUIETLY",
      price: "HK$ 8.00",
      cover: "https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      rank: 2
    },
    {
      id: 3,
      title: "Let's Dream",
      artist: "蘇打綠",
      price: "HK$ 8.00",
      cover: "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      rank: 3
    },
    {
      id: 4,
      title: "隨時隨地",
      artist: "阿信",
      price: "HK$ 8.00",
      cover: "https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      rank: 4
    },
    {
      id: 5,
      title: "Anti-People",
      artist: "Chace",
      price: "HK$ 8.00",
      cover: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      rank: 5
    },
    {
      id: 6,
      title: "Snow White",
      artist: "hurley",
      price: "HK$ 6.00",
      cover: "https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      rank: 6
    },
    {
      id: 7,
      title: "Loved You",
      artist: "Jonas Brothers",
      price: "HK$ 8.00",
      cover: "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      rank: 7
    },
    {
      id: 8,
      title: "接住",
      artist: "Luna is A Bet",
      price: "HK$ 8.00",
      cover: "https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      rank: 8
    },
    {
      id: 9,
      title: "捕获霓虹梦",
      artist: "可云万色",
      price: "HK$ 6.00",
      cover: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      rank: 9
    }
  ];
  
  // Auto scroll banner
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentBanner((prev) => (prev + 1) % banners.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [banners.length]);
  
  const nextBanner = () => {
    setCurrentBanner((prev) => (prev + 1) % banners.length);
  };
  
  const prevBanner = () => {
    setCurrentBanner((prev) => (prev - 1 + banners.length) % banners.length);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Horizontal Scroll Banner Section */}
      <section className="relative h-[300px] bg-white overflow-hidden">
        <div className="relative w-full h-full">
          {/* Banner container showing 3 images: 1/4 + full + 1/4 */}
          <div className="flex h-full w-full">
            {/* Left 1/4 banner */}
            <div className="w-1/4 h-full relative overflow-hidden">
              <div 
                className="w-full h-full bg-cover bg-center"
                style={{ 
                  backgroundImage: `url('${banners[(currentBanner - 1 + banners.length) % banners.length].image}')`
                }}
              ></div>
              {/* Left arrow positioned at bottom of left image */}
              <button 
                onClick={prevBanner}
                className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white p-3 rounded-full hover:bg-opacity-90 transition-all shadow-lg"
              >
                <ChevronLeftIcon className="h-6 w-6" />
              </button>
            </div>
            
            {/* Main center banner (full image) */}
            <div className="w-1/2 h-full relative overflow-hidden">
              <div 
                className="absolute inset-0 bg-cover bg-center"
                style={{ backgroundImage: `url('${banners[currentBanner].image}')` }}
              ></div>
              <div className="absolute inset-0 bg-black bg-opacity-30"></div>
              <div className="relative z-10 h-full flex items-center justify-center text-center text-white px-8">
                <div className="max-w-2xl">
                  <div className="inline-block bg-white bg-opacity-90 text-black px-4 py-2 rounded-full text-sm font-medium mb-4">
                    {banners[currentBanner].category[language as 'en' | 'zh']}
                  </div>
                  <h1 className="text-2xl md:text-4xl font-bold mb-4">
                    {banners[currentBanner].title[language as 'en' | 'zh']}
                  </h1>
                  <p className="text-base mb-6 opacity-90">
                    by {banners[currentBanner].artist}
                  </p>
                  <button className="bg-white text-black px-6 py-2 rounded-full font-medium hover:bg-gray-100 transition-colors">
                    {language === 'en' ? 'Listen Now' : '立即收听'}
                  </button>
                </div>
              </div>
            </div>
            
            {/* Right 1/4 banner */}
            <div className="w-1/4 h-full relative overflow-hidden">
              <div 
                className="w-full h-full bg-cover bg-center"
                style={{ 
                  backgroundImage: `url('${banners[(currentBanner + 1) % banners.length].image}')`
                }}
              ></div>
              {/* Right arrow positioned at bottom of right image */}
              <button 
                onClick={nextBanner}
                className="absolute bottom-4 right-4 bg-black bg-opacity-70 text-white p-3 rounded-full hover:bg-opacity-90 transition-all shadow-lg"
              >
                <ChevronRightIcon className="h-6 w-6" />
              </button>
            </div>
          </div>
          
          {/* Dots indicator at bottom center */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="flex space-x-2">
              {banners.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentBanner(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentBanner ? 'bg-white' : 'bg-white bg-opacity-50'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>
      
      {/* Trending Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-2">
              <ArrowTrendingUpIcon className="h-6 w-6 text-orange-500" />
              <h2 className="text-2xl font-bold text-gray-900">
                {language === 'en' ? 'Daily Trending' : '每日新聲'}
              </h2>
            </div>
            <button className="text-blue-600 hover:text-blue-800 font-medium">
              {language === 'en' ? 'View All' : '顯示全部'} →
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {trendingSongs.map((song, index) => (
              <div key={song.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
                <div className="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                  <img 
                    src={song.cover} 
                    alt={song.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900 truncate">{song.title}</h3>
                  <p className="text-sm text-gray-600 truncate">{song.artist}</p>
                </div>
                <div className="flex-shrink-0 text-right">
                  <div className="text-sm font-medium text-gray-900">{song.price}</div>
                  <div className="text-xs text-gray-500">#{song.rank}</div>
                </div>
                <button className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600">
                  <PlayIcon className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Discover Creative Spaces */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <div className="flex items-center space-x-2">
              <UsersIcon className="h-6 w-6 text-orange-500" />
              <h2 className="text-2xl font-bold text-gray-900">
                {language === 'en' ? 'Discover Creative Spaces' : '发现创作空间'}
              </h2>
            </div>
            <button 
              onClick={() => navigate('/collaboration')}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              {language === 'en' ? 'View All' : '显示全部'} →
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                id: 1,
                titleKey: "homepage.space.electronic.title",
                descKey: "homepage.space.electronic.desc",
                categoryKey: "homepage.space.electronic.category",
                image: "https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                memberCount: 42
              },
              {
                id: 2,
                titleKey: "homepage.space.folk.title",
                descKey: "homepage.space.folk.desc",
                categoryKey: "homepage.space.folk.category",
                image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                memberCount: 28
              },
              {
                id: 3,
                titleKey: "homepage.space.hiphop.title",
                descKey: "homepage.space.hiphop.desc",
                categoryKey: "homepage.space.hiphop.category",
                image: "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                memberCount: 67
              }
            ].map((space) => (
              <div key={space.id} className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="relative h-48 bg-gray-200 overflow-hidden">
                  <img 
                    src={space.image} 
                    alt={t(space.titleKey)}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 bg-black bg-opacity-60 text-white px-3 py-1 rounded-full text-sm">
                    {t(space.categoryKey)}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{t(space.titleKey)}</h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {t(space.descKey)}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">{space.memberCount}{t('homepage.space.members')}</span>
                    <button 
                      onClick={() => navigate('/collaboration')}
                      className="bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors font-medium"
                    >
                      {t('collab.join')}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Quality Works */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <div className="flex items-center space-x-2">
              <MusicalNoteIcon className="h-6 w-6 text-orange-500" />
              <h2 className="text-2xl font-bold text-gray-900">
                {language === 'en' ? 'Quality Works' : '优质作品'}
              </h2>
            </div>
            <button 
              onClick={() => navigate('/works')}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              {language === 'en' ? 'View All' : '显示全部'} →
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                id: 1,
                titleKey: "homepage.work.summer.breeze",
                artistKey: "homepage.artist.chen.ma",
                cover: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
                plays: "2.4k",
                likes: "856",
                duration: "04:31"
              },
              {
                id: 2,
                titleKey: "homepage.work.electronic.dream",
                artistKey: "homepage.artist.dj.xiaoyu",
                cover: "https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
                plays: "1.8k",
                likes: "623",
                duration: "03:45"
              },
              {
                id: 3,
                titleKey: "homepage.work.city.night",
                artistKey: "homepage.artist.wang.li",
                cover: "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
                plays: "3.2k",
                likes: "1.2k",
                duration: "05:12"
              },
              {
                id: 4,
                titleKey: "homepage.work.starry.tale",
                artistKey: "homepage.artist.zhang.sophie",
                cover: "https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
                plays: "1.1k",
                likes: "432",
                duration: "03:28"
              }
            ].map((work) => (
              <div key={work.id} className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                <div className="relative">
                  <img 
                    src={work.cover} 
                    alt={t(work.titleKey)}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                    <button className="opacity-0 group-hover:opacity-100 transform scale-75 group-hover:scale-100 transition-all duration-300">
                      <div className="bg-white rounded-full p-4 shadow-lg">
                        <PlayIcon className="h-8 w-8 text-blue-600" />
                      </div>
                    </button>
                  </div>
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-xs">
                    {work.duration}
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-bold text-gray-900 mb-1 truncate">{t(work.titleKey)}</h3>
                  <p className="text-sm text-gray-600 mb-3 truncate">by {t(work.artistKey)}</p>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        <PlayIcon className="h-4 w-4" />
                        <span>{work.plays}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <HeartIcon className="h-4 w-4" />
                        <span>{work.likes}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Recent Activities */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <div className="flex items-center space-x-2">
              <VideoCameraIcon className="h-6 w-6 text-orange-500" />
              <h2 className="text-2xl font-bold text-gray-900">
                {language === 'en' ? 'Recent Activities' : '近期活动'}
              </h2>
            </div>
            <button className="text-blue-600 hover:text-blue-800 font-medium">
              {language === 'en' ? 'View All' : '显示全部'} →
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                id: 1,
                titleKey: "homepage.activity.spring.festival",
                descKey: "homepage.activity.spring.festival.desc",
                statusKey: "homepage.activity.status.enrolling",
                image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                date: "2024-05-15"
              },
              {
                id: 2,
                titleKey: "homepage.activity.indie.masterclass",
                descKey: "homepage.activity.indie.masterclass.desc",
                statusKey: "homepage.activity.status.starting.soon",
                image: "https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                date: "2024-06-20"
              },
              {
                id: 3,
                titleKey: "homepage.activity.youth.contest",
                descKey: "homepage.activity.youth.contest.desc",
                statusKey: "homepage.activity.status.hot.signup",
                image: "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                date: "2024-07-10"
              }
            ].map((activity) => (
              <div key={activity.id} className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300">
                <div className="relative h-64 overflow-hidden">
                  <img 
                    src={activity.image} 
                    alt={t(activity.titleKey)}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute top-4 right-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {t(activity.statusKey)}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">{t(activity.titleKey)}</h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {t(activity.descKey)}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">{t('homepage.activity.time')}{activity.date}</span>
                    <button 
                      onClick={() => alert('活动报名功能开发中，敬请期待！')}
                      className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-full hover:from-blue-600 hover:to-purple-700 transition-all duration-300 font-medium"
                    >
                      {t('homepage.activity.signup')}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 加入我们的音乐社区 */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center opacity-20"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')"
          }}
        ></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto mb-16">
            <h2 className="text-5xl font-bold mb-6">{t('homepage.join.community')}</h2>
            <p className="text-xl text-blue-100 leading-relaxed">
              {t('homepage.community.main.desc')}
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16 text-center">
            <div className="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-6 border border-white border-opacity-20">
              <div className="text-4xl font-bold text-orange-400 mb-2">10,000+</div>
              <div className="text-white text-sm">{t('homepage.stats.creators')}</div>
            </div>
            <div className="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-6 border border-white border-opacity-20">
              <div className="text-4xl font-bold text-orange-400 mb-2">50,000+</div>
              <div className="text-white text-sm">{t('homepage.stats.works')}</div>
            </div>
            <div className="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-6 border border-white border-opacity-20">
              <div className="text-4xl font-bold text-orange-400 mb-2">1,000+</div>
              <div className="text-white text-sm">{t('homepage.stats.projects')}</div>
            </div>
            <div className="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-6 border border-white border-opacity-20">
              <div className="text-4xl font-bold text-orange-400 mb-2">100+</div>
              <div className="text-white text-sm">{t('homepage.stats.events')}</div>
            </div>
          </div>

          <div className="bg-white bg-opacity-10 backdrop-blur-lg rounded-3xl p-12 border border-white border-opacity-20 max-w-md mx-auto">
            <h3 className="text-2xl font-bold mb-6">{t('homepage.join.now')}</h3>
            <form className="space-y-4">
              <input
                type="text"
                placeholder={t('homepage.form.name.placeholder')}
                className="w-full px-4 py-3 rounded-full bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 placeholder-white placeholder-opacity-70 text-white focus:outline-none focus:ring-2 focus:ring-orange-400"
              />
              <input
                type="email"
                placeholder={t('homepage.form.email.placeholder')}
                className="w-full px-4 py-3 rounded-full bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 placeholder-white placeholder-opacity-70 text-white focus:outline-none focus:ring-2 focus:ring-orange-400"
              />
              <select className="w-full px-4 py-3 rounded-full bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 text-white focus:outline-none focus:ring-2 focus:ring-orange-400">
                <option value="">{t('homepage.form.style.placeholder')}</option>
                <option value="pop">{t('homepage.form.style.pop')}</option>
                <option value="rock">{t('homepage.form.style.rock')}</option>
                <option value="electronic">{t('homepage.form.style.electronic')}</option>
                <option value="jazz">{t('homepage.form.style.jazz')}</option>
                <option value="classical">{t('homepage.form.style.classical')}</option>
                <option value="hip-hop">{t('homepage.form.style.hiphop')}</option>
              </select>
              <button 
                type="submit"
                onClick={(e) => {
                  e.preventDefault();
                  navigate('/register');
                }}
                className="w-full bg-gradient-to-r from-orange-500 to-pink-600 text-white font-bold py-4 rounded-full hover:from-orange-600 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                {t('homepage.start.journey')}
              </button>
            </form>
          </div>
        </div>
      </section>


      {/* 页脚 */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">iBOM</h3>
              <p className="text-gray-400 text-sm">
                {t('footer.description')}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-semibold mb-4">{t('footer.products')}</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white">{t('footer.products.collaboration')}</a></li>
                <li><a href="#" className="hover:text-white">{t('footer.products.daw')}</a></li>
                <li><a href="#" className="hover:text-white">{t('footer.products.live')}</a></li>
                <li><a href="#" className="hover:text-white">{t('footer.products.ai')}</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-semibold mb-4">{t('footer.community')}</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white">{t('footer.community.musicians')}</a></li>
                <li><a href="#" className="hover:text-white">{t('footer.community.works')}</a></li>
                <li><a href="#" className="hover:text-white">{t('footer.community.events')}</a></li>
                <li><a href="#" className="hover:text-white">{t('footer.community.help')}</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-semibold mb-4">{t('footer.about')}</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white">{t('footer.about.company')}</a></li>
                <li><a href="#" className="hover:text-white">{t('footer.about.contact')}</a></li>
                <li><a href="#" className="hover:text-white">{t('footer.about.privacy')}</a></li>
                <li><a href="#" className="hover:text-white">{t('footer.about.terms')}</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>{t('footer.copyright')}</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Homepage;