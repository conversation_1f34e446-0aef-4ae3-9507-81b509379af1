import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  PlayIcon, 
  PauseIcon,
  StopIcon,
  MicrophoneIcon,
  SpeakerWaveIcon,
  ArrowLeftIcon,
  HeartIcon,
  ShareIcon,
  BookmarkIcon,
  AdjustmentsHorizontalIcon,
  MusicalNoteIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../contexts/LanguageContext';

interface KaraokeTrack {
  id: number;
  title: string;
  artist: string;
  duration: string;
  coverUrl: string;
  lyrics: Array<{
    time: number;
    text: string;
    translation?: string;
  }>;
  originalAudio?: string;
  instrumentalAudio?: string;
}

const KaraokePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t, language } = useLanguage();
  
  const [track, setTrack] = useState<KaraokeTrack | null>(null);
  const [loading, setLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentLyricIndex, setCurrentLyricIndex] = useState(0);
  const [micEnabled, setMicEnabled] = useState(false);
  const [volumeOriginal, setVolumeOriginal] = useState(50);
  const [volumeInstrumental, setVolumeInstrumental] = useState(100);
  const [showTranslation, setShowTranslation] = useState(false);
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    // Mock track data based on the work/demo ID
    const mockTrack: KaraokeTrack = {
      id: parseInt(id || '1'),
      title: language === 'zh' ? '为梦而飞' : 'Dreams in Flight',
      artist: language === 'zh' ? '张丹' : 'Zhang Dan',
      duration: '04:31',
      coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      lyrics: [
        { time: 0, text: language === 'zh' ? '每个梦想都有力量' : 'Every dream has power', translation: language === 'zh' ? 'Every dream has power' : '每个梦想都有力量' },
        { time: 5, text: language === 'zh' ? '推动我们向前闯' : 'Pushing us to move forward', translation: language === 'zh' ? 'Pushing us to move forward' : '推动我们向前闯' },
        { time: 10, text: language === 'zh' ? '不怕路途有多长' : 'Not afraid of how long the journey is', translation: language === 'zh' ? 'Not afraid of how long the journey is' : '不怕路途有多长' },
        { time: 15, text: language === 'zh' ? '只要心中有方向' : 'As long as there is direction in the heart', translation: language === 'zh' ? 'As long as there is direction in the heart' : '只要心中有方向' },
        { time: 20, text: language === 'zh' ? '为梦而飞' : 'Fly for dreams', translation: language === 'zh' ? 'Fly for dreams' : '为梦而飞' },
        { time: 25, text: language === 'zh' ? '青春无悔' : 'Youth without regrets', translation: language === 'zh' ? 'Youth without regrets' : '青春无悔' },
        { time: 30, text: language === 'zh' ? '每一次跌倒都是成长' : 'Every fall is growth', translation: language === 'zh' ? 'Every fall is growth' : '每一次跌倒都是成长' },
        { time: 35, text: language === 'zh' ? '每一次努力都有光芒' : 'Every effort has its light', translation: language === 'zh' ? 'Every effort has its light' : '每一次努力都有光芒' },
        { time: 40, text: language === 'zh' ? '为梦而飞' : 'Fly for dreams', translation: language === 'zh' ? 'Fly for dreams' : '为梦而飞' },
        { time: 45, text: language === 'zh' ? '永不后退' : 'Never retreat', translation: language === 'zh' ? 'Never retreat' : '永不后退' },
        { time: 50, text: language === 'zh' ? '即使世界风雨如磐' : 'Even when the world is stormy', translation: language === 'zh' ? 'Even when the world is stormy' : '即使世界风雨如磐' },
        { time: 55, text: language === 'zh' ? '我们的梦想依然灿烂' : 'Our dreams still shine bright', translation: language === 'zh' ? 'Our dreams still shine bright' : '我们的梦想依然灿烂' }
      ],
      originalAudio: '/audio/original.mp3',
      instrumentalAudio: '/audio/instrumental.mp3'
    };

    setTrack(mockTrack);
    setDuration(271); // 4:31 in seconds
    setLoading(false);
  }, [id, language]);

  useEffect(() => {
    if (track && currentTime > 0) {
      // Find current lyric based on time
      let index = 0;
      for (let i = 0; i < track.lyrics.length; i++) {
        if (currentTime >= track.lyrics[i].time) {
          index = i;
        } else {
          break;
        }
      }
      setCurrentLyricIndex(index);
    }
  }, [currentTime, track]);

  useEffect(() => {
    // Update audio time
    const interval = setInterval(() => {
      if (isPlaying) {
        setCurrentTime(prev => Math.min(prev + 1, duration));
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isPlaying, duration]);

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleStop = () => {
    setIsPlaying(false);
    setCurrentTime(0);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTime = parseInt(e.target.value);
    setCurrentTime(newTime);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleMicToggle = async () => {
    if (!micEnabled) {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true });
        setMicEnabled(true);
      } catch (error) {
        alert(language === 'zh' ? '无法访问麦克风，请检查权限设置' : 'Cannot access microphone, please check permissions');
      }
    } else {
      setMicEnabled(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!track) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {language === 'zh' ? '歌曲未找到' : 'Track not found'}
          </h2>
          <button
            onClick={() => navigate(-1)}
            className="text-blue-600 hover:text-blue-800"
          >
            {language === 'zh' ? '返回' : 'Go back'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <div className="bg-black bg-opacity-30 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center space-x-2 text-white hover:text-blue-300 transition-colors"
            >
              <ArrowLeftIcon className="h-6 w-6" />
              <span>{language === 'zh' ? '返回' : 'Back'}</span>
            </button>
            
            <div className="flex items-center space-x-4 text-white">
              <button className="p-2 hover:bg-white hover:bg-opacity-10 rounded-lg transition-colors">
                <HeartIcon className="h-6 w-6" />
              </button>
              <button className="p-2 hover:bg-white hover:bg-opacity-10 rounded-lg transition-colors">
                <ShareIcon className="h-6 w-6" />
              </button>
              <button className="p-2 hover:bg-white hover:bg-opacity-10 rounded-lg transition-colors">
                <BookmarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Karaoke Area */}
          <div className="lg:col-span-2">
            {/* Song Info */}
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 mb-6 text-white">
              <div className="flex items-center space-x-6">
                <img
                  src={track.coverUrl}
                  alt={track.title}
                  className="w-24 h-24 rounded-xl object-cover"
                />
                <div>
                  <h1 className="text-3xl font-bold mb-2">{track.title}</h1>
                  <p className="text-xl text-blue-200">{track.artist}</p>
                  <p className="text-blue-300">{track.duration}</p>
                </div>
              </div>
            </div>

            {/* Lyrics Display */}
            <div className="bg-black bg-opacity-40 backdrop-blur-sm rounded-2xl p-8 mb-6 min-h-[400px]">
              <div className="text-center">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center justify-center space-x-2">
                  <MusicalNoteIcon className="h-6 w-6" />
                  <span>{language === 'zh' ? '歌词' : 'Lyrics'}</span>
                  <button
                    onClick={() => setShowTranslation(!showTranslation)}
                    className="ml-4 px-3 py-1 bg-blue-600 text-white text-sm rounded-full hover:bg-blue-700 transition-colors"
                  >
                    {showTranslation ? (language === 'zh' ? '隐藏翻译' : 'Hide Translation') : (language === 'zh' ? '显示翻译' : 'Show Translation')}
                  </button>
                </h3>
                
                <div className="space-y-4 max-h-80 overflow-y-auto">
                  {track.lyrics.map((lyric, index) => (
                    <div
                      key={index}
                      className={`transition-all duration-500 ${
                        index === currentLyricIndex
                          ? 'text-3xl font-bold text-yellow-300 scale-110'
                          : index === currentLyricIndex - 1 || index === currentLyricIndex + 1
                          ? 'text-xl text-white opacity-80'
                          : 'text-lg text-gray-300 opacity-60'
                      }`}
                    >
                      <div>{lyric.text}</div>
                      {showTranslation && lyric.translation && (
                        <div className="text-sm text-blue-200 mt-1 opacity-70">
                          {lyric.translation}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Controls */}
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6">
              {/* Progress Bar */}
              <div className="mb-6">
                <div className="flex items-center justify-between text-white text-sm mb-2">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(duration)}</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max={duration}
                  value={currentTime}
                  onChange={handleSeek}
                  className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>

              {/* Play Controls */}
              <div className="flex items-center justify-center space-x-6 mb-6">
                <button
                  onClick={handleStop}
                  className="p-3 bg-gray-600 text-white rounded-full hover:bg-gray-700 transition-colors"
                >
                  <StopIcon className="h-6 w-6" />
                </button>
                
                <button
                  onClick={handlePlayPause}
                  className="p-4 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                >
                  {isPlaying ? (
                    <PauseIcon className="h-8 w-8" />
                  ) : (
                    <PlayIcon className="h-8 w-8" />
                  )}
                </button>

                <button
                  onClick={handleMicToggle}
                  className={`p-3 rounded-full transition-colors ${
                    micEnabled 
                      ? 'bg-red-600 text-white hover:bg-red-700' 
                      : 'bg-gray-600 text-white hover:bg-gray-700'
                  }`}
                >
                  <MicrophoneIcon className="h-6 w-6" />
                </button>
              </div>

              {/* Volume Controls */}
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-white text-sm font-medium mb-2">
                    {language === 'zh' ? '原唱音量' : 'Original Volume'}
                  </label>
                  <div className="flex items-center space-x-3">
                    <SpeakerWaveIcon className="h-5 w-5 text-white" />
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={volumeOriginal}
                      onChange={(e) => setVolumeOriginal(parseInt(e.target.value))}
                      className="flex-1 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                    />
                    <span className="text-white text-sm w-8">{volumeOriginal}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-white text-sm font-medium mb-2">
                    {language === 'zh' ? '伴奏音量' : 'Instrumental Volume'}
                  </label>
                  <div className="flex items-center space-x-3">
                    <MusicalNoteIcon className="h-5 w-5 text-white" />
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={volumeInstrumental}
                      onChange={(e) => setVolumeInstrumental(parseInt(e.target.value))}
                      className="flex-1 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                    />
                    <span className="text-white text-sm w-8">{volumeInstrumental}</span>
                  </div>
                </div>
              </div>

              {/* Status */}
              <div className="mt-4 flex items-center justify-between text-sm">
                <div className="flex items-center space-x-4 text-white">
                  <div className={`flex items-center space-x-2 ${isPlaying ? 'text-green-400' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${isPlaying ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                    <span>{isPlaying ? (language === 'zh' ? '播放中' : 'Playing') : (language === 'zh' ? '已暂停' : 'Paused')}</span>
                  </div>
                  
                  <div className={`flex items-center space-x-2 ${micEnabled ? 'text-red-400' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${micEnabled ? 'bg-red-400' : 'bg-gray-400'}`}></div>
                    <span>{micEnabled ? (language === 'zh' ? '麦克风开启' : 'Mic On') : (language === 'zh' ? '麦克风关闭' : 'Mic Off')}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Song Details */}
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 text-white">
              <h3 className="text-lg font-bold mb-4">{language === 'zh' ? '歌曲信息' : 'Song Info'}</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="text-blue-200">{language === 'zh' ? '时长：' : 'Duration: '}</span>
                  <span>{track.duration}</span>
                </div>
                <div>
                  <span className="text-blue-200">{language === 'zh' ? '演唱者：' : 'Artist: '}</span>
                  <span>{track.artist}</span>
                </div>
                <div>
                  <span className="text-blue-200">{language === 'zh' ? '当前行：' : 'Current Line: '}</span>
                  <span>{currentLyricIndex + 1} / {track.lyrics.length}</span>
                </div>
              </div>
            </div>

            {/* Settings */}
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 text-white">
              <h3 className="text-lg font-bold mb-4 flex items-center space-x-2">
                <AdjustmentsHorizontalIcon className="h-5 w-5" />
                <span>{language === 'zh' ? '设置' : 'Settings'}</span>
              </h3>
              <div className="space-y-4">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={showTranslation}
                    onChange={(e) => setShowTranslation(e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm">{language === 'zh' ? '显示翻译' : 'Show Translation'}</span>
                </label>
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 text-white">
              <h3 className="text-lg font-bold mb-4">{language === 'zh' ? '使用说明' : 'Instructions'}</h3>
              <div className="text-sm space-y-2 text-blue-200">
                <p>• {language === 'zh' ? '点击播放按钮开始K歌' : 'Click play to start karaoke'}</p>
                <p>• {language === 'zh' ? '调节原唱和伴奏音量' : 'Adjust vocal and instrumental volume'}</p>
                <p>• {language === 'zh' ? '开启麦克风进行录音' : 'Enable microphone for recording'}</p>
                <p>• {language === 'zh' ? '跟随高亮歌词演唱' : 'Follow highlighted lyrics to sing'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <audio ref={audioRef} />
      <canvas ref={canvasRef} className="hidden" />

      <style dangerouslySetInnerHTML={{
        __html: `
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: none;
        }
        `
      }} />
    </div>
  );
};

export default KaraokePage;