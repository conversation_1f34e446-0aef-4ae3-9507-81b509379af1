import React, { useState, useEffect } from 'react';
import { ApiService } from '../../services/api';
import { useLanguage } from '../../contexts/LanguageContext';

interface Post {
  id: number;
  authorId: number;
  authorName: string;
  authorAvatar?: string;
  content: string;
  mediaUrls?: string[];
  likesCount: number;
  commentsCount: number;
  sharesCount: number;
  isLiked: boolean;
  createdAt: string;
}

const SocialFeed: React.FC = () => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [newPost, setNewPost] = useState('');
  const [posting, setPosting] = useState(false);
  const { t } = useLanguage();

  useEffect(() => {
    loadPosts();
  }, []);

  const loadPosts = async () => {
    try {
      const response = await ApiService.getPosts();
      setPosts(response.posts || []);
    } catch (error) {
      console.error('Failed to load posts:', error);
      // 使用模拟数据
      setPosts([
        {
          id: 1,
          authorId: 1,
          authorName: '音乐制作人张三',
          authorAvatar: undefined,
          content: '刚刚完成了一首新的电子音乐作品，融合了古典和现代元素，希望大家喜欢！',
          mediaUrls: [],
          likesCount: 42,
          commentsCount: 8,
          sharesCount: 3,
          isLiked: false,
          createdAt: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          authorId: 2,
          authorName: '歌手李四',
          authorAvatar: undefined,
          content: '今天在录音棚录制新专辑，感谢团队的辛苦付出！',
          mediaUrls: [],
          likesCount: 156,
          commentsCount: 23,
          sharesCount: 12,
          isLiked: true,
          createdAt: '2024-01-15T08:15:00Z'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePost = async () => {
    if (!newPost.trim() || posting) return;

    setPosting(true);
    try {
      await ApiService.createPost({ content: newPost });
      setNewPost('');
      loadPosts(); // 重新加载帖子
    } catch (error) {
      console.error('Failed to create post:', error);
      // 模拟添加帖子
      const mockPost: Post = {
        id: Date.now(),
        authorId: 999,
        authorName: t('social.current.user'),
        content: newPost,
        likesCount: 0,
        commentsCount: 0,
        sharesCount: 0,
        isLiked: false,
        createdAt: new Date().toISOString()
      };
      setPosts([mockPost, ...posts]);
      setNewPost('');
    } finally {
      setPosting(false);
    }
  };

  const handleLike = async (postId: number) => {
    try {
      await ApiService.likePost(postId);
      setPosts(posts.map(post => 
        post.id === postId 
          ? { 
              ...post, 
              isLiked: !post.isLiked,
              likesCount: post.isLiked ? post.likesCount - 1 : post.likesCount + 1
            }
          : post
      ));
    } catch (error) {
      console.error('Failed to like post:', error);
      // 本地更新点赞状态
      setPosts(posts.map(post => 
        post.id === postId 
          ? { 
              ...post, 
              isLiked: !post.isLiked,
              likesCount: post.isLiked ? post.likesCount - 1 : post.likesCount + 1
            }
          : post
      ));
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return t('social.just.now');
    if (diffInHours < 24) return `${diffInHours}${t('social.hours.ago')}`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}${t('social.days.ago')}`;
    return date.toLocaleDateString('zh-CN');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* 发布新帖子 */}
      <div className="bg-white rounded-lg shadow p-6">
        <textarea
          className="w-full p-3 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500"
          rows={3}
          placeholder={t('social.new.post.placeholder')}
          value={newPost}
          onChange={(e) => setNewPost(e.target.value)}
        />
        <div className="mt-4 flex justify-between items-center">
          <div className="flex space-x-4">
            <button className="text-gray-500 hover:text-indigo-600">
              📷 {t('social.image')}
            </button>
            <button className="text-gray-500 hover:text-indigo-600">
              🎵 {t('social.audio')}
            </button>
            <button className="text-gray-500 hover:text-indigo-600">
              🎬 {t('social.video')}
            </button>
          </div>
          <button
            onClick={handleCreatePost}
            disabled={!newPost.trim() || posting}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {posting ? t('social.publishing') : t('social.publish')}
          </button>
        </div>
      </div>

      {/* 帖子列表 */}
      <div className="space-y-4">
        {posts.map(post => (
          <div key={post.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-start space-x-3">
              <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                {post.authorAvatar ? (
                  <img src={post.authorAvatar} alt="" className="w-10 h-10 rounded-full" />
                ) : (
                  <span className="text-indigo-600 font-semibold">
                    {post.authorName.charAt(0)}
                  </span>
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold text-gray-900">{post.authorName}</h3>
                  <span className="text-gray-500 text-sm">{formatDate(post.createdAt)}</span>
                </div>
                <p className="mt-2 text-gray-700">{post.content}</p>
                
                {post.mediaUrls && post.mediaUrls.length > 0 && (
                  <div className="mt-3 grid grid-cols-2 gap-2">
                    {post.mediaUrls.map((url, index) => (
                      <img
                        key={index}
                        src={url}
                        alt=""
                        className="rounded-md object-cover h-32 w-full"
                      />
                    ))}
                  </div>
                )}
                
                <div className="mt-4 flex items-center space-x-6 text-gray-500">
                  <button
                    onClick={() => handleLike(post.id)}
                    className={`flex items-center space-x-1 hover:text-red-500 ${
                      post.isLiked ? 'text-red-500' : ''
                    }`}
                  >
                    <span>{post.isLiked ? '❤️' : '🤍'}</span>
                    <span>{post.likesCount}</span>
                  </button>
                  <button className="flex items-center space-x-1 hover:text-blue-500">
                    <span>💬</span>
                    <span>{post.commentsCount}</span>
                  </button>
                  <button className="flex items-center space-x-1 hover:text-green-500">
                    <span>🔄</span>
                    <span>{post.sharesCount}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SocialFeed;