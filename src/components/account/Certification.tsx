import React, { useState, useMemo } from 'react';
import { 
  UserIcon, 
  MusicalNoteIcon, 
  DocumentCheckIcon,
  CloudArrowUpIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

const Certification: React.FC = () => {
  const [activeSection, setActiveSection] = useState('artist');
  const [uploadedFiles, setUploadedFiles] = useState<{[key: string]: File | null}>({});
  const { t } = useLanguage();

  const sections = useMemo(() => [
    { id: 'artist', name: t('certification.artist.info'), icon: MusicalNoteIcon },
    { id: 'user', name: t('certification.user.info'), icon: UserIcon },
    { id: 'qualification', name: t('certification.qualification.proof'), icon: DocumentCheckIcon }
  ], [t]);

  const handleFileUpload = (key: string, file: File) => {
    setUploadedFiles(prev => ({
      ...prev,
      [key]: file
    }));
  };

  const FileUploadArea: React.FC<{
    label: string;
    accept: string;
    fileKey: string;
    description: string;
  }> = ({ label, accept, fileKey, description }) => (
    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
      <input
        type="file"
        accept={accept}
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) handleFileUpload(fileKey, file);
        }}
        className="hidden"
        id={fileKey}
      />
      <label htmlFor={fileKey} className="cursor-pointer">
        <CloudArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-sm font-medium text-gray-900">{label}</p>
        <p className="text-xs text-gray-500 mt-1">{description}</p>
        {uploadedFiles[fileKey] && (
          <div className="mt-2 flex items-center justify-center text-green-600">
            <CheckCircleIcon className="h-4 w-4 mr-1" />
            <span className="text-xs">{uploadedFiles[fileKey]?.name}</span>
          </div>
        )}
      </label>
    </div>
  );

  const renderArtistInfo = () => (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-semibold text-blue-800 mb-2">{t('certification.artist.title')}</h4>
        <p className="text-blue-700 text-sm">
          {t('certification.artist.description')}
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.artist.stageName')} *</label>
          <input
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder={t('certification.artist.stageNamePlaceholder')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.idNumber')} *</label>
          <input
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder={t('certification.idNumberPlaceholder')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.phone')} *</label>
          <input
            type="tel"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder={t('certification.phonePlaceholder')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.email')} *</label>
          <input
            type="email"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder={t('certification.emailPlaceholder')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.musicType')} *</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
            <option value="">{t('certification.selectMusicType')}</option>
            <option value="pop">{t('certification.musicTypes.pop')}</option>
            <option value="rock">{t('certification.musicTypes.rock')}</option>
            <option value="jazz">{t('certification.musicTypes.jazz')}</option>
            <option value="classical">{t('certification.musicTypes.classical')}</option>
            <option value="electronic">{t('certification.musicTypes.electronic')}</option>
            <option value="folk">{t('certification.musicTypes.folk')}</option>
            <option value="hiphop">{t('certification.musicTypes.hiphop')}</option>
            <option value="country">{t('certification.musicTypes.country')}</option>
            <option value="other">{t('certification.musicTypes.other')}</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.experience')}</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
            <option value="">{t('certification.selectExperience')}</option>
            <option value="0-1">{t('certification.experienceYears.0-1')}</option>
            <option value="1-3">{t('certification.experienceYears.1-3')}</option>
            <option value="3-5">{t('certification.experienceYears.3-5')}</option>
            <option value="5-10">{t('certification.experienceYears.5-10')}</option>
            <option value="10+">{t('certification.experienceYears.10+')}</option>
          </select>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.bio')} *</label>
        <textarea
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          placeholder={t('certification.bioPlaceholder')}
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.majorWorks')}</label>
        <textarea
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          placeholder={t('certification.majorWorksPlaceholder')}
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.awards')}</label>
        <textarea
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          placeholder={t('certification.awardsPlaceholder')}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.personalPhoto')} *</label>
          <FileUploadArea
            label={t('certification.uploadPersonalPhoto')}
            accept="image/*"
            fileKey="artistPhoto"
            description={t('certification.photoFormat')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.idCardFront')} *</label>
          <FileUploadArea
            label={t('certification.uploadIdCardFront')}
            accept="image/*"
            fileKey="idCardFront"
            description={t('certification.idCardClear')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.idCardBack')} *</label>
          <FileUploadArea
            label={t('certification.uploadIdCardBack')}
            accept="image/*"
            fileKey="idCardBack"
            description={t('certification.idCardClear')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.workProof')}</label>
          <FileUploadArea
            label={t('certification.uploadWorkProof')}
            accept="image/*,.pdf"
            fileKey="workProof"
            description={t('certification.workProofDescription')}
          />
        </div>
      </div>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
          <div>
            <h4 className="font-semibold text-yellow-800">{t('certification.reviewNotice')}</h4>
            <ul className="text-yellow-700 text-sm mt-1 space-y-1">
              <li>• {t('certification.reviewRules.truthful')}</li>
              <li>• {t('certification.reviewRules.timeframe')}</li>
              <li>• {t('certification.reviewRules.contact')}</li>
              <li>• {t('certification.reviewRules.badge')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderUserInfo = () => (
    <div className="space-y-6">
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h4 className="font-semibold text-green-800 mb-2">{t('certification.user.title')}</h4>
        <p className="text-green-700 text-sm">
          {t('certification.user.description')}
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.realName')} *</label>
          <input
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder={t('certification.realNamePlaceholder')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.idNumber')} *</label>
          <input
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder={t('certification.idNumberPlaceholder')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.mobile')} *</label>
          <input
            type="tel"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder={t('certification.mobilePlaceholder')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.birthDate')} *</label>
          <input
            type="date"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.gender')} *</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
            <option value="">{t('certification.selectGender')}</option>
            <option value="male">{t('certification.genders.male')}</option>
            <option value="female">{t('certification.genders.female')}</option>
            <option value="other">{t('certification.genders.other')}</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.occupation')}</label>
          <input
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder={t('certification.occupationPlaceholder')}
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.address')}</label>
        <textarea
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          placeholder={t('certification.addressPlaceholder')}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.idCardFront')} *</label>
          <FileUploadArea
            label={t('certification.uploadIdCardFront')}
            accept="image/*"
            fileKey="userIdCardFront"
            description={t('certification.idCardClear')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.idCardBack')} *</label>
          <FileUploadArea
            label={t('certification.uploadIdCardBack')}
            accept="image/*"
            fileKey="userIdCardBack"
            description={t('certification.idCardClear')}
          />
        </div>
      </div>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <CheckCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
          <div>
            <h4 className="font-semibold text-blue-800">{t('certification.benefits')}</h4>
            <ul className="text-blue-700 text-sm mt-1 space-y-1">
              <li>• {t('certification.benefitsList.verification')}</li>
              <li>• {t('certification.benefitsList.security')}</li>
              <li>• {t('certification.benefitsList.features')}</li>
              <li>• {t('certification.benefitsList.events')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderQualification = () => (
    <div className="space-y-6">
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <h4 className="font-semibold text-purple-800 mb-2">{t('certification.qualification.title')}</h4>
        <p className="text-purple-700 text-sm">
          {t('certification.qualification.description')}
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.education')}</label>
          <FileUploadArea
            label={t('certification.uploadEducation')}
            accept="image/*,.pdf"
            fileKey="education"
            description={t('certification.educationDescription')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.certificate')}</label>
          <FileUploadArea
            label={t('certification.uploadCertificate')}
            accept="image/*,.pdf"
            fileKey="certificate"
            description={t('certification.certificateDescription')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.awardCertificate')}</label>
          <FileUploadArea
            label={t('certification.uploadAwardCertificate')}
            accept="image/*,.pdf"
            fileKey="awards"
            description={t('certification.awardDescription')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.copyrightProof')}</label>
          <FileUploadArea
            label={t('certification.uploadCopyrightProof')}
            accept="image/*,.pdf"
            fileKey="copyright"
            description={t('certification.copyrightDescription')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.mediaReport')}</label>
          <FileUploadArea
            label={t('certification.uploadMediaReport')}
            accept="image/*,.pdf"
            fileKey="media"
            description={t('certification.mediaDescription')}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.otherProof')}</label>
          <FileUploadArea
            label={t('certification.uploadOtherProof')}
            accept="image/*,.pdf"
            fileKey="other"
            description={t('certification.otherDescription')}
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{t('certification.additionalInfo')}</label>
        <textarea
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          placeholder={t('certification.additionalInfoPlaceholder')}
        />
      </div>
      
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-semibold text-gray-800 mb-2">{t('certification.notes')}</h4>
        <ul className="text-gray-700 text-sm space-y-1">
          <li>• {t('certification.notesList.authentic')}</li>
          <li>• {t('certification.notesList.formats')}</li>
          <li>• {t('certification.notesList.fileSize')}</li>
          <li>• {t('certification.notesList.clarity')}</li>
          <li>• {t('certification.notesList.quality')}</li>
        </ul>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'artist':
        return renderArtistInfo();
      case 'user':
        return renderUserInfo();
      case 'qualification':
        return renderQualification();
      default:
        return renderArtistInfo();
    }
  };

  return (
    <div className="space-y-6">
      {/* Top tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex space-x-8">
          {sections.map((section) => {
            const Icon = section.icon;
            const isActive = activeSection === section.id;
            
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  isActive
                    ? 'bg-indigo-50 text-indigo-700'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Icon className={`h-5 w-5 ${isActive ? 'text-indigo-500' : 'text-gray-400'}`} />
                <span className="font-medium">{section.name}</span>
              </button>
            );
          })}
        </div>
      </div>
      
      {/* Content area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {renderContent()}
        
        {/* Submit button */}
        <div className="flex justify-end mt-8 pt-6 border-t border-gray-200">
          <button className="px-8 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium">
            {t('certification.submit')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Certification;