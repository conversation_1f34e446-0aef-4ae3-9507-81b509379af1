import React, { useState, useRef } from 'react';

interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
}

interface MixerProps {
  tracks: Track[];
  selectedTrack: string | null;
  onUpdateTrack: (trackId: string, updates: Partial<Track>) => void;
}

const Mixer: React.FC<MixerProps> = ({ tracks, selectedTrack, onUpdateTrack }) => {
  const [masterVolume, setMasterVolume] = useState(0.85);
  const [eqValues, setEqValues] = useState<{ [key: string]: { [band: string]: number } }>({});
  const [sendValues, setSendValues] = useState<{ [key: string]: { [send: string]: number } }>({});
  const [clickedInserts, setClickedInserts] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<'horizontal' | 'vertical'>('horizontal');

  const handleFaderMouseDown = (e: React.MouseEvent, trackId: string, property: 'volume' | 'pan') => {
    e.preventDefault();
    
    const handleMouseMove = (moveEvent: MouseEvent) => {
      const rect = (e.target as HTMLElement).getBoundingClientRect();
      const y = moveEvent.clientY - rect.top;
      const height = rect.height;
      
      let value;
      if (property === 'volume') {
        value = Math.max(0, Math.min(1, 1 - (y / height)));
      } else {
        value = Math.max(-1, Math.min(1, 1 - (2 * y / height)));
      }
      
      onUpdateTrack(trackId, { [property]: value });
    };
    
    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };
  
  const handleKnobMouseDown = (e: React.MouseEvent, trackId: string, property: 'pan') => {
    e.preventDefault();
    const startY = e.clientY;
    const startValue = tracks.find(t => t.id === trackId)?.[property] || 0;
    
    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaY = startY - moveEvent.clientY;
      const sensitivity = 0.01;
      const newValue = Math.max(-1, Math.min(1, startValue + deltaY * sensitivity));
      
      onUpdateTrack(trackId, { [property]: newValue });
    };
    
    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };
  
  const handleEQKnobMouseDown = (e: React.MouseEvent, trackId: string, band: string) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`EQ ${band} knob clicked for track ${trackId}`);
    
    const startY = e.clientY;
    const startValue = eqValues[trackId]?.[band] || 0;
    
    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaY = startY - moveEvent.clientY;
      const sensitivity = 0.02;
      const newValue = Math.max(-1, Math.min(1, startValue + deltaY * sensitivity));
      
      setEqValues(prev => ({
        ...prev,
        [trackId]: { ...prev[trackId], [band]: newValue }
      }));
      console.log(`EQ ${band} value: ${newValue}`);
    };
    
    const handleMouseUp = () => {
      console.log(`EQ ${band} mouse up`);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };
  
  const handleSendKnobMouseDown = (e: React.MouseEvent, trackId: string, send: string) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`Send ${send} knob clicked for track ${trackId}`);
    
    const startY = e.clientY;
    const startValue = sendValues[trackId]?.[send] || 0;
    
    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaY = startY - moveEvent.clientY;
      const sensitivity = 0.02;
      const newValue = Math.max(0, Math.min(1, startValue + deltaY * sensitivity));
      
      setSendValues(prev => ({
        ...prev,
        [trackId]: { ...prev[trackId], [send]: newValue }
      }));
      console.log(`Send ${send} value: ${newValue}`);
    };
    
    const handleMouseUp = () => {
      console.log(`Send ${send} mouse up`);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };
  
  const handleMasterFaderMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    
    const handleMouseMove = (moveEvent: MouseEvent) => {
      const rect = (e.target as HTMLElement).getBoundingClientRect();
      const y = moveEvent.clientY - rect.top;
      const height = rect.height;
      const value = Math.max(0, Math.min(1, 1 - (y / height)));
      
      setMasterVolume(value);
    };
    
    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const MixerChannel: React.FC<{ track: Track }> = ({ track }) => {
    const volumeFaderRef = useRef<HTMLDivElement>(null);
    const panKnobRef = useRef<HTMLDivElement>(null);

    const volumePercent = track.volume * 100;

    return (
      <div className="mixer-channel-protools">
        <div className="mixer-channel-name text-xs font-bold mb-3">
          {track.name}
        </div>
        
        {/* Input Section */}
        <div className="mb-3 bg-gray-800 bg-opacity-50 rounded p-2">
          <div className="text-xs text-blue-300 mb-1 text-center font-semibold">INPUT</div>
          <select 
            className="w-full text-xs bg-gray-700 text-white p-1 rounded border border-gray-600 hover:border-blue-400 focus:border-blue-500 focus:outline-none transition-colors"
            onChange={(e) => {
              console.log(`Input changed to ${e.target.value} for track ${track.id}`);
            }}
          >
            <option>MIDI In</option>
            <option>Audio In 1</option>
            <option>Audio In 2</option>
          </select>
        </div>

        {/* Inserts Section */}
        <div className="mb-3 bg-gray-800 bg-opacity-50 rounded p-2">
          <div className="text-xs text-blue-300 mb-1 text-center font-semibold">INSERTS</div>
          <div className="space-y-1">
            {[1, 2].map((slot) => (
              <div 
                key={slot} 
                className={`text-xs p-2 rounded text-center border cursor-pointer transition-all duration-200 select-none ${
                  clickedInserts.has(`${track.id}-insert-${slot}`) 
                    ? 'bg-green-600 text-white border-green-400' 
                    : 'bg-gray-700 text-gray-400 border-gray-600 hover:border-green-400 hover:text-gray-300 hover:bg-gray-600'
                }`}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log(`Insert ${slot} clicked for track ${track.id}`);
                  
                  // 添加视觉反馈使用CSS类
                  const insertId = `${track.id}-insert-${slot}`;
                  setClickedInserts(prev => {
                    const newSet = new Set(prev);
                    newSet.add(insertId);
                    return newSet;
                  });
                  
                  setTimeout(() => {
                    setClickedInserts(prev => {
                      const newSet = new Set(prev);
                      newSet.delete(insertId);
                      return newSet;
                    });
                  }, 200);
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  console.log(`Insert ${slot} mouse down`);
                }}
              >
                Insert {slot}
              </div>
            ))}
          </div>
        </div>

        {/* EQ Section */}
        <div className="mb-3 bg-gray-800 bg-opacity-50 rounded p-2">
          <div className="text-xs text-blue-300 mb-1 text-center font-semibold">EQ</div>
          <div className="grid grid-cols-2 gap-2">
            {['HI', 'LO'].map((band) => (
              <div key={band} className="text-center">
                <div className="text-xs text-gray-400 mb-1 font-medium">{band}</div>
                <div 
                  className="mixer-knob mx-auto relative cursor-pointer w-6 h-6 select-none"
                  onMouseDown={(e) => {
                    console.log(`EQ knob ${band} mouse down event triggered`);
                    handleEQKnobMouseDown(e, track.id, band);
                  }}
                  onMouseEnter={() => console.log(`EQ ${band} hover`)}
                  style={{ userSelect: 'none' }}
                >
                  <div 
                    className="absolute w-0.5 h-2 bg-green-400 top-0.5 left-1/2 transform -translate-x-1/2 rounded-full transition-transform pointer-events-none"
                    style={{
                      transform: `translateX(-50%) rotate(${(eqValues[track.id]?.[band] || 0) * 140}deg)`
                    }}
                  />
                  {/* 添加knob的圆形指示器 */}
                  <div className="absolute inset-0 border border-gray-500 rounded-full pointer-events-none" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Send Section */}
        <div className="mb-3 bg-gray-800 bg-opacity-50 rounded p-2">
          <div className="text-xs text-blue-300 mb-1 text-center font-semibold">SENDS</div>
          <div className="grid grid-cols-2 gap-2">
            {['A', 'B'].map((send) => (
              <div key={send} className="text-center">
                <div className="text-xs text-gray-400 mb-1 font-medium">{send}</div>
                <div 
                  className="mixer-knob mx-auto relative cursor-pointer w-5 h-5 select-none"
                  onMouseDown={(e) => {
                    console.log(`Send knob ${send} mouse down event triggered`);
                    handleSendKnobMouseDown(e, track.id, send);
                  }}
                  onMouseEnter={() => console.log(`Send ${send} hover`)}
                  style={{ userSelect: 'none' }}
                >
                  <div 
                    className="absolute w-0.5 h-1.5 bg-blue-400 top-0.5 left-1/2 transform -translate-x-1/2 rounded-full transition-transform pointer-events-none"
                    style={{
                      transform: `translateX(-50%) rotate(${(sendValues[track.id]?.[send] || 0) * 140}deg)`
                    }}
                  />
                  {/* 添加knob的圆形指示器 */}
                  <div className="absolute inset-0 border border-gray-500 rounded-full pointer-events-none" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Pan Section */}
        <div className="mb-3 bg-gray-800 bg-opacity-50 rounded p-2">
          <div className="text-xs text-blue-300 mb-1 text-center font-semibold">PAN</div>
          <div className="text-xs text-center mb-2 font-mono bg-gray-700 rounded px-2 py-1">
            {track.pan === 0 ? 'CENTER' : track.pan < 0 ? `L${Math.abs(Math.round(track.pan * 100))}` : `R${Math.round(track.pan * 100)}`}
          </div>
          
          <div
            ref={panKnobRef}
            className="mixer-knob mx-auto relative cursor-pointer w-8 h-8"
            onMouseDown={(e) => handleKnobMouseDown(e, track.id, 'pan')}
          >
            {/* Knob indicator */}
            <div
              className="absolute w-1 h-3 bg-white top-1 left-1/2 transform -translate-x-1/2 origin-bottom rounded-full"
              style={{
                transform: `translateX(-50%) rotate(${track.pan * 140}deg)`
              }}
            />
          </div>
        </div>

        {/* Channel Controls */}
        <div className="mixer-controls mb-3">
          <button
            className={`w-full px-2 py-1 text-xs rounded mb-1 font-bold transition-all duration-200 ${
              track.armed 
                ? 'bg-red-600 text-white shadow-lg shadow-red-600/30 border-2 border-red-400' 
                : 'bg-gray-700 text-gray-300 border border-gray-600 hover:bg-gray-600 hover:border-red-400'
            }`}
            onClick={(e) => {
              e.preventDefault();
              onUpdateTrack(track.id, { armed: !track.armed });
            }}
          >
            REC
          </button>
          
          <button
            className={`w-full px-2 py-1 text-xs rounded mb-1 font-bold transition-all duration-200 ${
              track.solo 
                ? 'bg-yellow-500 text-black shadow-lg shadow-yellow-500/30 border-2 border-yellow-300' 
                : 'bg-gray-700 text-gray-300 border border-gray-600 hover:bg-gray-600 hover:border-yellow-400'
            }`}
            onClick={(e) => {
              e.preventDefault();
              onUpdateTrack(track.id, { solo: !track.solo });
            }}
          >
            SOLO
          </button>
          
          <button
            className={`w-full px-2 py-1 text-xs rounded font-bold transition-all duration-200 ${
              track.muted 
                ? 'bg-red-700 text-white shadow-lg shadow-red-700/30 border-2 border-red-500' 
                : 'bg-gray-700 text-gray-300 border border-gray-600 hover:bg-gray-600 hover:border-red-400'
            }`}
            onClick={(e) => {
              e.preventDefault();
              onUpdateTrack(track.id, { muted: !track.muted });
            }}
          >
            MUTE
          </button>
        </div>

        {/* Volume Fader */}
        <div className="mb-3 bg-gray-800 bg-opacity-50 rounded p-2">
          <div className="text-xs text-blue-300 mb-1 text-center font-semibold">VOLUME</div>
          <div className="text-xs text-center mb-2 font-mono bg-gray-700 rounded px-2 py-1">
            {Math.round(volumePercent)}%
          </div>
          
          <div className="flex justify-center items-start gap-2">
            <div
              ref={volumeFaderRef}
              className="mixer-fader-protools relative cursor-pointer"
              onMouseDown={(e) => handleFaderMouseDown(e, track.id, 'volume')}
            >
              <div
                className="mixer-fader-handle-protools"
                style={{
                  top: `${(1 - track.volume) * 85}%`
                }}
              />
            </div>
            
            {/* Volume scale marks */}
            <div className="h-32 relative text-xs">
              {[0, -20, -60].map((db, i) => {
                const position = i * 40;
                return (
                  <div
                    key={i}
                    className="absolute text-xs text-gray-400"
                    style={{ top: `${position}%`, fontSize: '9px' }}
                  >
                    {db}
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Level Meter */}
        <div className="mt-2 bg-gray-800 bg-opacity-50 rounded p-2">
          <div className="text-xs text-blue-300 mb-1 text-center font-semibold">LEVEL</div>
          <div className="flex justify-center gap-1">
            <div className="relative">
              <div className="w-3 h-20 bg-gray-900 rounded-sm border border-gray-700">
                <div
                  className="absolute bottom-0 w-full bg-gradient-to-t from-green-400 via-yellow-400 to-red-500 rounded-sm transition-all duration-100"
                  style={{ height: '0%' }}
                />
              </div>
              <div className="text-xs text-gray-500 text-center mt-1">L</div>
            </div>
            <div className="relative">
              <div className="w-3 h-20 bg-gray-900 rounded-sm border border-gray-700">
                <div
                  className="absolute bottom-0 w-full bg-gradient-to-t from-green-400 via-yellow-400 to-red-500 rounded-sm transition-all duration-100"
                  style={{ height: '0%' }}
                />
              </div>
              <div className="text-xs text-gray-500 text-center mt-1">R</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderHorizontalMixer = () => (
    <div className="mixer-container-horizontal">
      <div className="mixer-header-horizontal">
        <div className="flex items-center justify-between p-3">
          <h3 className="text-sm font-bold text-blue-400">Mixer (Horizontal)</h3>
          <button
            className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500"
            onClick={() => setViewMode('vertical')}
          >
            Switch to Vertical
          </button>
        </div>
      </div>
      
      <div className="mixer-channels-horizontal overflow-x-auto">
        <div className="flex gap-2 p-4 min-w-max">
          {tracks.map((track) => (
            <div key={track.id} className={`mixer-channel-horizontal ${selectedTrack === track.id ? 'selected' : ''}`}>
              <div className="mixer-channel-name text-center mb-2 font-bold text-xs truncate w-24">
                {track.name}
              </div>
              
              {/* Horizontal Controls */}
              <div className="flex flex-col gap-2 bg-gray-800 rounded p-2 w-24">
                {/* Track Type Indicator */}
                <div className={`text-xs text-center px-2 py-1 rounded ${
                  track.type === 'midi' ? 'bg-blue-600' : 'bg-green-600'
                } text-white`}>
                  {track.type.toUpperCase()}
                </div>
                
                {/* Record/Solo/Mute Buttons */}
                <div className="grid grid-cols-3 gap-1">
                  <button
                    className={`text-xs px-1 py-1 rounded ${
                      track.armed ? 'bg-red-600 text-white' : 'bg-gray-700 text-gray-300'
                    }`}
                    onClick={() => onUpdateTrack(track.id, { armed: !track.armed })}
                  >
                    R
                  </button>
                  <button
                    className={`text-xs px-1 py-1 rounded ${
                      track.solo ? 'bg-yellow-500 text-black' : 'bg-gray-700 text-gray-300'
                    }`}
                    onClick={() => onUpdateTrack(track.id, { solo: !track.solo })}
                  >
                    S
                  </button>
                  <button
                    className={`text-xs px-1 py-1 rounded ${
                      track.muted ? 'bg-red-700 text-white' : 'bg-gray-700 text-gray-300'
                    }`}
                    onClick={() => onUpdateTrack(track.id, { muted: !track.muted })}
                  >
                    M
                  </button>
                </div>
                
                {/* Horizontal Volume Slider */}
                <div className="mb-2">
                  <label className="text-xs text-gray-400 block mb-1">Vol</label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={track.volume * 100}
                    onChange={(e) => onUpdateTrack(track.id, { volume: parseInt(e.target.value) / 100 })}
                    className="horizontal-slider volume-slider w-full"
                  />
                  <div className="text-xs text-center text-gray-400">
                    {Math.round(track.volume * 100)}%
                  </div>
                </div>
                
                {/* Horizontal Pan Slider */}
                <div className="mb-2">
                  <label className="text-xs text-gray-400 block mb-1">Pan</label>
                  <input
                    type="range"
                    min="-100"
                    max="100"
                    value={track.pan * 100}
                    onChange={(e) => onUpdateTrack(track.id, { pan: parseInt(e.target.value) / 100 })}
                    className="horizontal-slider pan-slider w-full"
                  />
                  <div className="text-xs text-center text-gray-400">
                    {track.pan > 0 ? `R${Math.round(track.pan * 100)}` : 
                     track.pan < 0 ? `L${Math.round(Math.abs(track.pan) * 100)}` : 'C'}
                  </div>
                </div>
                
                {/* Level Meter */}
                <div className="flex justify-center gap-1">
                  <div className="w-2 h-16 bg-gray-900 rounded-sm relative">
                    <div
                      className="absolute bottom-0 w-full bg-gradient-to-t from-green-400 via-yellow-400 to-red-500 rounded-sm"
                      style={{ height: '0%' }}
                    />
                  </div>
                  <div className="w-2 h-16 bg-gray-900 rounded-sm relative">
                    <div
                      className="absolute bottom-0 w-full bg-gradient-to-t from-green-400 via-yellow-400 to-red-500 rounded-sm"
                      style={{ height: '0%' }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {/* Master Channel Horizontal */}
          <div className="mixer-channel-horizontal master-channel-horizontal">
            <div className="mixer-channel-name text-center mb-2 font-bold text-xs text-blue-400 w-24">
              MASTER
            </div>
            
            <div className="flex flex-col gap-2 bg-gray-800 rounded p-2 w-24">
              <div className="text-xs text-center px-2 py-1 rounded bg-blue-700 text-white">
                MAIN
              </div>
              
              {/* Master Volume */}
              <div className="mb-2">
                <label className="text-xs text-gray-400 block mb-1">Master</label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={masterVolume * 100}
                  onChange={(e) => setMasterVolume(parseInt(e.target.value) / 100)}
                  className="horizontal-slider volume-slider w-full"
                />
                <div className="text-xs text-center text-gray-400">
                  {Math.round(masterVolume * 100)}%
                </div>
              </div>
              
              {/* Master Level Meters */}
              <div className="flex justify-center gap-1">
                <div className="w-3 h-20 bg-gray-900 rounded-sm relative">
                  <div className="absolute bottom-0 w-full bg-gradient-to-t from-green-500 via-yellow-500 to-red-500" 
                       style={{ height: '0%' }} />
                </div>
                <div className="w-3 h-20 bg-gray-900 rounded-sm relative">
                  <div className="absolute bottom-0 w-full bg-gradient-to-t from-green-500 via-yellow-500 to-red-500" 
                       style={{ height: '0%' }} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className={viewMode === 'horizontal' ? 'mixer-horizontal-layout' : 'mixer-container-protools'}>
      {viewMode === 'horizontal' ? renderHorizontalMixer() : (
        <>
          <div className="mixer-header">
            <div className="flex items-center justify-between p-3">
              <h3 className="text-sm font-bold text-blue-400">Mixer (Vertical)</h3>
              <button
                className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500"
                onClick={() => setViewMode('horizontal')}
              >
                Switch to Horizontal
              </button>
            </div>
          </div>
          
          <div className="mixer-channels-vertical">
            {tracks.map((track) => (
              <MixerChannel key={track.id} track={track} />
            ))}
            
            {/* Master Channel */}
            <div className="mixer-channel-protools master-channel">
              <div className="mixer-channel-name text-blue-400 font-bold">
                MASTER
              </div>
          
          {/* Master EQ */}
          <div className="mb-4">
            <div className="text-xs text-gray-400 mb-2 text-center">MASTER EQ</div>
            <div className="grid grid-cols-3 gap-1">
              {['HIGH', 'MID', 'LOW'].map((band) => (
                <div key={band} className="text-center">
                  <div className="text-xs text-gray-500 mb-1">{band}</div>
                  <div 
                    className="mixer-knob mx-auto relative cursor-pointer w-8 h-8 select-none"
                    onMouseDown={(e) => {
                      console.log(`Master EQ knob ${band} mouse down event triggered`);
                      handleEQKnobMouseDown(e, 'master', band);
                    }}
                    onMouseEnter={() => console.log(`Master EQ ${band} hover`)}
                    style={{ userSelect: 'none' }}
                  >
                    <div 
                      className="absolute w-0.5 h-3 bg-blue-400 top-1 left-1/2 transform -translate-x-1/2 transition-transform pointer-events-none"
                      style={{
                        transform: `translateX(-50%) rotate(${(eqValues['master']?.[band] || 0) * 140}deg)`
                      }}
                    />
                    {/* 添加knob的圆形指示器 */}
                    <div className="absolute inset-0 border border-gray-500 rounded-full pointer-events-none" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Master Volume */}
          <div className="mb-4">
            <div className="text-xs text-gray-400 mb-1 text-center">MASTER</div>
            <div className="text-xs text-center mb-2 font-mono bg-gray-700 rounded px-2 py-1">
              {Math.round(masterVolume * 100)}%
            </div>
            
            <div 
              className="mixer-fader-protools mx-auto relative cursor-pointer"
              onMouseDown={handleMasterFaderMouseDown}
            >
              <div 
                className="mixer-fader-handle-protools" 
                style={{ top: `${(1 - masterVolume) * 85}%` }} 
              />
              
              {/* Volume scale marks */}
              <div className="absolute -right-6 top-0 h-full">
                {[0, -20, -40, -60].map((db, i) => (
                  <div
                    key={i}
                    className="absolute text-xs text-gray-500"
                    style={{ top: `${i * 25}%`, fontSize: '9px' }}
                  >
                    {db}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Master Level Meters */}
          <div className="mt-4">
            <div className="text-xs text-gray-400 mb-1 text-center">MASTER OUT</div>
            <div className="flex justify-center gap-1">
              <div className="w-3 h-20 bg-gray-900 rounded-sm border border-gray-700 relative">
                <div className="text-xs text-gray-500 absolute -left-6 top-0">L</div>
                <div 
                  className="absolute bottom-0 w-full bg-gradient-to-t from-green-400 via-yellow-400 to-red-500 rounded-sm transition-all duration-100" 
                  style={{ height: '0%' }}
                />
              </div>
              <div className="w-3 h-20 bg-gray-900 rounded-sm border border-gray-700 relative">
                <div className="text-xs text-gray-500 absolute -right-6 top-0">R</div>
                <div 
                  className="absolute bottom-0 w-full bg-gradient-to-t from-green-400 via-yellow-400 to-red-500 rounded-sm transition-all duration-100" 
                  style={{ height: '0%' }}
                />
              </div>
            </div>
            </div>
          </div>
        </div>
        
        </>
      )}
    </div>
  );
};

export default Mixer;