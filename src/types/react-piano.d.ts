declare module 'react-piano' {
  export interface KeyboardShortcutsConfig {
    firstNote: number;
    lastNote: number;
    keyboardConfig: any;
  }

  export interface NoteRange {
    first: number;
    last: number;
  }

  export interface PianoProps {
    noteRange: NoteRange;
    playNote: (midiNumber: number) => void;
    stopNote: (midiNumber: number) => void;
    width?: number;
    keyboardShortcuts?: KeyboardShortcutsConfig;
    renderNoteLabel?: (props: {
      keyboardShortcut: string;
      midiNumber: number;
      isActive: boolean;
      isAccidental: boolean;
    }) => React.ReactNode;
  }

  export const Piano: React.FC<PianoProps>;

  export const KeyboardShortcuts: {
    create: (config: {
      firstNote: number;
      lastNote: number;
      keyboardConfig: any;
    }) => KeyboardShortcutsConfig;
    HOME_ROW: any;
  };

  export const MidiNumbers: {
    fromNote: (note: string) => number;
  };
}