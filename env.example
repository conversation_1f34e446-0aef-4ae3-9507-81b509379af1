# API Configuration
REACT_APP_API_URL=http://localhost:8080/api/v1

# Environment
NODE_ENV=development

# WebSocket Configuration
REACT_APP_WS_URL=ws://localhost:8080/ws

# Media Configuration
REACT_APP_RTMP_SERVER=rtmp://live.example.com/live
REACT_APP_WEBRTC_SERVER=wss://webrtc.example.com

# File Upload Configuration
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,audio/mpeg,audio/wav