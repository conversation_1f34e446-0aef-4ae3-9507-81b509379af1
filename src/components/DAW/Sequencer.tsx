import React, { useRef, useState } from 'react';

interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
}

interface Clip {
  id: string;
  trackId: string;
  start: number;
  duration: number;
  name: string;
  color: string;
  notes?: Array<{
    note: string;
    time: number;
    duration: number;
    velocity: number;
  }>;
}

interface SequencerProps {
  tracks: Track[];
  clips: Clip[];
  selectedTrack: string | null;
  isPlaying: boolean;
  currentTime: number;
  onUpdateTrack: (trackId: string, updates: Partial<Track>) => void;
  onUpdateClips: (clips: Clip[]) => void;
}

const Sequencer: React.FC<SequencerProps> = ({
  tracks,
  clips,
  selectedTrack,
  isPlaying,
  currentTime,
  onUpdateTrack,
  onUpdateClips
}) => {
  const [zoom, setZoom] = useState(1);
  const [, setScrollTop] = useState(0);
  const [dragState, setDragState] = useState<{
    type: 'move' | 'resize-left' | 'resize-right' | null;
    clipId: string | null;
    startX: number;
    startClipPosition: number;
    startClipDuration: number;
  }>({ type: null, clipId: null, startX: 0, startClipPosition: 0, startClipDuration: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const tracksScrollRef = useRef<HTMLDivElement>(null);

  const pixelsPerSecond = 50 * zoom;
  const trackHeight = 54; // 轨道高度改为80的2/3
  const timelineHeight = 40;
  // const sequencerHeaderHeight = 80; // header区域高度 - 暂时不使用
  // 固定可见区域高度，确保能显示更多轨道
  const fixedContainerHeight = 350; // 固定高度
  const maxVisibleTracks = Math.floor(fixedContainerHeight / trackHeight); // 可见轨道数（约6个）
  const totalTracksHeight = tracks.length * trackHeight;
  const needsScrolling = totalTracksHeight > fixedContainerHeight;
  
  // Snap to grid (quarter note intervals)
  const snapToGrid = (time: number, snapSize: number = 0.25) => {
    return Math.round(time / snapSize) * snapSize;
  };

  // Handle mouse events for clip dragging and resizing
  const handleMouseDown = (e: React.MouseEvent, clipId: string, type: 'move' | 'resize-left' | 'resize-right') => {
    e.stopPropagation();
    e.preventDefault();
    
    const clip = clips.find(c => c.id === clipId);
    if (!clip) return;

    setDragState({
      type,
      clipId,
      startX: e.clientX,
      startClipPosition: clip.start,
      startClipDuration: clip.duration
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!dragState.type || !dragState.clipId) return;

    const deltaX = e.clientX - dragState.startX;
    const deltaTime = deltaX / pixelsPerSecond;

    const updatedClips = clips.map(clip => {
      if (clip.id !== dragState.clipId) return clip;

      const track = tracks.find(t => t.id === clip.trackId);
      let updatedClip = { ...clip };

      switch (dragState.type) {
        case 'move':
          const newStart = snapToGrid(Math.max(0, dragState.startClipPosition + deltaTime));
          updatedClip = {
            ...clip,
            start: newStart
          };
          break;
        case 'resize-left':
          const adjustedStart = snapToGrid(Math.max(0, dragState.startClipPosition + deltaTime));
          const adjustedDuration = snapToGrid(Math.max(0.25, dragState.startClipDuration - (adjustedStart - dragState.startClipPosition)));
          updatedClip = {
            ...clip,
            start: adjustedStart,
            duration: adjustedDuration,
            // 重新生成音符以匹配新的长度
            notes: track ? generateNotesForClip(track.name, adjustedDuration, clip.id) : clip.notes
          };
          break;
        case 'resize-right':
          const newDuration = snapToGrid(Math.max(0.25, dragState.startClipDuration + deltaTime));
          updatedClip = {
            ...clip,
            duration: newDuration,
            // 重新生成音符以匹配新的长度
            notes: track ? generateNotesForClip(track.name, newDuration, clip.id) : clip.notes
          };
          break;
        default:
          return clip;
      }

      return updatedClip;
    });

    onUpdateClips(updatedClips);
  };

  const handleMouseUp = () => {
    setDragState({ type: null, clipId: null, startX: 0, startClipPosition: 0, startClipDuration: 0 });
  };

  // Add global mouse event listeners
  React.useEffect(() => {
    if (dragState.type) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [dragState]);

  const deleteClip = (clipId: string) => {
    const updatedClips = clips.filter(clip => clip.id !== clipId);
    onUpdateClips(updatedClips);
  };

  // 根据乐器类型和clip长度生成动态音符
  const generateNotesForClip = (trackName: string, clipDuration: number, clipId: string) => {
    const beatsInClip = clipDuration / 0.5; // 假设每0.5秒一拍
    const notes = [];
    
    // 添加随机种子基于clipId，确保相同clip每次生成相同的音符
    const seed = clipId.split('-')[1] || '0';
    const randomSeed = parseInt(seed) % 1000;
    
    if (trackName.includes('钢琴') || trackName.includes('Piano')) {
      // 钢琴：和弦进行
      const chords = [
        ['C4', 'E4', 'G4'], ['F4', 'A4', 'C5'], ['G4', 'B4', 'D5'], ['A4', 'C5', 'E5']
      ];
      for (let beat = 0; beat < beatsInClip; beat += 2) {
        if (beat * 0.5 >= clipDuration) break;
        const chordIndex = (Math.floor(beat / 2) + randomSeed) % chords.length;
        const chord = chords[chordIndex];
        chord.forEach((note, idx) => {
          if (beat * 0.5 + idx * 0.1 < clipDuration) {
            notes.push({
              note: note,
              time: beat * 0.5 + idx * 0.1,
              duration: Math.min(0.8, clipDuration - beat * 0.5),
              velocity: 0.6 + (idx * 0.1)
            });
          }
        });
      }
    } else if (trackName.includes('吉他') || trackName.includes('Guitar')) {
      // 吉他：琶音模式
      const scales = ['E3', 'G3', 'A3', 'B3', 'D4', 'E4', 'G4', 'A4'];
      for (let beat = 0; beat < beatsInClip * 2; beat++) {
        const time = beat * 0.25;
        if (time >= clipDuration) break;
        const noteIndex = (beat + randomSeed) % scales.length;
        notes.push({
          note: scales[noteIndex],
          time: time,
          duration: 0.2,
          velocity: 0.7 + (Math.sin(beat * 0.5) * 0.2)
        });
      }
    } else if (trackName.includes('贝斯') || trackName.includes('Bass')) {
      // 贝斯：低音线
      const bassNotes = ['E2', 'G2', 'A2', 'B2', 'C3', 'D3'];
      for (let beat = 0; beat < beatsInClip; beat++) {
        const time = beat * 0.5;
        if (time >= clipDuration) break;
        const noteIndex = (beat + randomSeed) % bassNotes.length;
        notes.push({
          note: bassNotes[noteIndex],
          time: time,
          duration: 0.4,
          velocity: 0.8 + (beat % 2 === 0 ? 0.1 : -0.1)
        });
      }
    } else if (trackName.includes('鼓') || trackName.includes('Drum')) {
      // 鼓：节拍模式
      const drumPattern = [
        { note: 'C2', beats: [0, 2] }, // 底鼓
        { note: 'D2', beats: [1, 3] }, // 军鼓
        { note: 'F#2', beats: [0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5] } // 踩镲
      ];
      
      for (let measure = 0; measure < Math.ceil(beatsInClip / 4); measure++) {
        drumPattern.forEach(drum => {
          drum.beats.forEach(beat => {
            const time = (measure * 4 + beat) * 0.5;
            if (time < clipDuration) {
              notes.push({
                note: drum.note,
                time: time,
                duration: 0.1,
                velocity: drum.note === 'C2' ? 0.9 : drum.note === 'D2' ? 0.8 : 0.6
              });
            }
          });
        });
      }
    } else {
      // 合成器：旋律线
      const melodyNotes = ['C4', 'D4', 'E4', 'F4', 'G4', 'A4', 'B4', 'C5'];
      for (let beat = 0; beat < beatsInClip; beat++) {
        const time = beat * 0.5;
        if (time >= clipDuration) break;
        const noteIndex = (beat * 2 + randomSeed) % melodyNotes.length;
        notes.push({
          note: melodyNotes[noteIndex],
          time: time,
          duration: 0.4,
          velocity: 0.6 + Math.sin(beat * 0.3) * 0.2
        });
      }
    }
    
    return notes;
  };

  const addClip = (trackId: string, startTime: number) => {
    const track = tracks.find(t => t.id === trackId);
    const clipDuration = 2;
    const clipId = `clip-${Date.now()}`;

    const newClip: Clip = {
      id: clipId,
      trackId,
      start: startTime,
      duration: clipDuration,
      name: `${track?.name || 'New'} Clip`,
      color: `hsl(${Math.random() * 360}, 70%, 60%)`, // Random vibrant color
      notes: track ? generateNotesForClip(track.name, clipDuration, clipId) : []
    };
    console.log('Created clip with dynamic notes:', newClip);
    onUpdateClips([...clips, newClip]);
  };

  const renderTimelineMarkers = () => {
    const canvasWidth = 3000; // Extended timeline width
    const canvasHeight = timelineHeight;
    
    return (
      <svg width={canvasWidth} height={canvasHeight} className="absolute top-0 left-0">
        {/* Timeline background */}
        <rect width={canvasWidth} height={canvasHeight} fill="#2a2a2a" />
        
        {/* Major markers (seconds) */}
        {Array.from({ length: 60 }, (_, i) => {
          const x = i * pixelsPerSecond;
          return (
            <g key={`major-${i}`}>
              <line
                x1={x}
                y1={0}
                x2={x}
                y2={canvasHeight}
                stroke="#555"
                strokeWidth="1"
              />
              <text
                x={x + 2}
                y={12}
                fill="#aaa"
                fontSize="10"
                fontFamily="monospace"
              >
                {Math.floor(i / 60)}:{(i % 60).toString().padStart(2, '0')}
              </text>
            </g>
          );
        })}
        
        {/* Minor markers (quarter seconds) */}
        {Array.from({ length: 240 }, (_, i) => {
          const x = (i * pixelsPerSecond) / 4;
          return (
            <line
              key={`minor-${i}`}
              x1={x}
              y1={canvasHeight - 5}
              x2={x}
              y2={canvasHeight}
              stroke="#444"
              strokeWidth="0.5"
            />
          );
        })}
      </svg>
    );
  };

  const renderPlayhead = () => {
    const x = currentTime * pixelsPerSecond;
    
    return (
      <div
        className="absolute z-20 pointer-events-none"
        style={{ 
          left: x - 1, 
          top: 0,
          width: '2px',
          height: '100%',
          background: '#ff4444',
          boxShadow: '0 0 4px rgba(255, 68, 68, 0.6)'
        }}
      >
        {/* Playhead triangle */}
        <div
          style={{
            position: 'absolute',
            top: '-8px',
            left: '-6px',
            width: 0,
            height: 0,
            borderLeft: '6px solid transparent',
            borderRight: '6px solid transparent',
            borderBottom: '8px solid #ff4444',
            filter: 'drop-shadow(0 0 2px rgba(255, 68, 68, 0.8))'
          }}
        />
      </div>
    );
  };

  const renderClips = (trackId: string) => {
    const trackClips = clips.filter(clip => clip.trackId === trackId);
    const trackWidth = 3000;
    
    return (
      <svg
        width={trackWidth}
        height={trackHeight}
        className="absolute top-0 left-0"
        style={{ pointerEvents: 'none' }}
      >
        {trackClips.map(clip => {
          const x = clip.start * pixelsPerSecond;
          const width = clip.duration * pixelsPerSecond;
          const y = 5;
          const height = trackHeight - 10;
          
          return (
            <g key={clip.id}>
              {/* Clip background with gradient */}
              <defs>
                <linearGradient id={`grad-${clip.id}`} x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style={{ stopColor: clip.color, stopOpacity: 0.7 }} />
                  <stop offset="100%" style={{ stopColor: clip.color, stopOpacity: 0.3 }} />
                </linearGradient>
              </defs>
              <rect
                x={x}
                y={y}
                width={width}
                height={height}
                fill={`url(#grad-${clip.id})`}
                stroke={clip.color}
                strokeWidth="1"
                rx="3"
                className={`cursor-move hover:opacity-80 ${dragState.clipId === clip.id ? 'opacity-80' : ''}`}
                style={{ pointerEvents: 'auto' }}
                onClick={(e) => e.stopPropagation()}
                onDoubleClick={(e) => {
                  e.stopPropagation();
                  deleteClip(clip.id);
                }}
                onMouseDown={(e) => handleMouseDown(e, clip.id, 'move')}
                onContextMenu={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (window.confirm(`Delete clip "${clip.name}"?`)) {
                    deleteClip(clip.id);
                  }
                }}
              />
              
              {/* Realistic waveform visualization */}
              {(() => {
                const waveformPoints = [];
                const samples = Math.floor(width / 2); // One sample every 2 pixels
                const centerY = y + height / 2;
                const maxAmplitude = (height - 10) / 2;
                
                // Generate realistic waveform based on clip type and duration
                for (let i = 0; i < samples; i++) {
                  const progress = i / samples;
                  const time = progress * clip.duration;
                  
                  // Different waveform patterns based on clip name/type
                  let amplitude = 0;
                  
                  if (clip.name.includes('钢琴') || clip.name.includes('Piano')) {
                    // Piano-like waveform with attack and decay
                    const attack = Math.min(1, time * 8);
                    const decay = Math.exp(-time * 1.5);
                    amplitude = attack * decay * (0.6 + 0.4 * Math.sin(time * 15 + Math.sin(time * 3)));
                  } else if (clip.name.includes('吉他') || clip.name.includes('Guitar')) {
                    // Guitar-like waveform with strumming pattern
                    const strum = Math.sin(time * 8) > 0.3 ? 1 : 0.3;
                    amplitude = strum * Math.exp(-time * 0.8) * (0.7 + 0.3 * Math.sin(time * 25));
                  } else if (clip.name.includes('贝斯') || clip.name.includes('Bass')) {
                    // Bass-like waveform with strong fundamentals
                    amplitude = 0.8 * Math.sin(time * 4) * Math.exp(-time * 0.5);
                  } else if (clip.name.includes('鼓') || clip.name.includes('Drum')) {
                    // Drum-like waveform with sharp attacks
                    const hits = Math.floor(time * 4) !== Math.floor((time - 0.01) * 4);
                    amplitude = hits ? 0.9 * Math.exp(-((time % 0.25) * 20)) : 0.1 * (Math.random() - 0.5);
                  } else {
                    // Generic melodic waveform
                    amplitude = 0.5 * Math.sin(time * 6 + Math.sin(time * 2)) * Math.exp(-time * 0.3);
                  }
                  
                  // Add some randomness for realism
                  amplitude *= (0.9 + 0.1 * Math.random());
                  
                  // Calculate y position
                  const sampleY = centerY + amplitude * maxAmplitude * (Math.random() > 0.5 ? 1 : -1);
                  
                  waveformPoints.push(`${x + 4 + i * 2},${sampleY}`);
                }
                
                return (
                  <g>
                    {/* Main waveform */}
                    <polyline
                      points={waveformPoints.join(' ')}
                      fill="none"
                      stroke={clip.color}
                      strokeWidth="1"
                      opacity="0.8"
                      className="pointer-events-none"
                    />
                    {/* Secondary waveform for depth */}
                    <polyline
                      points={waveformPoints.map((point, idx) => {
                        const [px, py] = point.split(',').map(Number);
                        const newY = centerY + (py - centerY) * 0.5;
                        return `${px},${newY}`;
                      }).join(' ')}
                      fill="none"
                      stroke={clip.color}
                      strokeWidth="0.5"
                      opacity="0.4"
                      className="pointer-events-none"
                    />
                  </g>
                );
              })()}
              
              {/* Clip name */}
              <text
                x={x + 5}
                y={y + 15}
                fill="#fff"
                fontSize="10"
                fontFamily="sans-serif"
                className="pointer-events-none"
              >
                {clip.name}
              </text>
              
              {/* Clip resize handles */}
              <rect
                x={x}
                y={y}
                width="8"
                height={height}
                fill="rgba(255,255,255,0.2)"
                className="cursor-w-resize hover:fill-white"
                style={{ pointerEvents: 'auto' }}
                onMouseDown={(e) => handleMouseDown(e, clip.id, 'resize-left')}
              />
              <rect
                x={x + width - 8}
                y={y}
                width="8"
                height={height}
                fill="rgba(255,255,255,0.2)"
                className="cursor-e-resize hover:fill-white"
                style={{ pointerEvents: 'auto' }}
                onMouseDown={(e) => handleMouseDown(e, clip.id, 'resize-right')}
              />
              
              {/* Visual indicators for resize handles */}
              <rect
                x={x + 2}
                y={y + height/2 - 8}
                width="1"
                height="4"
                fill="rgba(255,255,255,0.6)"
                className="pointer-events-none"
              />
              <rect
                x={x + 4}
                y={y + height/2 - 6}
                width="1"
                height="4"
                fill="rgba(255,255,255,0.6)"
                className="pointer-events-none"
              />
              <rect
                x={x + width - 5}
                y={y + height/2 - 6}
                width="1"
                height="4"
                fill="rgba(255,255,255,0.6)"
                className="pointer-events-none"
              />
              <rect
                x={x + width - 3}
                y={y + height/2 - 8}
                width="1"
                height="4"
                fill="rgba(255,255,255,0.6)"
                className="pointer-events-none"
              />
            </g>
          );
        })}
      </svg>
    );
  };

  return (
    <div className="sequencer-container" ref={containerRef} style={{ cursor: dragState.type ? 'grabbing' : 'default' }}>
      <div className="sequencer-header">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <h3 className="text-sm font-bold text-blue-400">Sequencer</h3>
            <span className="text-xs text-gray-400">Tracks: {tracks.length}</span>
            <span className="text-xs text-gray-400">Visible: {Math.min(tracks.length, maxVisibleTracks)}</span>
            {needsScrolling && (
              <span className="text-xs text-yellow-400">({tracks.length - maxVisibleTracks} more - scroll to view)</span>
            )}
            {dragState.type && (
              <span className="text-xs text-green-400">
                {dragState.type === 'move' ? 'Moving clip...' : 
                 dragState.type === 'resize-left' ? 'Resizing left...' : 'Resizing right...'}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <label className="text-xs text-gray-400">Zoom:</label>
            <input
              type="range"
              min="0.5"
              max="3"
              step="0.1"
              value={zoom}
              onChange={(e) => setZoom(parseFloat(e.target.value))}
              className="w-20"
            />
            <span className="text-xs text-gray-400">Grid: 1/4 note</span>
          </div>
        </div>
      </div>
      
      <div className="sequencer-content relative">
        <div 
          className="sequencer-timeline" 
          ref={timelineRef}
          onScroll={(e) => {
            // Sync tracks horizontal scroll
            if (tracksScrollRef.current) {
              tracksScrollRef.current.scrollLeft = e.currentTarget.scrollLeft;
            }
          }}
        >
          <div className="relative h-full overflow-x-auto">
            {renderTimelineMarkers()}
          </div>
        </div>
        
        <div 
          className="sequencer-tracks"
          ref={tracksScrollRef}
          style={{ 
            height: `${fixedContainerHeight}px`,
            maxHeight: `${fixedContainerHeight}px`,
            overflowY: needsScrolling ? 'scroll' : 'hidden',
            overflowX: 'auto'
          }}
          onScroll={(e) => {
            setScrollTop(e.currentTarget.scrollTop);
            // Sync timeline horizontal scroll
            if (timelineRef.current) {
              timelineRef.current.scrollLeft = e.currentTarget.scrollLeft;
            }
          }}
        >
          <div 
            style={{ 
              height: `${totalTracksHeight}px`,
              width: '3000px',
              position: 'relative'
            }}
          >
            {tracks.map((track, index) => (
              <div
                key={track.id}
                className={`sequencer-track ${selectedTrack === track.id ? 'border-blue-400' : ''}`}
                style={{ 
                  height: trackHeight,
                  position: 'absolute',
                  top: index * trackHeight,
                  width: '100%',
                  left: 0
                }}
                onClick={(e) => {
                  console.log('Track clicked:', track.name, track.id);
                  const rect = e.currentTarget.getBoundingClientRect();
                  const x = e.clientX - rect.left;
                  const time = x / pixelsPerSecond;
                  console.log('Adding clip at time:', time);
                  addClip(track.id, Math.max(0, time));
                }}
              >
                <div className="absolute left-2 top-2 text-xs text-gray-400 pointer-events-none z-10">
                  {track.name}
                </div>
                {renderClips(track.id)}
              </div>
            ))}
            
            {/* Playhead that spans the entire track area */}
            {renderPlayhead()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sequencer;