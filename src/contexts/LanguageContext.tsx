import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'zh' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const translations = {
  zh: {
    // Navigation
    'nav.musicians': '音乐人',
    'nav.collaboration': '协作',
    'nav.works': '作品',
    'nav.investment': '投资',
    'nav.publishing': '发行',
    'nav.chat': '聊天',
    'nav.notifications': '通知',
    'nav.profile': '个人中心',
    'nav.logout': '退出登录',
    'nav.login': '登录',
    'nav.register': '注册',
    
    // Account Sidebar
    'account.title': '个人中心',
    'account.homepage': '我的主页',
    'account.homepage.desc': '个人展示页面',
    'account.info': '账户信息',
    'account.info.desc': '基本账户设置',
    'account.profile': '个人资料',
    'account.profile.desc': '详细个人信息',
    'account.messages': '消息中心',
    'account.messages.desc': '消息与通知',
    'account.creative': '创作中心',
    'account.creative.desc': '我的创作内容',
    'account.rewards': '我的奖励',
    'account.rewards.desc': '积分与奖励',
    'account.wallet': '我的钱包',
    'account.wallet.desc': '资产管理',
    'account.certification': '认证',
    'account.certification.desc': '身份认证',
    
    // Homepage
    'homepage.title': '让音乐创作更有温度',
    'homepage.subtitle': '10,000+音乐创作者正在这里，分享、学习、合作、成长',
    'homepage.explore': '创建作品不设限',
    'homepage.collaboration': '开启协作之旅',
    'homepage.featured.title': '精选内容',
    'homepage.musicians.title': '热门音乐人',
    'homepage.works.title': '精选作品',
    'homepage.live.title': '直播中',
    'homepage.discover.title': '发现创作空间',
    'homepage.discover.subtitle': '探索无限创意，加入协作项目',
    'homepage.view.all': '查看全部',
    'homepage.trending.musicians': '热门音乐人',
    'homepage.trending.musicians.subtitle': '发现才华横溢的音乐创作者',
    'homepage.featured.works': '精选作品',
    'homepage.featured.works.subtitle': '最新最热的音乐作品',
    'homepage.live.streams': '正在直播',
    'homepage.live.streams.subtitle': '实时音乐演出和创作',
    'homepage.join.community': '加入我们的音乐社区',
    'homepage.connect.creators': '与全球音乐创作者建立联系',
    'homepage.start.journey': '开始你的音乐之旅',
    'homepage.register.now': '立即注册',
    'homepage.login.now': '立即登录',
    'homepage.space.electronic.title': '电子音乐实验室',
    'homepage.space.electronic.desc': '探索电子音乐的无限可能，与全球制作人合作创造独特声音',
    'homepage.space.electronic.category': '电子音乐',
    'homepage.space.folk.title': '民谣创作社',
    'homepage.space.folk.desc': '用朴实的旋律诉说生活故事，寻找志同道合的音乐伙伴',
    'homepage.space.folk.category': '民谣',
    'homepage.space.hiphop.title': '说唱工作室',
    'homepage.space.hiphop.desc': '用节拍表达态度，用韵律传递力量，展现说唱文化的魅力',
    'homepage.space.hiphop.category': '说唱',
    'homepage.space.members': '位成员',
    'homepage.quality.works': '优质作品',
    'homepage.quality.works.subtitle': '精选社区热门音乐作品',
    'homepage.work.summer.breeze': '夏日微风',
    'homepage.work.electronic.dream': '电子梦境',
    'homepage.work.city.night': '城市夜色',
    'homepage.work.starry.tale': '星空物语',
    'homepage.artist.chen.ma': '陈晓音 x 马可',
    'homepage.artist.dj.xiaoyu': 'DJ晓宇',
    'homepage.artist.wang.li': '王小明 x 李诗雨',
    'homepage.artist.zhang.sophie': '张小雅 x Sophie',
    'homepage.recent.activities': '近期活动',
    'homepage.recent.activities.subtitle': '参与精彩活动，展示你的才华',
    'homepage.activity.spring.festival': '春季音乐节',
    'homepage.activity.spring.festival.desc': '聚集全球优秀音乐人，共同打造音乐盛宴，展现多元化音乐风格',
    'homepage.activity.indie.masterclass': '独立大师课',
    'homepage.activity.indie.masterclass.desc': '知名音乐制作人亲自授课，分享制作经验和创作技巧',
    'homepage.activity.youth.contest': '青年音乐大赛',
    'homepage.activity.youth.contest.desc': '为年轻音乐人提供展示平台，发掘音乐新星',
    'homepage.activity.status.enrolling': '报名中',
    'homepage.activity.status.starting.soon': '即将开始',
    'homepage.activity.status.hot.signup': '火热报名',
    'homepage.activity.join.now': '立即参与',
    'homepage.activity.time': '活动时间：',
    'homepage.activity.signup': '立即报名',
    'homepage.community.desc1': '与全球10,000+音乐创作者建立联系',
    'homepage.community.desc2': '分享创作灵感，学习专业技能，展示个人才华',
    'homepage.community.desc3': '无论你是初学者还是专业音乐人，这里都是你的舞台',
    'homepage.community.main.desc': '成为全球音乐创作者网络的一员，与志同道合的音乐人一起，创造属于这个时代的音乐作品',
    
    // Collaboration
    'collab.title': '协作空间',
    'collab.subtitle': '找到志同道合的音乐伙伴，开始你的协创之旅',
    'collab.all.spaces': '所有协创空间',
    'collab.my.spaces': '我的协创空间',
    'collab.create': '创建空间',
    'collab.create.space': '创建协创空间',
    'collab.join': '申请加入',
    'collab.members': '成员',
    'collab.status.recruiting': '招募中',
    'collab.status.ongoing': '进行中',
    'collab.status.completed': '已完成',
    'collab.load.more': '加载更多协创空间',
    'collab.created.spaces': '我创建的协创空间',
    'collab.joined.spaces': '我参与的协创空间',
    'collab.created.daw': '我创建的DAW项目',
    'collab.joined.daw': '我参与的DAW项目',
    'collab.signed.contracts': '我签署的协议',
    'collab.pending.contracts': '待签协议',
    'collab.copyright.contracts': '我创建的版权分成合约',
    'collab.manage.created': '管理你创建的所有协创空间',
    'collab.view.joined': '查看你参与的协创空间',
    'collab.manage.daw.created': '管理你创建的DAW音乐制作项目',
    'collab.view.daw.joined': '查看你参与的DAW音乐制作项目',
    'collab.view.signed.contracts': '查看已经签署的合约',
    'collab.view.pending.contracts': '查看待签署的合约',
    'collab.manage.copyright.contracts': '管理你创建的版权分成合约',
    'collab.login.required': '请先登录以创建协创空间',
    
    // My Collaboration Spaces
    'collab.tabs.discussion': '讨论',
    'collab.tabs.activity': '活动',
    'collab.tabs.media': '媒体',
    'collab.tabs.members': '成员',
    'collab.invite.members': '邀请成员',
    'collab.share.space': '分享空间',
    'collab.publish.work': '发布作品',
    'collab.create.nft': '创建NFT',
    'collab.members.count': '成员',
    'collab.recent.activity': '最近活动',
    'collab.hours.ago': '小时前',
    'collab.days.ago': '天前',
    'collab.discussion.posted': '在讨论区发表了意见',
    'collab.discussion.participated': '参与了在线讨论',
    'collab.no.discussion': '暂无讨论内容，开始你的第一条消息吧！',
    'collab.no.activity': '暂无活动记录',
    'collab.no.media': '暂无媒体文件',
    'collab.members.loading': '成员列表加载中...',
    'collab.my.role': '我的角色',
    
    // Collaboration Space Mock Data - Chinese
    'collab.space.pop.title': '原创流行歌曲制作',
    'collab.space.pop.desc': '寻找优秀的作词人和歌手，一起打造一首动听的流行歌曲',
    'collab.space.folk.title': '民谣吉他合奏项目',
    'collab.space.folk.desc': '希望找到志同道合的民谣爱好者，一起创作温暖的音乐',
    'collab.space.electronic.title': '电子音乐实验室',
    'collab.space.electronic.desc': '探索前沿的电子音乐制作技术，创造独特的音响体验',
    'collab.space.ancient.title': '古风音乐工作室',
    'collab.space.ancient.desc': '传承经典，融合现代，打造具有中国特色的音乐作品',
    'collab.creator.wang': '音乐制作人王某',
    'collab.creator.dj.alex': 'DJ_Alex',
    'collab.creator.ancient': '古韵音坊',
    'collab.creator.zhao': '作词人赵某',
    'collab.creator.chen': '吉他手陈某',
    'collab.creator.liu': '歌手刘某',
    'collab.creator.arranger': '编曲师李某',
    'collab.creator.mixer': '混音师张某',
    'collab.creator.guitarist': '吉他手陈某',
    'collab.creator.singer': '歌手刘某',
    'collab.creator.lyricist': '作词人李某',
    'collab.creator.vocalist': '歌手张某',
    'collab.creator.mixing.engineer': '混音师陈某',
    'collab.role.arrangement': '编曲',
    'collab.role.lyrics': '作词',
    'collab.role.vocals': '主唱',
    'collab.role.mixing': '混音',
    'collab.role.creator': '发起人',
    'collab.activity.melody': '发布了新的旋律片段',
    'collab.activity.recording': '上传了新的录音文件',
    'collab.activity.discussion': '在讨论区发表了意见',
    'collab.activity.participated': '参与了在线讨论',
    'collab.activity.created': '刚刚创建了协创空间',
    'collab.time.hours.ago': '小时前',
    'collab.time.days.ago': '天前',
    'collab.time.yesterday': '昨天',
    'collab.privacy.public': '公开',
    'collab.privacy.private': '私密',
    'collab.privacy.searchable': '可搜索',
    'collab.creator.label': '发起人',
    'collab.source.space': '来源空间',
    'collab.contract.signed': '已签约',
    'collab.publish': '发布',
    'collab.publish.progress': '发布中...',
    'collab.invite': '邀请',
    'collab.sign.contract': '签署合约',
    'collab.daw': 'DAW',
    'collab.copyright.contract': '版权分成合约',
    'collab.nft.create': 'NFT',
    'collab.nft.creating': '创建中...',
    'collab.my.spaces.title': '我的协创空间',
    'collab.created.spaces.title': '我创建的协创空间',
    'collab.joined.spaces.title': '我参与的协创空间',
    'collab.signed.contracts.title': '我签署的协议',
    'collab.pending.contracts.title': '待签协议',
    'collab.copyright.contracts.title': '我创建的版权分成合约',
    'collab.space.count': '个',
    'collab.contract.count': '个',
    'collab.contracts.signed': '已签署',
    'collab.contracts.pending': '待签署',
    'collab.contract.parties': '参与方',
    'collab.contract.signed.date': '签署时间',
    'collab.contract.status': '状态',
    'collab.contract.status.active': '已生效',
    'collab.contract.status.pending': '待签署',
    'collab.contract.created.date': '创建时间',
    'collab.contract.blockchain.hash': '区块链哈希',
    'collab.contract.revenue.split': '分成比例',
    'collab.contract.view.details': '查看详情',
    'collab.contract.send.invite': '发送签署邀请',
    'collab.contract.edit': '编辑合约',
    'collab.contract.create.new': '创建新的版权分成合约',
    'collab.contract.title.dreams': '《梦想启航》版权分成合约',
    'collab.contract.title.folk': '《民谣时光》版权分成合约',
    'collab.contract.revenue.composition': '作曲40% + 作词30% + 演唱20% + 混音10%',
    'collab.contract.revenue.folk': '作曲35% + 吉他20% + 演唱20% + 制作25%',
    'collab.contract.status.deployed': '已部署',
    'collab.contract.status.waiting': '等待签署',
    
    // Contract Detail specific keys
    'contract.not.found.title': '合约不存在',
    'contract.not.found.message': '合约信息未找到，请检查链接是否正确',
    'contract.detail.title': '合约详情',
    'contract.detail.subtitle': '查看合约的详细信息和条款',
    'contract.title.copyright': '版权合作协议',
    'contract.collaboration.space': '协创空间',
    'contract.status.signed': '已签署',
    'contract.status.pending': '待签署',
    'contract.signed.date': '签署时间',
    'contract.participants.count': '参与方数量',
    'contract.participants.people': '人',
    'contract.signing.time': '签署时间',
    'contract.blockchain.verification': '区块链验证',
    'contract.blockchain.verified': '已上链',
    'contract.participants.info': '参与方信息',
    'contract.participant.creator': '创作者',
    'contract.participant.percentage': '分成比例',
    'contract.terms': '合约条款',
    'contract.clause.cooperation': '第一条：合作内容',
    'contract.clause.revenue': '第二条：收益分配',
    'contract.clause.copyright': '第三条：版权归属',
    'contract.clause.breach': '第四条：违约责任',
    'contract.clause.dispute': '第五条：争议解决',
    'contract.cooperation.content': '各方就音乐作品《{spaceName}》的创作、演唱及版权分配达成协议，明确各方在音乐创作过程中的职责和权利。本协议涵盖词曲创作、编曲、录制、后期制作等各个环节的合作细节。',
    'contract.revenue.content': '各方按照贡献度和约定比例分配作品产生的所有收益，包括但不限于版税收入、演出收入、授权使用费等。分配比例如上述参与方信息所示。',
    'contract.copyright.content': '作品的版权归各参与方共同所有，任何一方不得单独处置版权。如需对外授权或转让，须经全体参与方一致同意。版权保护期限按照相关法律法规执行。',
    'contract.breach.content': '任何一方违反本协议约定的，应承担相应的违约责任，包括但不限于赔偿损失、继续履行合同等。因违约给其他方造成损失的，违约方应予以赔偿。',
    'contract.dispute.content': '因本协议产生的争议，各方应友好协商解决。协商不成的，可向有管辖权的人民法院提起诉讼。本协议适用中华人民共和国法律。',
    'contract.blockchain.info': '区块链验证信息',
    'contract.blockchain.hash': '区块链哈希',
    'contract.blockchain.timestamp': '上链时间',
    'contract.blockchain.notice': '此合约已通过区块链技术进行存证，确保合约内容的不可篡改性和真实性。',
    'contract.action.back': '返回',
    'contract.action.sign': '去签署',
    'contract.action.download': '下载合约',
    'contract.action.share': '分享合约',
    
    // Contract Signing specific keys
    'contract.signing.title': '音乐版权合约',
    'contract.signing.contract.number': '合约编号',
    'contract.signing.signed.by': '本合约由以下各方于',
    'contract.signing.signed.on': '签署',
    'contract.signing.party.first': '甲方',
    'contract.signing.party.second': '乙方',
    'contract.signing.party.third': '丙方',
    'contract.signing.role.lyricist': '作词方',
    'contract.signing.role.composer': '作曲方',
    'contract.signing.role.vocalist': '演唱方',
    'contract.signing.clause.cooperation': '第一条：合作内容',
    'contract.signing.clause.revenue': '第二条：收益分配',
    'contract.signing.cooperation.description': '各方就音乐作品《繁星点点》的创作、演唱及版权分配达成以下协议：',
    'contract.signing.cooperation.detail1': '甲方负责歌词创作，享有作词版权；',
    'contract.signing.cooperation.detail2': '乙方负责乐曲创作，享有作曲版权；',
    'contract.signing.cooperation.detail3': '丙方负责演唱，享有录音制作版权。',
    'contract.signing.revenue.description': '作品产生的收益按以下比例分配：',
    'contract.signing.revenue.lyricist': '甲方（作词）：40%',
    'contract.signing.revenue.composer': '乙方（作曲）：30%',
    'contract.signing.revenue.vocalist': '丙方（演唱）：30%',
    'contract.signing.area': '签署区域',
    'contract.signing.signature.of': '的签名',
    'contract.signing.please.sign': '请在下方区域签名：',
    'contract.signing.clear': '清除',
    'contract.signing.confirm': '确认签名',
    'contract.signing.cancel': '取消',
    'contract.signing.click.sign': '点击签名',
    'contract.signing.signed': '已签署',
    'contract.signing.pending': '待签署',
    'contract.signing.success': '签名成功！',
    'contract.signing.please.sign.first': '请先进行签名',
    'contract.signing.completed': '合约签署完成！即将返回协创空间。',
    'contract.signing.wait.all': '请等待所有参与方完成签署。',
    'contract.signing.back.collab': '返回协创空间',
    'contract.signing.notice': '所有部分都签署合约并提交后可在DAW页面进行作品合作',
    
    // Copyright Contract specific keys
    'copyright.contract.title': '版权分成合约',
    'copyright.contract.subtitle': '创建和管理音乐作品的版权分成协议，确保所有参与方的权益得到保障',
    'copyright.contract.back': '返回',
    'copyright.work.info': '作品信息',
    'copyright.work.title': '作品名称',
    'copyright.work.type': '作品类型',
    'copyright.work.type.music': '音乐作品',
    'copyright.work.type.single': '单曲',
    'copyright.work.type.album': '专辑',
    'copyright.work.type.ep': 'EP',
    'copyright.territory': '授权地区',
    'copyright.territory.global': '全球',
    'copyright.territory.china': '中国大陆',
    'copyright.territory.apac': '亚太地区',
    'copyright.territory.north.america': '北美',
    'copyright.territory.europe': '欧洲',
    'copyright.duration': '合约期限',
    'copyright.duration.permanent': '永久',
    'copyright.duration.5years': '5年',
    'copyright.duration.10years': '10年',
    'copyright.duration.20years': '20年',
    'copyright.duration.custom': '自定义',
    'copyright.effective.date': '生效日期',
    'copyright.payment.method': '支付方式',
    'copyright.payment.revenue.share': '按收益分配',
    'copyright.payment.one.time': '一次性支付',
    'copyright.payment.installment': '分期支付',
    'copyright.distribution.platforms': '发行平台',
    'copyright.special.terms': '特殊条款',
    'copyright.special.terms.placeholder': '请输入任何特殊条款或说明...',
    'copyright.participants.title': '参与方及分成比例',
    'copyright.add.party': '添加参与方',
    'copyright.party.number': '参与方',
    'copyright.party.name': '姓名',
    'copyright.party.role': '角色',
    'copyright.party.percentage': '分成比例 (%)',
    'copyright.party.email': '邮箱',
    'copyright.party.phone': '电话',
    'copyright.party.select.role': '请选择角色',
    'copyright.role.lyricist': '作词',
    'copyright.role.composer': '作曲',
    'copyright.role.arranger': '编曲',
    'copyright.role.vocalist': '演唱',
    'copyright.role.mixing': '混音',
    'copyright.role.producer': '制作',
    'copyright.role.publisher': '发行',
    'copyright.role.other': '其他',
    'copyright.total.percentage': '总分成比例：',
    'copyright.percentage.warning': '请确保总分成比例为100%',
    'copyright.contract.status': '合约状态',
    'copyright.status.generation': '合约生成',
    'copyright.status.blockchain': '区块链部署',
    'copyright.status.signing': '各方签署',
    'copyright.status.effective': '合约生效',
    'copyright.percentage.preview': '分成比例预览',
    'copyright.party.unnamed': '未命名',
    'copyright.role.unset': '未设置',
    'copyright.percentage.complete': '分成比例配置完成',
    'copyright.actions': '操作',
    'copyright.generating': '生成中...',
    'copyright.generate': '生成版权分成合约',
    'copyright.view.details': '查看合约详情',
    'copyright.send.invitation': '发送签署邀请',
    'copyright.percentage.sum.error': '分成比例总和必须为100%',
    'copyright.party.info.incomplete': '请完善所有参与方的信息',
    'copyright.contract.success': '版权分成合约生成成功！合约已部署到区块链。',
    
    // Create Space specific keys
    'space.create.title': '创建协创空间',
    'space.create.subtitle': '填写空间信息，邀请成员开始音乐协作',
    'space.create.back': '返回',
    'space.admin': '管理员',
    'space.project.progress': '项目进度',
    'space.progress.completed': '已完成',
    'space.progress.team.building': '团队组建',
    'space.progress.revenue.confirm': '分成确认',
    'space.progress.in.progress': '进行中',
    'space.progress.contract.signing': '合约签署',
    'space.progress.pending': '待开始',
    'space.basic.info': '基本信息',
    'space.name': '空间名称',
    'space.name.placeholder': '请输入协创空间名称',
    'space.description': '空间简介',
    'space.description.placeholder': '请简要描述这个协创空间的目标和需求',
    'space.required.roles': '需要的人员',
    'space.privacy.settings': '隐私设置',
    'space.privacy.public': '公开',
    'space.privacy.public.desc': '任何人都可以看到和加入这个空间',
    'space.privacy.participants': '仅参与者可见',
    'space.privacy.participants.desc': '只有受邀的成员可以看到空间内容',
    'space.privacy.searchable': '可搜索',
    'space.privacy.searchable.desc': '可以被搜索到，但需要申请加入',
    'space.revenue.settings': '分成比例设置',
    'space.revenue.desc': '设置各角色的分成比例，总计应为100%',
    'space.revenue.total': '总计：',
    'space.revenue.warning.over': '警告：分成比例超过100%，请调整',
    'space.revenue.warning.under': '提示：分成比例小于100%，请调整',
    'space.revenue.visualization': '分成比例可视化',
    'space.role.details': '角色分成详情',
    'space.revenue.note': '平台标准分成比例，可在协作签署前协商调整',
    'space.invite.members': '邀请成员',
    'space.platform.recommended': '平台推荐',
    'space.member.skills': '擅长：',
    'space.member.expected.share': '期望分成比例：',
    'space.member.rating': '评分：',
    'space.invite.join': '邀请加入',
    'space.condition.search': '条件搜索',
    'space.search.placeholder': '搜索用户名或技能',
    'space.table.index': '序号',
    'space.table.username': '用户名',
    'space.table.role': '角色',
    'space.table.expected.share': '期望分成比例',
    'space.table.rating': '平台评分',
    'space.table.has.works': '是否有作品展示',
    'space.table.actions': '操作',
    'space.table.yes': '是',
    'space.table.no': '否',
    'space.table.view': '查看',
    'space.table.invite': '邀请加入',
    'space.table.placeholder': '待搜索用户',
    'space.selected.count': '已选择',
    'space.selected.members': '位成员',
    'space.send.invites': '发送邀请',
    'space.create.button': '创建协创空间',
    'space.member.info': '成员信息',
    'space.member.skills.label': '擅长技能：',
    'space.member.expected.share.label': '期望分成比例：',
    'space.member.works.label': '作品展示：',
    'space.member.works.yes': '有',
    'space.member.works.no': '无',
    'space.modal.close': '关闭',
    'space.invite.member': '邀请成员',
    'space.invite.message': '邀请消息',
    'space.invite.message.placeholder': '写一些邀请消息，让对方了解项目详情...',
    'space.invite.button': '邀请',
    'space.batch.invite': '批量邀请成员',
    'space.invite.targets': '邀请对象',
    'space.invite.people': '人',
    'space.error.select.members': '请先选择要邀请的成员',
    'space.error.space.name': '请输入空间名称',
    'space.error.select.roles': '请选择至少一个需要的角色',
    'space.error.percentage.total': '分成比例总计为{total}%，请调整为100%',
    'space.success.created': '协创空间创建成功！',
    'space.alert.invites.sent': '邀请已发送！',
    'space.alert.batch.invites.sent': '已向{count}位成员发送邀请！',
    
    // Create space member count
    'createspace.member.count': '成员数量',
    'createspace.member.count.hint': '最多5人',
    
    // CreateSpace 核心翻译
    'createspace.back': '返回',
    'createspace.title': '创建协作空间',
    'createspace.subtitle': '填写空间信息并邀请成员，开启音乐协作之旅',
    'createspace.basic.info': '基本信息',
    'createspace.space.name': '空间名称',
    'createspace.space.name.placeholder': '请输入空间名称',
    'createspace.space.description': '空间描述',
    'createspace.space.description.placeholder': '请描述这个协作空间的目标和要求',
    'createspace.required.roles': '需要的角色',
    'createspace.privacy.settings': '隐私设置',
    'createspace.privacy.public': '公开',
    'createspace.privacy.public.desc': '任何人都可以查看和申请加入',
    'createspace.privacy.participants': '仅参与者',
    'createspace.privacy.participants.desc': '只有受邀成员可以查看',
    'createspace.privacy.searchable': '可搜索',
    'createspace.privacy.searchable.desc': '可以被搜索到，但需要申请加入',
    'createspace.revenue.settings': '分成设置',
    'createspace.revenue.description': '设置各角色的分成比例',
    'createspace.revenue.total': '总计',
    'createspace.create.button': '创建空间',
    'createspace.success.created': '协作空间创建成功！',
    'createspace.validation.space.name': '请输入空间名称',
    'createspace.validation.select.roles': '请选择至少一个需要的角色',
    'createspace.validation.revenue.total': '分成比例总计为{percentage}%，请调整为100%',
    
    // CreateSpace 角色翻译
    'createspace.role.lyrics': '作词',
    'createspace.role.composition': '作曲',
    'createspace.role.arrangement': '编曲',
    'createspace.role.vocals': '演唱',
    'createspace.role.mixing': '混音',
    'createspace.role.production': '制作',
    'createspace.role.other': '其他',
    'createspace.role.singing': '演唱',
    
    // CreateSpace 其他翻译
    'createspace.admin': '管理员',
    'createspace.progress.title': '项目进度',
    'createspace.progress.completed': '已完成',
    'createspace.progress.team.building': '团队建设',
    'createspace.progress.revenue.confirmation': '分成确认',
    'createspace.progress.ongoing': '进行中',
    'createspace.progress.contract.signing': '合约签署',
    'createspace.progress.pending': '等待中',
    'createspace.revenue.visualization': '分成可视化',
    'createspace.revenue.details': '分成详情',
    'createspace.revenue.hint': '请确保总分成为100%',
    'createspace.revenue.warning': '分成总计超过100%，请调整',
    'createspace.revenue.platform.standard': '平台标准分成',
    'createspace.invite.members': '邀请成员',
    'createspace.platform.recommendations': '平台推荐',
    'createspace.member.specialties': '专长：',
    'createspace.member.expected.share': '期望分成：',
    'createspace.member.rating': '评分：',
    'createspace.invite.join': '邀请加入',
    'createspace.conditional.search': '条件搜索',
    'createspace.search.placeholder': '搜索成员...',
    'createspace.category.all': '全部',
    'createspace.current.user': '当前用户',
    'createspace.status.recruiting': '招募中',
    'createspace.selected.members': '已选择{count}位成员',
    'createspace.send.invitations': '发送邀请给{count}位成员',
    'createspace.validation.select.members': '请先选择要邀请的成员',
    
    // CreateSpace 成员名称
    'createspace.member.lin.yuwei': '林雨薇',
    'createspace.member.chen.junjie': '陈俊杰', 
    'createspace.member.zhang.mengqi': '张梦琪',
    
    // CreateSpace 表格翻译
    'createspace.table.number': '序号',
    'createspace.table.username': '用户名',
    'createspace.table.role': '角色',
    'createspace.table.expected.share': '期望分成',
    'createspace.table.rating': '评分',
    'createspace.table.portfolio': '作品集',
    'createspace.table.actions': '操作',
    'createspace.table.yes': '是',
    'createspace.table.no': '否',
    'createspace.table.view': '查看',
    'createspace.table.invite': '邀请',
    'createspace.table.awaiting.users': '等待用户加入...',
    
    // CreateSpace 弹窗翻译
    'createspace.modal.member.info': '成员信息',
    'createspace.modal.platform.rating': '平台评分：',
    'createspace.modal.specialties.label': '专长领域',
    'createspace.modal.portfolio.label': '作品展示',
    'createspace.modal.close': '关闭',
    'createspace.modal.invite.member': '邀请成员',
    
    // ViewAllSpaces specific keys
    'spaces.hot.title': '热门协创空间',
    'spaces.search.placeholder': '搜索协作空间',
    'spaces.category.title': '类别',
    'spaces.category.all': '全部',
    'spaces.category.lyricist': '找作词',
    'spaces.category.composer': '找作曲',
    'spaces.category.vocalist': '找歌手',
    'spaces.category.band': '找乐队',
    'spaces.category.producer': '找制作',
    'spaces.join': '加入',
    'spaces.view.translation': '查看翻译',
    'spaces.like': '赞',
    'spaces.comment': '评论',
    'spaces.comments.count': '条评论',
    'spaces.join.space': '加入空间',
    'spaces.featured.classical': '古典音乐鉴赏与创作',
    'spaces.featured.classical.desc': '古典音乐爱好者的专业交流平台',
    'spaces.featured.electronic': '电子音乐制作人联盟',
    'spaces.featured.electronic.desc': '电子音乐制作技术与创意分享',
    'spaces.members.count': '万位成员',
    'spaces.posts.yearly': '年均发表：',
    'spaces.posts.count': '篇',
    
    // MySpaces specific keys
    'myspaces.cover.alt': '空间封面',
    'myspaces.edit': '编辑',
    'myspaces.public.group': '公开小组',
    'myspaces.members.count': '位成员',
    'myspaces.invite': '邀请',
    'myspaces.share': '分享',
    'myspaces.tab.discussion': '讨论',
    'myspaces.tab.activity': '活动',
    'myspaces.tab.content': '影音内容',
    'myspaces.tab.files': '文件',
    'myspaces.tab.members': '成员',
    'myspaces.sign.contract': '签署合约',
    'myspaces.daw': 'DAW',
    'myspaces.share.mood': '分享心情...',
    'myspaces.quick.anonymous': '匿名发帖',
    'myspaces.quick.vote': '投票',
    'myspaces.quick.activity': '感受/活动',
    'myspaces.created.group': '创建了小组',
    'myspaces.hours.ago': '小时前',
    'myspaces.no.content': '该动态暂无内容',
    'myspaces.like': '赞',
    'myspaces.comment': '评论',
    'myspaces.send': '发送',
    'myspaces.share.post': '分享',
    'myspaces.write.comment': '写评论...',
    'myspaces.start.conversation': '开始对话',
    'myspaces.share.ideas': '分享想法、提出问题或开始讨论',
    'myspaces.first.post': '发表第一篇帖子',
    'myspaces.content.developing': '内容开发中...',
    'myspaces.create.post': '创建帖子',
    'myspaces.what.thinking': '你在想什么？',
    'myspaces.post': '发布',
    
    // AllSpaces discussion data specific keys
    'allspaces.discussion.hello': '大家好！我们的主旋律部分已经完成了初步录制，请大家听一下demo，有什么建议可以在这里讨论。特别是歌词部分，希望能更贴近主题。',
    'allspaces.discussion.demo.good': '听了demo，整体感觉很不错！我觉得副歌部分可以再强调一下情感递进，我会在今晚提交修改版本。',
    'allspaces.discussion.bridge.adjust': '同意李老师的建议，另外我觉得bridge部分的音域可以稍微调整一下，这样演唱起来会更舒服。',
    'allspaces.discussion.mixing': '关于混音的问题想和大家讨论一下：目前鼓点和贝斯的层次感还需要调整，我建议在副歌部分增加一些氛围音效，让整体更有冲击力。',
    'allspaces.discussion.mixing.reply': '很好的建议！我们可以试试在副歌加入一些合成器的pad音效，营造更宽广的声场。你有什么具体的想法吗？',
    'allspaces.discussion.recording': '今天录制了一版试唱，感觉整体还不错，但是有几个地方的咬字还需要调整。大家可以听听看，给点意见！',
    'allspaces.time.hours.ago': '小时前',
    'allspaces.time.minutes.ago': '分钟前',
    'allspaces.time.days.ago': '天前',
    'allspaces.author.producer.wang': '音乐制作人王某',
    'allspaces.author.lyricist.li': '作词人李某',
    'allspaces.author.singer.zhang': '歌手张某',
    'allspaces.author.mixer.chen': '混音师陈某',
    'allspaces.activity.file.upload': '上传了新文件：主旋律_v3.2.wav',
    'allspaces.activity.file.size': '文件大小：25.6MB',
    'allspaces.activity.comment.post': '在讨论区发表了新评论',
    'allspaces.activity.comment.detail': '关于歌词修改的建议',
    'allspaces.activity.member.join': '加入了协创空间',
    'allspaces.activity.role.mixer': '角色：混音师',
    'allspaces.activity.version.create': '创建了新版本 v3.1',
    'allspaces.activity.version.detail': '完成主歌部分录制和初步混音',
    'allspaces.system': '系统',
    
    // AllSpaces data specific keys
    'allspaces.data.pop.title': '原创流行歌曲制作',
    'allspaces.data.pop.desc': '寻找优秀的作词人和歌手，一起打造一首动听的流行歌曲',
    'allspaces.data.folk.title': '民谣吉他合奏项目',
    'allspaces.data.folk.desc': '希望找到志同道合的民谣爱好者，一起创作温暖的音乐',
    'allspaces.data.electronic.title': '电子音乐实验室',
    'allspaces.data.electronic.desc': '探索前沿的电子音乐制作技术，创造独特的音响体验',
    'allspaces.data.ancient.title': '古风音乐创作',
    'allspaces.data.ancient.desc': '传统与现代的完美结合，创作具有中国风特色的音乐作品',
    'allspaces.data.indie.title': '独立音乐联盟',
    'allspaces.data.indie.desc': '支持原创，拒绝平庸，打造属于我们的独立音乐空间',
    'allspaces.status.recruiting': '招募中',
    'allspaces.status.in.progress': '进行中',
    'allspaces.status.completed': '已完成',
    'allspaces.creator.producer.wang': '音乐制作人小王',
    'allspaces.creator.folk.fan': '民谣小清新',
    'allspaces.creator.electronic.master': '电子音乐大师',
    'allspaces.creator.ancient.poet': '古风诗人',
    'allspaces.creator.indie.rock': '独立摇滚',
    'allspaces.role.lyrics': '作词',
    'allspaces.role.composition': '作曲',
    'allspaces.role.vocals': '歌手',
    'allspaces.role.guitar': '吉他',
    'allspaces.role.mixing': '混音',
    'allspaces.role.production': '制作',
    'allspaces.role.ancient.arrange': '古风编曲',
    'allspaces.alert.apply.join': '已申请加入，发起人审核后通过后就会在我申请的协创空间中显示',
    'collab.initiator': '发起人',
    'collab.welcome.title': '我的协创空间',
    'collab.welcome.desc': '在这里管理你的协创空间、签署的协议和版权分成合约',
    'collab.welcome.instruction': '请从左侧菜单选择要查看的内容',
    'collab.success.publish': '协创作品已成功发布到作品页面！',
    'collab.error.publish': '发布失败，请稍后重试',
    'collab.success.nft': 'NFT创建成功！',
    'collab.nft.details': '该NFT已在作品详情页显示，支持购买功能。',
    'collab.error.nft': 'NFT创建失败，请稍后重试',
    'collab.categories.all': '全部',
    'collab.categories.lyrics': '找作词',
    
    // Collaboration page
    'collab.spaces.title': '协作空间',
    'collab.spaces.subtitle': '与全球音乐人一起创作美妙的音乐',
    'collab.search.placeholder': '搜索协作空间...',
    'collab.search.button': '搜索',
    'collab.created.at': '创建于',
    'collab.join.collaboration': '加入协作',
    'collab.no.spaces': '暂无协作空间',
    'collab.no.spaces.desc': '创建第一个协作空间，开始你的音乐创作之旅',
    'collab.create.now': '立即创建',
    'collab.demo.pop.title': '流行音乐协作室',
    'collab.demo.pop.desc': '专注于流行音乐创作的协作空间，欢迎各种乐器演奏者和词曲创作人加入',
    'collab.demo.electronic.title': '电子音乐实验室',
    'collab.demo.electronic.desc': '探索电子音乐的无限可能，使用最新的合成器和制作技术',
    'collab.demo.rock.title': '摇滚乐队招募',
    'collab.demo.rock.desc': '寻找志同道合的摇滚音乐人，组建新的乐队，一起征服舞台',
    
    // AllSpaces 成员和历史数据
    'collab.member.arranger.li': '编曲师李某',
    'collab.member.mixer.zhang': '混音师张某',
    'collab.member.guitarist.chen': '吉他手陈某',
    'collab.member.singer.liu': '歌手刘某',
    'collab.member.lyricist.zhao': '作词人赵某',
    'collab.member.lyricist.li': '作词人李某',
    'collab.member.singer.zhang': '歌手张某',
    'collab.member.mixer.chen': '混音师陈某',
    'collab.member.producer.wang': '音乐制作人王某',
    'collab.member.lyricist.li.full': '作词人李某',
    'collab.member.singer.zhang.full': '歌手张某',
    'collab.member.mixer.chen.full': '混音师陈某',
    'collab.role.lyricist': '作词',
    'collab.role.vocalist': '主唱',
    'collab.role.mixer': '混音',
    'collab.specialty.composition': '作曲',
    'collab.specialty.arrangement': '编曲',
    'collab.specialty.lyrics': '作词',
    'collab.specialty.vocals': '演唱',
    'collab.specialty.harmony': '和声',
    'collab.specialty.mixing': '混音',
    'collab.specialty.mastering': '母带处理',
    'collab.time.2hours.ago': '2小时前',
    'collab.time.1day.ago': '1天前',
    'collab.time.5hours.ago': '5小时前',
    'collab.time.3hours.ago': '3小时前',
    
    // 版本历史翻译
    'collab.version.title.main.melody': '完成主旋律录制和初步混音',
    'collab.version.title.lyrics.vocals': '歌词创作完成，录制演唱部分',
    'collab.version.title.arrangement.framework': '基础编曲框架搭建',
    'collab.version.title.concept.style': '概念确定和风格定位',
    'collab.author.team.discussion': '团队讨论',
    'collab.change.main.recording': '完成主歌部分的录制',
    'collab.change.drums.bass': '添加了鼓点和贝斯',
    'collab.change.initial.mixing': '初步混音调整',
    'collab.change.beat.sync.fix': '修复了节拍同步问题',
    'collab.change.lyrics.complete': '完成完整歌词创作',
    'collab.change.demo.recording': '录制试唱版本',
    'collab.change.lyrics.rhythm': '调整歌词韵律',
    'collab.change.harmony.arrangement': '添加和声编排',
    'collab.change.song.structure': '确定歌曲整体结构',
    'collab.change.chord.progression': '创建基础和弦进行',
    'collab.change.melody.framework': '设计主旋律框架',
    'collab.change.production.timeline': '制定制作时间表',
    'collab.change.pop.rock.style': '确定流行摇滚风格',
    'collab.change.theme.direction': '讨论歌曲主题方向',
    'collab.change.division.plan': '制定分工计划',
    'collab.change.project.milestones': '设置项目里程碑',
    
    // 文件名翻译
    'collab.file.main.melody': '主旋律',
    'collab.file.drums': '鼓点',
    'collab.file.lyrics.final': '歌词_最终版',
    'collab.file.demo.vocal': '试唱',
    'collab.file.arrangement.sketch': '编曲草图',
    'collab.file.chord.chart': '和弦谱',
    'collab.file.project.plan': '项目规划',
    'collab.file.style.reference': '风格参考',
    'collab.categories.composition': '找作曲',
    'collab.categories.singer': '找歌手',
    'collab.categories.mixing': '找混音',
    'collab.categories.production': '找制作发行',
    
    // Musicians
    'musicians.title': '音乐人',
    'musicians.subtitle': '发现才华横溢的音乐创作者，开启你的音乐协作之旅',
    'musicians.search.placeholder': '搜索音乐人...',
    'musicians.category.title': '类别',
    'musicians.category.all': '全部',
    'musicians.category.singer': '歌手',
    'musicians.category.producer': '制作人',
    'musicians.category.composer': '作曲家',
    'musicians.category.lyricist': '作词人',
    'musicians.category.rapper': '说唱歌手',
    'musicians.followers': '粉丝',
    'musicians.works': '作品',
    'musicians.rating': '评分',
    'musicians.verified': '认证',
    'musicians.follow': '关注音乐人',
    'musicians.follow.success': '已关注',
    'musicians.message': '私信',
    'musicians.view.profile': '查看主页',
    'musicians.no.results': '没有找到音乐人',
    'musicians.no.results.desc': '尝试调整搜索条件或浏览其他分类',
    
    // Musician mock data
    'musician.1.name': '林雨涵',
    'musician.1.description': '专注于现代流行音乐的创作与演唱，擅长将古典元素融入现代音乐中，营造独特的音乐氛围',
    'musician.2.name': '张志华', 
    'musician.2.description': '资深音乐制作人，在电子音乐制作方面有着丰富的经验，曾为多位知名艺人制作专辑',
    'musician.3.name': '陈梦琪',
    'musician.3.description': '新生代音乐人，以独特的嗓音和强烈的个人风格在音乐圈中脱颖而出，深受年轻听众喜爱',
    'musician.4.name': '刘东京',
    'musician.4.description': '独立音乐人，擅长多种乐器演奏，音乐作品风格多样，从民谣到摇滚都有涉及',
    'musician.5.name': '王艺萌',
    'musician.5.description': '才华横溢的词作者，作品情感真挚，文字优美，善于用音乐传达深层的情感和思考',
    'musician.6.name': '郑建国',
    'musician.6.description': '爵士乐演奏家，精通钢琴和萨克斯，音乐风格优雅而富有感染力，经常在各大音乐节演出',
    
    // Genre translations
    'genre.singer': '歌手',
    'genre.composer': '作曲',  
    'genre.lyricist': '作词',
    'genre.instrumental': '器乐',
    
    // Tag translations
    'tag.pop': '流行',
    'tag.classical': '古典',
    'tag.creative': '创作',
    'tag.electronic': '电子',
    'tag.production': '制作',
    'tag.arrangement': '编曲',
    'tag.independent': '独立',
    'tag.folk': '民谣',
    'tag.rock': '摇滚',
    'tag.multiinstrument': '多乐器',
    'tag.lyrics': '作词',
    'tag.literature': '文学',
    'tag.emotional': '情感',
    'tag.jazz': '爵士',
    'tag.piano': '钢琴',
    'tag.saxophone': '萨克斯',
    
    // Location translations
    'location.beijing': '北京市',
    'location.shanghai': '上海市',
    'location.guangzhou': '广州市',
    'location.shenzhen': '深圳市',
    'location.chengdu': '成都市',
    'location.hangzhou': '杭州市',
    
    // Musician Detail Data
    'musician.detail.name': '陈实迅',
    'musician.detail.genre': '歌手',
    'musician.detail.location': '香港著名歌手、以其独特的嗓音和情感丰富的演唱风格深受听众喜爱。音乐作品横跨流行、摇滚、电子等多个领域。',
    'musician.detail.description': '香港著名歌手，以其独特的嗓音和情感丰富的演唱风格深受听众喜爱。音乐作品横跨流行、摇滚、电子等多个领域，致力于为听众带来更多高质量的音乐作品。',
    'musician.detail.tag.pop': '流行',
    'musician.detail.tag.rock': '摇滚', 
    'musician.detail.tag.creative': '创作',
    'musician.detail.tag.vocal': '演唱',
    
    // Song titles
    'song.1.title': '训练营唱歌',
    'song.2.title': '富士山下',
    'song.3.title': '陪你度过漫长岁月',
    'song.4.title': '为你流泪',
    'song.5.title': '最佳损友',
    'song.6.title': '十年',
    'song.7.title': '千千万万',
    'song.8.title': '不要说话',
    'song.9.title': '十面埋伏',
    'song.10.title': '好久不见',
    
    // Album titles
    'album.1.title': '收藏专辑',
    'album.2.title': '精选集',
    'album.3.title': '新作品',
    'musicians.loading': '加载中...',
    
    // DAW Project Manager
    'daw.demo.project1': '我的新作品',
    'daw.demo.project2': '夏日回忆',
    'daw.project.manager.title': '项目管理器',
    'daw.recent.projects': '最近项目',
    'daw.project.templates': '项目模板',
    'daw.shared.projects': '共享项目',
    'daw.create.new.project': '创建新项目',
    'daw.project.name': '项目名称',
    'daw.create': '创建',
    'daw.cancel': '取消',
    'daw.last.modified': '最后修改',
    'daw.collaborators': '协作者',
    'daw.no.projects': '暂无项目',
    'daw.create.first.project': '创建你的第一个项目',
    
    // DAW TrackList translations
    'daw.tracks.title': 'Tracks',
    'daw.add.track': 'Add Track',
    'daw.audio.track': '音频轨道',
    'daw.audio.recording.track': '音频录制轨道',
    'daw.midi.instruments': 'MIDI 乐器',
    
    // Account MyHomepage translations
    'account.homepage.quick.actions.live': '直播视频',
    'account.homepage.quick.actions.photo': '照片/视频',
    'account.homepage.quick.actions.music.life': '音乐人生活',
    'account.homepage.post.minutes.ago': '分钟',
    'account.homepage.post.author': 'Fred Yee',
    'account.homepage.post.content1': '找几个音乐人一起创作几首歌曲',
    'account.homepage.post.content2': '找一位作曲人，词已写好了，',
    
    // Musician Detail
    'musician.detail.followers': '粉丝',
    'musician.detail.following': '关注',
    'musician.detail.works': '作品',
    'musician.detail.follow': '+ 关注',
    'musician.detail.followed': '已关注',
    'musician.detail.message': '私信',
    'musician.detail.tabs.works': '作品',
    'musician.detail.tabs.albums': '专辑',
    'musician.detail.tabs.related': '相关MV',
    'musician.detail.play.all': '播放全部',
    'musician.detail.collected.songs': '收藏歌曲',
    'musician.detail.sort.popular': '热门作品',
    'musician.detail.sort.latest': '最新作品',
    'musician.detail.sort.plays': '播放量',
    'musician.detail.recent.works': '最新作品',
    'musician.detail.plays': '播放',
    'musician.detail.related.musicians': '相关音乐人',
    
    // Works
    'works.title': '作品订阅',
    'works.subtitle': '发现和订阅精彩的音乐作品',
    'works.search.placeholder': '搜索作品...',
    'works.category.title': '类别',
    'works.category.top': 'Top',
    'works.category.hot': '最热',
    'works.category.style': '类型',
    'works.category.musician': '音乐人',
    'works.subscription': '精品订阅',
    'works.collaborators': '位合作者',
    'works.play': '播放',
    'works.like': '点赞',
    'works.favorite': '收藏',
    'works.share': '分享',
    'works.comments': '评论',
    'works.no.results': '没有找到作品',
    'works.no.results.desc': '尝试调整搜索条件或浏览其他分类',
    'works.loading': '加载中...',
    'works.playing': '正在播放：',
    'works.liked': '已点赞作品：',
    'works.favorited': '已收藏作品：',
    'works.share.dev': '分享功能开发中！',
    'works.my.works': '我的作品',
    'works.published': '已发布',
    'works.drafts': '草稿',
    'works.likes': '点赞',
    'works.plays': '播放',
    'works.shares': '分享',
    
    // Work Detail
    'work.detail.play': '播放',
    'work.detail.pause': '暂停',
    'work.detail.like': '点赞',
    'work.detail.share': '分享',
    'work.detail.comment': '评论',
    'work.detail.download': '下载',
    'work.detail.collect': '收藏',
    'work.detail.artist': '艺术家',
    'work.detail.duration': '时长',
    'work.detail.genre': '类型',
    'work.detail.release.date': '发布时间',
    'work.detail.description': '作品描述',
    'work.detail.tags': '标签',
    'work.detail.related.works': '相关作品',
    'work.detail.comments.title': '评论',
    'work.detail.add.comment': '添加评论',
    'work.detail.post.comment': '发表评论',
    'work.detail.not.exist': '作品不存在',
    'work.detail.back.to.list': '返回作品列表',
    'work.detail.intro': '作品简介',
    'work.detail.copyright': '版权信息',
    'work.detail.composer': '作曲',
    'work.detail.lyricist': '作词',
    'work.detail.producer': '制作人',
    'work.detail.label': '发行方',
    'work.detail.release.time': '发布时间',
    'work.detail.plays.count': '播放量',
    'work.detail.write.comment': '写评论...',
    'work.detail.post': '评论',
    'work.detail.reply': '回复',
    'work.detail.verified': '认证',
    'work.detail.just.now': '刚刚',
    'work.detail.likes': '喜欢',
    'work.detail.plays.k': 'k 播放',
    
    // DAW
    'daw.title': 'DAW',
    'daw.projects': 'Projects',
    'daw.save.project': '保存项目',
    'daw.export.data': '导出项目数据',
    'daw.show.automation': '显示Automation',
    'daw.hide.automation': '隐藏Automation',
    'daw.connected': '已连接后端',
    'daw.offline': '离线模式',
    'daw.tracks': 'Tracks',
    'daw.clips': 'Clips',
    'daw.events': 'Events',
    'daw.online': 'Online',
    'daw.saved': '已保存',
    'daw.save.failed': '保存失败',
    'daw.instruments.piano': '钢琴',
    'daw.instruments.guitar': '吉他',
    'daw.instruments.bass': '贝斯',
    'daw.instruments.trumpet': '小号',
    'daw.instruments.violin': '小提琴',
    'daw.instruments.drums': '鼓组',
    'daw.instruments.synth': '合成器',
    'daw.instruments.flute': '长笛',
    'daw.instruments.saxophone': '萨克斯风',
    'daw.instruments.clarinet': '单簧管',
    'daw.instruments.cello': '大提琴',
    'daw.instruments.harp': '竖琴',
    'daw.instruments.organ': '管风琴',
    'daw.instruments.electricpiano': '电钢琴',
    'daw.instruments.electricguitar': '电吉他',
    'daw.instruments.acousticguitar': '原声吉他',
    'daw.instruments.electricbass': '电贝斯',
    'daw.instruments.upright_bass': '立式贝斯',
    'daw.instruments.french_horn': '法国号',
    'daw.instruments.trombone': '长号',
    'daw.instruments.tuba': '大号',
    'daw.instruments.oboe': '双簧管',
    'daw.instruments.piccolo': '短笛',
    'daw.instruments.banjo': '班卓琴',
    'daw.instruments.mandolin': '曼陀林',
    'daw.instruments.marimba': '马林巴',
    'daw.instruments.vibraphone': '颤音琴',
    'daw.instruments.xylophone': '木琴',
    'daw.instruments.timpani': '定音鼓',
    'daw.instruments.pad': '铺底音色',
    'daw.instruments.lead': '主音合成器',
    'daw.instruments.bass_synth': '贝斯合成器',
    'daw.instruments.arpeggiator': '琶音器',
    'daw.instruments.string_ensemble': '弦乐合奏',
    'daw.track.audio': '音频轨道',
    
    // DAW Project Manager translations
    'daw.new.project': '新建项目',
    'daw.loading.projects': '加载项目中...',
    'daw.no.shared': '暂无共享项目。',
    'daw.create.new': '创建新项目',
    'daw.name.placeholder': '输入项目名称...',
    'daw.close': '关闭',
    'daw.use.template': '使用模板',
    
    // Additional DAW translations
    'daw.tempo': '节拍',
    'daw.bpm': 'BPM',
    'daw.time.signature': '拍号',
    'daw.visibility.private': '私有',
    'daw.visibility.public': '公开',
    'daw.visibility.collaborative': '协作',
    'daw.project.from': '来自',
    
    // Chat/Social
    'social.new.post.placeholder': '分享你的音乐想法...',
    'social.image': '图片',
    'social.audio': '音频',
    'social.video': '视频',
    'social.publish': '发布',
    'social.publishing': '发布中...',
    'social.just.now': '刚刚',
    'social.hours.ago': '小时前',
    'social.days.ago': '天前',
    'social.current.user': '当前用户',
    
    // Account Details
    'account.basic.info': '基本信息',
    'account.security': '账户安全',
    'account.privacy': '隐私设置',
    'account.notifications': '通知设置',
    'account.demo.user': '演示用户',
    'account.change.avatar': '更换头像',
    'account.username': '用户名',
    'account.nickname': '昵称',
    'account.email': '邮箱',
    'account.phone': '手机号',
    'account.birthday': '生日',
    'account.gender': '性别',
    'account.bio': '个人简介',
    'account.location': '所在地',
    'account.save': '保存',
    'account.cancel': '取消',
    'account.settings': '账户设置',
    'account.bio.demo': '这是一个演示用户的个人简介。热爱音乐，喜欢创作，经常参与协作项目。',
    'account.save.changes': '保存更改',
    'account.security.tip.title': '账户安全提示',
    'account.security.tip.desc': '定期更新密码，启用两步验证，保护账户安全',
    'account.password': '登录密码',
    'account.password.last.modified': '最后修改：2024-01-01',
    'account.change.password': '修改密码',
    'account.two.factor.title': '两步验证',
    'account.two.factor.desc': '提高账户安全性',
    'account.enable': '启用',
    'account.device.management.title': '登录设备管理',
    'account.device.management.desc': '查看和管理登录设备',
    'account.view.devices': '查看设备',
    'account.profile.visibility.title': '个人资料可见性',
    'account.profile.visibility.desc': '控制谁可以查看你的个人资料',
    'account.works.visibility.title': '作品可见性',
    'account.works.visibility.desc': '控制谁可以查看你的作品',
    'account.allow.messages.title': '允许私信',
    'account.allow.messages.desc': '控制谁可以给你发私信',
    'account.visibility.everyone': '所有人',
    'account.visibility.following': '关注的人',
    'account.visibility.self': '仅自己',
    'account.visibility.none': '不允许',
    'account.notification.followers.title': '新关注者通知',
    'account.notification.followers.desc': '有人关注你时通知',
    'account.notification.comments.title': '作品评论通知',
    'account.notification.comments.desc': '作品被评论时通知',
    'account.notification.system.title': '系统通知',
    'account.notification.system.desc': '系统消息和更新通知',
    'account.notification.email.title': '邮件通知',
    'account.notification.email.desc': '重要通知发送邮件',
    
    // Message Center translations
    'message.center': '消息中心',
    'message.search.placeholder': '搜索消息...',
    'message.my.messages': '我的消息',
    'message.replies': '回复我的',
    'message.mentions': '@我的',
    'message.likes': '收到的赞',
    'message.system': '系统通知',
    'message.private': '我的私信',
    'message.mark.all.read': '全部已读',
    'message.clear': '清空',
    'message.no.messages': '暂无消息',
    
    // MessageCenter mock data translations
    'message.user.music.lover': '音乐爱好者',
    'message.user.creator': '创作达人',
    'message.user.producer': '音乐制作人',
    'message.user.system': '系统消息',
    'message.user.partner': '协作伙伴',
    'message.user.student': '音乐学生',
    'message.user.band.leader': '乐队主唱',
    'message.user.fan': '粉丝用户',
    'message.user.company': '音乐公司',
    'message.content.liked.work': '点赞了你的作品《夜曲》',
    'message.content.replied.comment': '回复了你的评论：这首歌很有意境，期待更多作品',
    'message.content.mentioned': '在动态中@了你：@demo_user 一起来听听这首新作品',
    'message.content.work.approved': '你的作品《春天的故事》已通过审核',
    'message.content.collaboration.discuss': '想和你讨论下新项目的合作事宜',
    'message.content.replied.thanks': '回复了你：谢谢老师的指导，我会继续努力的',
    'message.content.mentioned.arrangement': '在评论中@了你：@demo_user 你觉得这个编曲怎么样？',
    'message.content.liked.post': '点赞了你的动态',
    'message.content.collaboration.proposal': '希望能与你合作，请查看合作提案',
    'message.time.2min.ago': '2分钟前',
    'message.time.15min.ago': '15分钟前',
    'message.time.1hour.ago': '1小时前',
    'message.time.2hours.ago': '2小时前',
    'message.time.3hours.ago': '3小时前',
    'message.time.30min.ago': '30分钟前',
    'message.time.1day.ago': '1天前',
    'message.time.2days.ago': '2天前',
    
    // AllSpaces sorting and tabs translations
    'allspaces.sort.hot': '按热门排序',
    'allspaces.sort.new': '按时间排序',
    'allspaces.sort.members': '按成员数排序',
    'allspaces.tab.discussion': '讨论',
    'allspaces.tab.activity': '活动',
    'allspaces.tab.media': '影音和文件',
    'allspaces.tab.members': '成员',
    'allspaces.tab.history': '历史版本',
    'allspaces.discussion.title': '团队讨论',
    'allspaces.discussion.new': '发起新讨论',
    'allspaces.discussion.empty.title': '还没有讨论内容',
    'allspaces.discussion.empty.desc': '开始你的第一个团队讨论吧！',
    'allspaces.discussion.placeholder': '参与讨论...',
    'allspaces.activity.title': '活动记录',
    'allspaces.activity.recent': '最近 7 天',
    'allspaces.activity.empty.title': '还没有活动记录',
    'allspaces.activity.empty.desc': '当成员开始协作时，活动记录会显示在这里',
    'allspaces.activity.view.more': '查看更多活动记录',
    'allspaces.members.title': '成员列表',
    'allspaces.members.count': '位成员',
    'allspaces.members.invite': '邀请新成员',
    'allspaces.members.view.profile': '查看资料',
    'allspaces.members.remove': '移除',
    'allspaces.history.title': '历史版本',
    'allspaces.history.count': '个版本',
    'allspaces.history.empty.title': '还没有版本记录',
    'allspaces.history.empty.desc': '当作品有更新时，版本历史会显示在这里',
    'allspaces.history.load.more': '加载更多历史版本',
    
    // 操作按钮相关翻译
    'allspaces.button.sign.contract': '签署合约',
    'allspaces.button.publish.work': '发布作品',
    'allspaces.button.publishing': '发布中...',
    'allspaces.button.create.nft': '创建NFT',
    'allspaces.button.creating.nft': '创建中...',
    
    // 侧边栏菜单
    'allspaces.sidebar.my.spaces': '我的协创空间',
    'allspaces.sidebar.expand.indicator': '▼',
    'allspaces.sidebar.collapse.indicator': '▶',
    
    // 影音和文件数据翻译
    'allspaces.media.file.name.melody': '主旋律_v3.2.wav',
    'allspaces.media.file.name.vocal': '试唱版本_v2.1.mp3',
    'allspaces.media.file.name.arrangement': '编曲草图_v3.0.mid',
    'allspaces.media.file.name.lyrics': '歌词_最终版.txt',
    'allspaces.media.file.name.chords': '和弦谱_v3.0.pdf',
    'allspaces.media.file.name.project.plan': '项目规划.pdf',
    'allspaces.media.file.name.album.cover': '专辑封面设计_v1.jpg',
    'allspaces.media.file.name.sheet.music': '乐谱截图.png',
    'allspaces.media.file.uploaded.by.producer': '音乐制作人王某',
    'allspaces.media.file.uploaded.by.singer': '歌手张某',
    'allspaces.media.file.uploaded.by.lyricist': '作词人李某',
    'allspaces.media.time.hours.ago': '小时前',
    'allspaces.media.time.day.ago': '天前',
    'allspaces.media.time.days.ago': '天前',
    
    // 合约相关翻译
    'allspaces.contract.source.space': '来源空间：',
    'allspaces.contract.created.time': '创建时间：',
    'allspaces.contract.status.deployed': '已部署',
    'allspaces.contract.status.pending': '等待签署',
    'allspaces.contract.status.draft': '草稿',
    
    // DAW项目翻译
    'allspaces.daw.project.edm': '电子舞曲制作项目',
    'allspaces.daw.project.edm.desc': '制作原创EDM舞曲，已完成基础编曲',
    'allspaces.daw.project.folk': '民谣吉他录音项目',
    'allspaces.daw.project.folk.desc': '录制温暖的民谣吉他作品',
    'allspaces.daw.project.traditional': '古风音乐编曲',
    'allspaces.daw.project.traditional.desc': '传统乐器与现代编曲的完美结合',
    'allspaces.daw.project.rock': '摇滚乐队录音',
    'allspaces.daw.project.rock.desc': '参与摇滚乐队的鼓点录制和混音',
    'allspaces.daw.project.hiphop': 'Hip-Hop Beat制作',
    'allspaces.daw.project.hiphop.desc': '为说唱歌手制作原创Beat',
    'allspaces.daw.project.piano': '钢琴独奏录制',
    'allspaces.daw.project.piano.desc': '协助完成古典钢琴作品的录制',
    'allspaces.daw.project.jazz': '爵士即兴演奏',
    'allspaces.daw.project.jazz.desc': '参与爵士乐队的即兴演奏录制',
    'allspaces.daw.status.in.progress': '进行中',
    'allspaces.daw.status.recruiting': '招募中',
    'allspaces.daw.status.completed': '已完成',
    
    // 通用翻译
    'allspaces.role.all': '全部角色',
    'allspaces.time.just.now': '刚刚',
    
    // 申请加入相关翻译
    'allspaces.space.not.found': '找不到该协作空间',
    'allspaces.apply.success': '申请已提交！创建者将审核您的申请，通过后您可在我的协作空间中查看',
    'allspaces.revenue.modal.title': '分成比例确认',
    'allspaces.revenue.modal.subtitle': '请确认您申请加入「{spaceName}」的分成比例分配',
    'allspaces.revenue.current.distribution': '分成比例分配',
    'allspaces.revenue.your.proposed.role': '您的提议角色',
    'allspaces.revenue.total': '总计',
    'allspaces.revenue.modal.hint': '您可以调整分成比例，确保总计为100%。此比例将作为您的申请提案发送给创建者审核。',
    'allspaces.revenue.modal.cancel': '取消',
    'allspaces.revenue.modal.apply': '提交申请',
    'allspaces.revenue.modal.success': '申请已提交！您的分成比例提案将发送给创建者审核',
    'allspaces.revenue.modal.error.total': '分成比例总计为{total}%，请调整为100%',
    
    // CreativeCenter作品翻译
    'creative.work.nocturne': '夜曲',
    'creative.work.spring.story': '春天的故事',
    'creative.work.untitled': '未命名作品',
    'creative.genre.classical': '古典',
    'creative.genre.folk': '民谣',
    'creative.genre.electronic': '电子',
    
    // CreativeCenter协作翻译
    'creative.collab.dream.concerto': '梦想协奏曲',
    'creative.collab.youth.memories': '青春回忆',
    'creative.role.producer': '音乐制作人',
    'creative.role.pianist': '钢琴家',
    'creative.role.violinist': '小提琴手',
    'creative.role.arranger': '编曲',
    'creative.role.singer': '创作歌手',
    'creative.role.guitarist': '吉他手',
    'creative.role.lyricist': '作词',
    
    // CreativeCenter播放列表翻译
    'creative.playlist.my.originals': '我的原创',
    'creative.playlist.favorite.classical': '喜欢的古典',
    'creative.playlist.relaxing.time': '放松时光',
    'allspaces.invite.modal.title': '邀请成员',
    'allspaces.invite.role': '角色',
    'allspaces.invite.role.select': '选择角色',
    'allspaces.invite.role.lyrics': '作词',
    'allspaces.invite.role.compose': '作曲',
    'allspaces.invite.role.arrange': '编曲',
    'allspaces.invite.role.sing': '歌手',
    'allspaces.invite.role.mix': '混音',
    'allspaces.invite.message': '邀请消息',
    'allspaces.share.social': '分享到社交媒体',
    'allspaces.share.wechat': '微信',
    'allspaces.share.weibo': '微博',
    'allspaces.share.qq': 'QQ',
    'allspaces.share.copy': '复制链接',
    'allspaces.discussion.modal.title': '发起新讨论',
    'allspaces.discussion.modal.title.label': '讨论标题',
    'allspaces.discussion.modal.title.placeholder': '请输入讨论标题',
    'allspaces.discussion.modal.content.label': '讨论内容',
    'allspaces.discussion.modal.content.placeholder': '描述你想讨论的内容...',
    'allspaces.discussion.modal.start': '发起讨论',
    'allspaces.discussion.started': '讨论已发起！',
    
    // Creative Center translations
    'creative.my.works': '我创作的作品',
    'creative.my.collaborations': '我参与的协作',
    'creative.my.playlists': '我的歌单',
    'creative.my.favorites': '我的收藏',
    'creative.upload.work': '上传新作品',
    'creative.join.collaboration': '加入协作',
    'creative.create.playlist': '创建歌单',
    'creative.published': '已发布',
    'creative.draft': '草稿',
    'creative.edit': '编辑',
    'creative.classical': '古典',
    'creative.folk': '民谣',
    'creative.electronic': '电子',
    'creative.collaboration.members': '协作成员',
    'creative.my.role': '我的角色',
    'creative.arrangement': '编曲',
    'creative.lyrics': '作词',
    'creative.completed': '已完成',
    'creative.in.progress': '进行中',
    'creative.progress': '进度',
    'creative.deadline': '截止',
    'creative.songs.count': '首歌曲',
    'creative.public': '公开',
    'creative.private': '私密',
    'creative.created.at': '创建于',
    'creative.total.songs': '共',
    'creative.added.at': '收藏于',
    'creative.playlist.my.original': '我的原创',
    'creative.playlist.classical.favorites': '喜欢的古典',
    'creative.playlist.relaxation': '放松时光',
    'creative.favorite.moonlight.sonata': '月光奏鸣曲',
    'creative.favorite.jasmine.flower': '茬莉花',
    'creative.artist.beethoven': '贝多芬',
    'creative.artist.folk.music': '民间音乐',
    'creative.upload.new.work': '上传新作品',
    'creative.songs': '首歌曲',
    'creative.created.on': '创建于',
    'creative.total': '共',
    'creative.favorited.on': '收藏于',
    
    // MyHomepage specific translations
    'myhomepage.cover.add': '添加封面照片',
    'myhomepage.cover.alt': '封面',
    'myhomepage.publish.news': '发布快讯',
    'myhomepage.edit.profile': '编辑资料',
    'myhomepage.personal.profile': '个人资料',
    'myhomepage.add.signature': '添加个性名',
    'myhomepage.edit.details': '编辑详细信息',
    'myhomepage.add.featured': '添加精选内容',
    'myhomepage.works.title': '作品',
    'myhomepage.all.works': '所有作品',
    'myhomepage.no.works': '暂无作品',
    'myhomepage.friends.title': '好友',
    'myhomepage.all.friends': '全部好友',
    'myhomepage.no.friends': '暂无好友',
    'myhomepage.privacy.policy': '隐私政策',
    'myhomepage.terms.service': '服务条款',
    'myhomepage.advertising': '广告',
    'myhomepage.ad.choices': 'Ad Choices',
    'myhomepage.cookies': 'Cookie',
    'myhomepage.more': '更多',
    'myhomepage.copyright': 'iBOM © 2025',
    'myhomepage.share.music': '好的音乐要分享',
    'myhomepage.introduce.yourself': '介绍一下自己',
    'myhomepage.public': '公开',
    'myhomepage.cancel': '取消',
    'myhomepage.save': '保存',
    'myhomepage.updated.status': '更新了状态',
    'myhomepage.like.action': '赞',
    'myhomepage.comment.action': '评论',
    'myhomepage.send.action': '发送',
    'myhomepage.share.action': '分享',
    'myhomepage.write.comment': '写评论...',
    'myhomepage.support.cv.link': '支持生成CV链接',
    'myhomepage.avatar.alt': '头像',
    
    // PersonalProfile specific translations
    'profile.basic.info': '基本信息',
    'profile.certification': '认证信息',
    'profile.binding': '账户绑定',
    'profile.security': '账户安全',
    'profile.preferences': '偏好设置',
    'profile.demo.user': '演示用户',
    'profile.normal.user': '普通用户',
    'profile.verified': '实名认证',
    'profile.real.name': '真实姓名',
    'profile.gender': '性别',
    'profile.male': '男',
    'profile.female': '女',
    'profile.secret': '保密',
    'profile.birth.date': '出生日期',
    'profile.location': '所在地区',
    'profile.bio': '个人简介',
    'profile.bio.demo': '这是一个演示用户的个人简介。热爱音乐，喜欢创作，经常参与协作项目。',
    'profile.music.style': '音乐风格偏好',
    'profile.style.pop': '流行',
    'profile.style.classical': '古典',
    'profile.style.rock': '摇滚',
    'profile.style.jazz': '爵士',
    'profile.style.electronic': '电子',
    'profile.style.folk': '民谣',
    'profile.style.hiphop': '嘻哈',
    'profile.style.country': '乡村',
    'profile.save.changes': '保存更改',
    'profile.cert.description': '认证说明',
    'profile.cert.benefit': '完成认证可以获得更多权限和信任度',
    'profile.real.name.cert': '实名认证',
    'profile.musician.cert': '音乐人认证',
    'profile.company.cert': '企业认证',
    'profile.certified': '已认证',
    'profile.apply.cert': '申请认证',
    'profile.cert.time': '认证时间：2024-01-01',
    'profile.musician.cert.desc': '音乐人认证可以获得专业标识和更多曝光机会',
    'profile.musician.cert.requires': '需要提供：',
    'profile.musician.works': '音乐作品集',
    'profile.musician.id': '身份证明',
    'profile.musician.certificates': '音乐相关证书或证明',
    'profile.company.cert.desc': '企业认证适用于音乐公司、工作室等机构',
    'profile.company.requires': '需要提供：',
    'profile.business.license': '营业执照',
    'profile.company.intro': '企业介绍',
    'profile.legal.person.id': '法人身份证明',
    'profile.binding.tip.title': '绑定提示',
    'profile.binding.tip.desc': '绑定第三方账户可以快速登录和分享',
    'profile.wechat': '微信',
    'profile.wechat.bound': '已绑定：wx****8888',
    'profile.qq': 'QQ',
    'profile.github': 'GitHub',
    'profile.weibo': '微博',
    'profile.not.bound': '未绑定',
    'profile.bind': '绑定',
    'profile.unbind': '解绑',
    'profile.security.tip.title': '安全提醒',
    'profile.security.tip.desc': '请定期更新密码，保护账户安全',
    'profile.login.password': '登录密码',
    'profile.password.modified': '最后修改：2024-01-01',
    'profile.change.password': '修改密码',
    'profile.phone.number': '手机号码',
    'profile.change.phone': '更换手机',
    'profile.email.address': '邮箱地址',
    'profile.change.email': '更换邮箱',
    'profile.two.factor': '两步验证',
    'profile.two.factor.disabled': '未启用',
    'profile.enable': '启用',
    'profile.interface.settings': '界面设置',
    'profile.dark.mode': '深色模式',
    'profile.font.size': '字体大小',
    'profile.font.small': '小',
    'profile.font.medium': '中',
    'profile.font.large': '大',
    'profile.language': '语言',
    'profile.chinese': '中文',
    'profile.english': 'English',
    'profile.music.playback': '音乐播放',
    'profile.auto.play': '自动播放',
    'profile.audio.quality': '音质偏好',
    'profile.quality.standard': '标准',
    'profile.quality.high': '高品质',
    'profile.quality.lossless': '无损',
    'profile.volume': '音量',
    'profile.privacy.settings': '隐私设置',
    'profile.profile.visibility': '个人资料可见性',
    'profile.visibility.everyone': '所有人',
    'profile.visibility.friends': '好友',
    'profile.visibility.self': '仅自己',
    'profile.allow.private.messages': '允许私信',
    'profile.show.online.status': '显示在线状态',
    'profile.save.settings': '保存设置',
    
    // Rewards translations
    'rewards.overview': '奖励总览',
    'rewards.earn.methods': '快速获取方式',
    'rewards.daily.checkin': '每日签到',
    'rewards.invite.friends': '邀请好友',
    'rewards.history': '奖励明细',
    'rewards.rules': '查看规则',
    'rewards.total.points': '总积分',
    'rewards.gold.coins': '金币',
    'rewards.crystal.coins': '水晶币',
    'rewards.level.progress': '等级进度',
    'rewards.current.level': '当前等级',
    'rewards.need.points': '还需',
    'rewards.days': '天',
    'rewards.day': '第',
    'rewards.points.needed': '还需',
    'rewards.points.to.next': '积分',
    'rewards.checkin.stats': '签到统计',
    'rewards.consecutive.days': '连续签到天数',
    'rewards.invite.stats': '邀请统计',
    'rewards.successful.invites': '成功邀请好友',
    'rewards.quick.earn.points': '快速获取积分方式',
    'rewards.daily.checkin.desc': '连续签到获得更多奖励',
    'rewards.upload.work': '上传作品',
    'rewards.upload.work.desc': '发布原创音乐作品',
    'rewards.invite.friend.desc': '邀请朋友注册并认证',
    'rewards.complete.profile': '完善资料',
    'rewards.complete.profile.desc': '完成个人资料认证',
    'rewards.go.complete': '去完成',
    'rewards.consecutive.checkin': '连续签到',
    'rewards.checkin.now': '立即签到',
    'rewards.already.checked': '今日已签到',
    'rewards.type': '类型',
    'rewards.description': '描述',
    'rewards.points': '积分',
    'rewards.coins': '金币',
    'rewards.crystals': '水晶币',
    'rewards.time': '时间',
    'rewards.checkin': '签到',
    'rewards.upload': '上传',
    'rewards.invite': '邀请',
    'rewards.level.up': '升级',
    'rewards.invite.friends.title': '邀请好友',
    'rewards.invite.desc': '邀请好友注册并完成认证，你和朋友都可以获得丰厚奖励！',
    'rewards.invite.reward': '邀请奖励',
    'rewards.invite.link': '你的邀请链接：',
    'rewards.copy': '复制',
    'rewards.successful.invites.count': '成功邀请',
    'rewards.earned.points': '获得积分',
    'rewards.earn.rules': '奖励规则',
    'rewards.points.rules': '积分获取规则',
    'rewards.coins.rules': '金币获取规则',
    'rewards.crystals.rules': '水晶币获取规则',
    'rewards.notes': '注意事项',
    
    // Wallet translations
    'wallet.balance.display': '余额显示',
    'wallet.transaction.details': '消费明细',
    'wallet.purchase.history': '购买记录',
    'wallet.gold.balance': '金币余额',
    'wallet.gold.usage': '可用于购买会员、道具等',
    'wallet.crystal.balance': '水晶币余额',
    'wallet.crystal.usage': '可提现到银行账户',
    'wallet.purchase.coins': '购买金币',
    'wallet.withdraw.crystals': '水晶币提现',
    'wallet.transaction.history': '交易记录',
    'wallet.spending.stats': '消费统计',
    'wallet.monthly.spending': '本月消费',
    'wallet.monthly.income': '本月收入',
    'wallet.usage.guide': '使用说明',
    'wallet.all.types': '全部类型',
    'wallet.purchase': '购买',
    'wallet.spending': '消费',
    'wallet.income': '收入',
    'wallet.withdraw': '提现',
    'wallet.all.currencies': '全部币种',
    'wallet.gold.coin': '金币',
    'wallet.crystal.coin': '水晶币',
    'wallet.amount': '数量',
    'wallet.status': '状态',
    'wallet.completed': '已完成',
    'wallet.pending': '处理中',
    'wallet.purchase.amount': '数量',
    'wallet.currency.type': '类型',
    'wallet.price': '金额',
    'wallet.payment.method': '支付方式',
    'wallet.wechat.pay': '微信支付',
    'wallet.alipay': '支付宝',
    'wallet.payment.success': '支付成功',
    
    // MyWallet 完整翻译
    'wallet.tab.balance': '余额',
    'wallet.tab.transactions': '交易记录', 
    'wallet.tab.purchase.history': '购买记录',
    'wallet.gold.description': '用于购买会员、道具等',
    'wallet.crystal.description': '可提现到银行账户',
    'wallet.crystal.withdraw': '提现',
    'wallet.purchase.gold': '购买金币',
    'wallet.currency.gold': '金币',
    'wallet.currency.crystal': '水晶币',
    'wallet.filter.all.types': '全部类型',
    'wallet.filter.all.currencies': '全部币种',
    'wallet.type.purchase': '购买',
    'wallet.type.spend': '消费',
    'wallet.type.earn': '收入',
    'wallet.type.withdraw': '提现',
    'wallet.status.completed': '已完成',
    'wallet.status.pending': '处理中',
    'wallet.status.payment.success': '支付成功',
    'wallet.table.description': '描述',
    'wallet.table.amount': '数量',
    'wallet.table.type': '类型',
    'wallet.table.status': '状态',
    'wallet.table.time': '时间',
    'wallet.table.price': '金额',
    'wallet.table.payment.method': '支付方式',
    'wallet.payment.wechat': '微信支付',
    'wallet.payment.alipay': '支付宝',
    'wallet.spending.statistics': '消费统计',
    'wallet.transaction.purchase.gold': '购买金币',
    'wallet.transaction.purchase.vip': '购买VIP会员',
    'wallet.transaction.purchase.crystal': '购买水晶币',
    'wallet.transaction.reward.income': '奖励收入',
    'wallet.transaction.crystal.withdraw': '水晶币提现',
    'wallet.transactions': '交易记录',
    'wallet.usage.gold.description': '金币可用于购买VIP会员、虚拟道具等',
    'wallet.usage.crystal.earn': '通过创作、合作等方式获得水晶币',
    'wallet.usage.crystal.withdraw': '水晶币可提现到银行账户',
    'wallet.usage.transaction.view': '查看详细交易记录',
    
    // Certification 完整翻译
    'certification.artist.title': '艺人认证',
    'certification.user.title': '用户认证',
    'certification.qualification.title': '资质证明',
    'certification.artist.description': '艺人认证适用于音乐人、歌手、制作人等音乐专业人士。认证后可获得专业标识，享受更多平台权益。',
    'certification.user.description': '用户认证适用于普通用户，完成认证后可获得更多平台功能权限。',
    'certification.qualification.description': '上传相关资质证明文件，有助于提升认证通过率和账户权威性。',
    'certification.artist.info': '艺人信息',
    'certification.user.info': '用户信息',
    'certification.artist.stageName': '艺名/真名',
    'certification.artist.stageNamePlaceholder': '请输入艺名或真实姓名',
    'certification.realName': '真实姓名',
    'certification.realNamePlaceholder': '请输入真实姓名',
    'certification.idNumber': '身份证号',
    'certification.idNumberPlaceholder': '请输入身份证号码',
    'certification.phone': '联系电话',
    'certification.phonePlaceholder': '请输入联系电话',
    'certification.mobile': '手机号码',
    'certification.mobilePlaceholder': '请输入手机号码',
    'certification.email': '邮箱地址',
    'certification.emailPlaceholder': '请输入邮箱地址',
    'certification.birthDate': '出生日期',
    'certification.gender': '性别',
    'certification.genders.male': '男',
    'certification.genders.female': '女',
    'certification.genders.other': '其他',
    'certification.selectGender': '请选择性别',
    'certification.occupation': '职业',
    'certification.occupationPlaceholder': '请输入职业',
    'certification.address': '通讯地址',
    'certification.addressPlaceholder': '请输入通讯地址',
    'certification.musicType': '音乐类型',
    'certification.selectMusicType': '请选择音乐类型',
    'certification.musicTypes.pop': '流行音乐',
    'certification.musicTypes.rock': '摇滚音乐',
    'certification.musicTypes.jazz': '爵士音乐',
    'certification.musicTypes.classical': '古典音乐',
    'certification.musicTypes.electronic': '电子音乐',
    'certification.musicTypes.folk': '民谣音乐',
    'certification.musicTypes.hiphop': '嘻哈音乐',
    'certification.musicTypes.country': '乡村音乐',
    'certification.musicTypes.other': '其他',
    'certification.experience': '从业年限',
    'certification.selectExperience': '请选择从业年限',
    'certification.experienceYears.0-1': '0-1年',
    'certification.experienceYears.1-3': '1-3年',
    'certification.experienceYears.3-5': '3-5年',
    'certification.experienceYears.5-10': '5-10年',
    'certification.experienceYears.10+': '10年以上',
    'certification.bio': '个人简介',
    'certification.bioPlaceholder': '请简要介绍您的音乐经历和专长',
    'certification.majorWorks': '主要作品',
    'certification.majorWorksPlaceholder': '请列举您的主要音乐作品',
    'certification.awards': '获奖情况',
    'certification.awardsPlaceholder': '请描述获奖经历（如无可不填）',
    'certification.personalPhoto': '个人照片',
    'certification.idCardFront': '身份证正面',
    'certification.idCardBack': '身份证反面',
    'certification.idCardClear': '请确保身份证照片清晰',
    'certification.workProof': '作品证明',
    'certification.workProofDescription': '上传能证明您作品的相关文件',
    'certification.education': '学历证明',
    'certification.educationDescription': '上传学历证书或相关教育背景证明',
    'certification.certificate': '专业证书',
    'certification.certificateDescription': '上传专业技能证书或资质证明',
    'certification.awardCertificate': '获奖证书',
    'certification.awardDescription': '上传获奖证书或相关荣誉证明',
    'certification.copyrightProof': '作品版权证明',
    'certification.copyrightDescription': '上传作品版权登记证明或相关文件',
    'certification.mediaReport': '媒体报道',
    'certification.mediaDescription': '上传媒体报道截图或链接',
    'certification.otherProof': '其他证明',
    'certification.otherDescription': '上传其他相关证明材料',
    'certification.additionalInfo': '补充说明',
    'certification.additionalInfoPlaceholder': '如有需要，请补充说明相关情况',
    'certification.uploadPersonalPhoto': '点击上传个人照片',
    'certification.uploadIdCardFront': '点击上传身份证正面',
    'certification.uploadIdCardBack': '点击上传身份证反面',
    'certification.uploadWorkProof': '点击上传作品证明',
    'certification.uploadEducation': '点击上传学历证明',
    'certification.uploadCertificate': '点击上传专业证书',
    'certification.uploadAwardCertificate': '点击上传获奖证书',
    'certification.uploadCopyrightProof': '点击上传版权证明',
    'certification.uploadMediaReport': '点击上传媒体报道',
    'certification.uploadOtherProof': '点击上传其他证明',
    'certification.photoFormat': '支持JPG、PNG格式，文件大小不超过5MB',
    'certification.benefits': '认证权益',
    'certification.benefitsList.verification': '获得认证标识，提升账户可信度',
    'certification.benefitsList.features': '享受更多平台功能和服务',
    'certification.benefitsList.events': '优先参与平台活动和推广',
    'certification.benefitsList.security': '账户安全性和权限提升',
    'certification.notes': '注意事项',
    'certification.notesList.authentic': '请确保提供的信息真实有效',
    'certification.notesList.clarity': '上传的图片请保持清晰可读',
    'certification.notesList.formats': '支持常见图片格式（JPG、PNG等）',
    'certification.notesList.fileSize': '单个文件大小不超过5MB',
    'certification.notesList.quality': '照片质量越高，审核通过率越高',
    'certification.reviewNotice': '审核说明',
    'certification.reviewRules.timeframe': '审核时间：通常在3-7个工作日内完成',
    'certification.reviewRules.truthful': '请确保提供信息真实有效，虚假信息将被拒绝',
    'certification.reviewRules.contact': '如有问题，可通过客服联系我们',
    'certification.reviewRules.badge': '认证通过后将获得相应的认证标识',
    'certification.submit': '提交认证申请',
    
    // Homepage Community Section
    'homepage.stats.creators': '音乐创作者',
    'homepage.stats.works': '音乐作品',
    'homepage.stats.projects': '协作项目',
    'homepage.stats.events': '每月活动',
    'homepage.join.now': '立即加入',
    'homepage.form.name.placeholder': '你的艺名或真实姓名',
    'homepage.form.email.placeholder': '邮箱地址',
    'homepage.form.style.placeholder': '选择你的音乐风格',
    'homepage.form.style.pop': '流行音乐',
    'homepage.form.style.rock': '摇滚音乐',
    'homepage.form.style.electronic': '电子音乐',
    'homepage.form.style.jazz': '爵士音乐',
    'homepage.form.style.classical': '古典音乐',
    'homepage.form.style.hiphop': '嘻哈音乐',
    
    // Footer
    'footer.description': '专业的音乐协创社交平台，连接全球音乐人',
    'footer.products': '产品',
    'footer.products.collaboration': '协创空间',
    'footer.products.daw': 'DAW工具',
    'footer.products.live': '直播功能',
    'footer.products.ai': 'AI助手',
    'footer.community': '社区',
    'footer.community.musicians': '音乐人',
    'footer.community.works': '作品展示',
    'footer.community.events': '活动中心',
    'footer.community.help': '帮助中心',
    'footer.about': '关于我们',
    'footer.about.company': '公司介绍',
    'footer.about.contact': '联系我们',
    'footer.about.privacy': '隐私政策',
    'footer.about.terms': '服务条款',
    'footer.copyright': '© 2024 iBOM. All rights reserved.',
    
    // Common
    'common.home': '首页',
    'common.search': '搜索',
    'common.filter': '筛选',
    'common.sort': '排序',
    'common.save': '保存',
    'common.cancel': '取消',
    'common.confirm': '确认',
    'common.edit': '编辑',
    'common.delete': '删除',
    'common.view': '查看',
    'common.more': '更多',
    'common.loading': '加载中...',
    'common.error': '出错了',
    'common.success': '成功',
    'common.back': '返回',
    'common.next': '下一步',
    'common.previous': '上一步',
    
    // Language
    'lang.chinese': '中文',
    'lang.english': 'English',
    
    // Time
    'time.now': '刚刚',
    'time.minutes.ago': '分钟前',
    'time.hours.ago': '小时前',
    'time.days.ago': '天前',
    'time.weeks.ago': '周前',
    'time.months.ago': '月前',
    
    // Mock Work Titles
    'mock.work.title.summer.breeze': '夏日微风',
    'mock.work.title.city.lights': '城市之光',
    'mock.work.title.between.mountains.sea': '山海之间',
    'mock.work.title.starry.walk': '星空海步',
    'mock.work.title.city.rhythm': '城市节奏',
    'mock.work.title.deep.sea.sound': '深海之音',
    'mock.work.title.galaxy.travel': '星河漫游',
    'mock.work.title.mirage': '海市蜃楼',
    'mock.work.title.night.sky.wish': '夜空的愿望',
    'mock.work.title.spring.story': '春天的故事',
    'mock.work.title.city.nightscape': '城市夜景',
    
    // Mock Work Descriptions
    'mock.work.desc.summer.breeze': '轻快悦耳的夏日恋歌，拥有令人回味的旋律',
    'mock.work.desc.city.lights': '描述现代都市夜晚的电子音乐作品',
    'mock.work.desc.between.mountains.sea': '融合民族风情的创作，展现大自然的壮美',
    'mock.work.desc.starry.walk': '梦幻般的海岸音乐作品',
    'mock.work.desc.city.rhythm': '无声的城市节奏，反映现代生活',
    'mock.work.desc.deep.sea.sound': '沉浸式的环境音乐，带来宁静的感受',
    'mock.work.desc.galaxy.travel': '一首充满梦幻色彩的音乐作品，融合了现代流行元素与传统音乐的精髓，带给听众全新的听觉体验。',
    
    // Mock Artist Names
    'mock.artist.chen.xiaowei': '陈晓伟',
    'mock.artist.marco': '马可',
    'mock.artist.wang.xiaoming': '王小明',
    'mock.artist.li.shiyu': '李诗雨',
    'mock.artist.zhang.sanhe': '张三和',
    'mock.artist.zhao.liu': '赵六',
    'mock.artist.qian.qi': '钱七',
    'mock.artist.sun.ba': '孙八',
    'mock.artist.li.si': '李四',
    'mock.artist.wang.wu': '王五',
    'mock.artist.tianyu.music': '天羽音乐',
    'mock.artist.yuan.yuxi': '原雨夕',
    'mock.artist.yang.yuming': '杨宇明',
    'mock.artist.xiaoyu.music': '小雨音乐',
    'mock.artist.electronic.band': '电子乐队',
    'mock.artist.zhang.meiqi': '张美琪',
    'mock.artist.wang.zhiqiang': '王志强',
    
    // Mock Genres
    'mock.genre.pop': '流行',
    'mock.genre.electronic': '电子',
    'mock.genre.folk': '民族',
    'mock.genre.light.music': '轻音乐',
    'mock.genre.hiphop': '嘻哈',
    'mock.genre.ambient': '环境音乐',
    'mock.genre.healing': '治愈',
    'mock.genre.original': '原创',
    
    // Mock Studio Names
    'mock.studio.tianyu': '天羽音乐工作室',
    'mock.label.ibom': 'iBOM Music',
    
    // Mock Comment Content
    'mock.comment.1': '旋律太美了，无限循环中，非常有感觉的一首歌！期待更多作品。',
    'mock.comment.2': '这首歌真的是太棒了！音乐制作的质量非常高，每个细节都处理得很到位。听了好多遍都不腻，强烈推荐给大家！',
    
    // Mock User Names
    'mock.user.me': '我',
    
    // NFT Related
    'nft.digital.collectible': 'NFT 数字藏品',
    'nft.limited.edition': '限量发行',
    'nft.id': 'NFT ID',
    'nft.token.id': 'Token ID',
    'nft.price': '发行价格',
    'nft.release.time': '发行时间',
    'nft.buy.now': '立即购买 NFT',
    'nft.buy.alert': '购买NFT功能正在开发中...',
    'nft.buy.work': '作品',
    'nft.buy.price': '价格',
    'nft.buy.coming.soon': '敬请期待！',
  },
  en: {
    // Navigation
    'nav.musicians': 'Musicians',
    'nav.collaboration': 'Collaboration',
    'nav.works': 'Works',
    'nav.investment': 'Investment',
    'nav.publishing': 'Publishing',
    'nav.chat': 'Chat',
    'nav.notifications': 'Notifications',
    'nav.profile': 'Profile',
    'nav.logout': 'Logout',
    'nav.login': 'Login',
    'nav.register': 'Register',
    
    // Account Sidebar
    'account.title': 'Account Center',
    'account.homepage': 'My Homepage',
    'account.homepage.desc': 'Personal showcase page',
    'account.info': 'Account Info',
    'account.info.desc': 'Basic account settings',
    'account.profile': 'Profile',
    'account.profile.desc': 'Detailed personal information',
    'account.messages': 'Messages',
    'account.messages.desc': 'Messages and notifications',
    'account.creative': 'Creative Center',
    'account.creative.desc': 'My creative content',
    'account.rewards': 'My Rewards',
    'account.rewards.desc': 'Points and rewards',
    'account.wallet': 'My Wallet',
    'account.wallet.desc': 'Asset management',
    'account.certification': 'Certification',
    'account.certification.desc': 'Identity verification',
    
    // Homepage
    'homepage.title': 'Make Music Creation More Meaningful',
    'homepage.subtitle': '10,000+ music creators are here, sharing, learning, collaborating, and growing',
    'homepage.explore': 'Create Works Without Limits',
    'homepage.collaboration': 'Start Collaboration Journey',
    'homepage.featured.title': 'Featured Content',
    'homepage.musicians.title': 'Popular Musicians',
    'homepage.works.title': 'Featured Works',
    'homepage.live.title': 'Live Now',
    'homepage.discover.title': 'Discover Creative Spaces',
    'homepage.discover.subtitle': 'Explore unlimited creativity, join collaborative projects',
    'homepage.view.all': 'View All',
    'homepage.trending.musicians': 'Trending Musicians',
    'homepage.trending.musicians.subtitle': 'Discover talented music creators',
    'homepage.featured.works': 'Featured Works',
    'homepage.featured.works.subtitle': 'Latest and hottest music works',
    'homepage.live.streams': 'Live Streams',
    'homepage.live.streams.subtitle': 'Real-time music performances and creation',
    'homepage.join.community': 'Join Our Music Community',
    'homepage.connect.creators': 'Connect with global music creators',
    'homepage.start.journey': 'Start Your Musical Journey',
    'homepage.register.now': 'Register Now',
    'homepage.login.now': 'Login Now',
    'homepage.space.electronic.title': 'Electronic Music Lab',
    'homepage.space.electronic.desc': 'Explore the infinite possibilities of electronic music, collaborate with global producers to create unique sounds',
    'homepage.space.electronic.category': 'Electronic',
    'homepage.space.folk.title': 'Folk Creation Society',
    'homepage.space.folk.desc': 'Tell life stories with simple melodies, find like-minded musical partners',
    'homepage.space.folk.category': 'Folk',
    'homepage.space.hiphop.title': 'Hip-Hop Studio',
    'homepage.space.hiphop.desc': 'Express attitude with beats, convey power with rhythm, showcase the charm of hip-hop culture',
    'homepage.space.hiphop.category': 'Hip-Hop',
    'homepage.space.members': ' members',
    'homepage.quality.works': 'Quality Works',
    'homepage.quality.works.subtitle': 'Featured popular music works from the community',
    'homepage.work.summer.breeze': 'Summer Breeze',
    'homepage.work.electronic.dream': 'Electronic Dream',
    'homepage.work.city.night': 'City Night',
    'homepage.work.starry.tale': 'Starry Tale',
    'homepage.artist.chen.ma': 'Chen Xiaoyin x Marco',
    'homepage.artist.dj.xiaoyu': 'DJ Xiaoyu',
    'homepage.artist.wang.li': 'Wang Xiaoming x Li Shiyu',
    'homepage.artist.zhang.sophie': 'Zhang Xiaoya x Sophie',
    'homepage.recent.activities': 'Recent Activities',
    'homepage.recent.activities.subtitle': 'Participate in exciting activities and showcase your talent',
    'homepage.activity.spring.festival': 'Spring Music Festival',
    'homepage.activity.spring.festival.desc': 'Gather excellent musicians from around the world to create a musical feast showcasing diverse music styles',
    'homepage.activity.indie.masterclass': 'Independent Masterclass',
    'homepage.activity.indie.masterclass.desc': 'Renowned music producers teach in person, sharing production experience and creative techniques',
    'homepage.activity.youth.contest': 'Youth Music Contest',
    'homepage.activity.youth.contest.desc': 'Provide a platform for young musicians to showcase their talents and discover new music stars',
    'homepage.activity.status.enrolling': 'Enrolling',
    'homepage.activity.status.starting.soon': 'Starting Soon',
    'homepage.activity.status.hot.signup': 'Hot Signup',
    'homepage.activity.join.now': 'Join Now',
    'homepage.activity.time': 'Event Time: ',
    'homepage.activity.signup': 'Sign Up Now',
    'homepage.community.desc1': 'Connect with 10,000+ music creators worldwide',
    'homepage.community.desc2': 'Share creative inspiration, learn professional skills, showcase your talent',
    'homepage.community.desc3': 'Whether you are a beginner or a professional musician, this is your stage',
    'homepage.community.main.desc': 'Become part of the global music creator network, work with like-minded musicians to create musical works of this era',
    
    // Collaboration
    'collab.title': 'Collaboration Spaces',
    'collab.subtitle': 'Find like-minded musical partners and start your collaborative journey',
    'collab.all.spaces': 'All Collaboration Spaces',
    'collab.my.spaces': 'My Collaboration Spaces',
    'collab.create': 'Create Space',
    'collab.create.space': 'Create Collaboration Space',
    'collab.join': 'Apply to Join',
    'collab.members': 'Members',
    'collab.status.recruiting': 'Recruiting',
    'collab.status.ongoing': 'Ongoing',
    'collab.status.completed': 'Completed',
    'collab.load.more': 'Load More Collaboration Spaces',
    'collab.created.spaces': 'My Created Spaces',
    'collab.joined.spaces': 'My Joined Spaces',
    'collab.created.daw': 'My Created DAW Projects',
    'collab.joined.daw': 'My Joined DAW Projects',
    'collab.signed.contracts': 'My Signed Contracts',
    'collab.pending.contracts': 'Pending Contracts',
    'collab.copyright.contracts': 'My Created Copyright Contracts',
    'collab.manage.created': 'Manage all your created collaboration spaces',
    'collab.view.joined': 'View collaboration spaces you participate in',
    'collab.manage.daw.created': 'Manage your created DAW music production projects',
    'collab.view.daw.joined': 'View DAW music production projects you participate in',
    'collab.view.signed.contracts': 'View signed contracts',
    'collab.view.pending.contracts': 'View pending contracts',
    'collab.manage.copyright.contracts': 'Manage your created copyright contracts',
    'collab.login.required': 'Please login first to create collaboration space',
    
    // My Collaboration Spaces
    'collab.tabs.discussion': 'Discussion',
    'collab.tabs.activity': 'Activity',
    'collab.tabs.media': 'Media',
    'collab.tabs.members': 'Members',
    'collab.invite.members': 'Invite Members',
    'collab.share.space': 'Share Space',
    'collab.publish.work': 'Publish Work',
    'collab.create.nft': 'Create NFT',
    'collab.members.count': 'Members',
    'collab.recent.activity': 'Recent Activity',
    'collab.hours.ago': 'hours ago',
    'collab.days.ago': 'days ago',
    'collab.discussion.posted': 'posted in discussion',
    'collab.discussion.participated': 'participated in online discussion',
    'collab.no.discussion': 'No discussion content yet, start your first message!',
    'collab.no.activity': 'No activity records',
    'collab.no.media': 'No media files',
    'collab.members.loading': 'Loading member list...',
    'collab.my.role': 'My Role',
    
    // Collaboration Space Mock Data - English
    'collab.space.pop.title': 'Original Pop Song Production',
    'collab.space.pop.desc': 'Looking for excellent lyricists and singers to create beautiful pop songs together',
    'collab.space.folk.title': 'Folk Guitar Ensemble Project',
    'collab.space.folk.desc': 'Hope to find like-minded folk music lovers to create warm music together',
    'collab.space.electronic.title': 'Electronic Music Lab',
    'collab.space.electronic.desc': 'Explore cutting-edge electronic music production technology and create unique sound experiences',
    'collab.space.ancient.title': 'Ancient Style Music Studio',
    'collab.space.ancient.desc': 'Inherit classics, integrate modernity, create music works with Chinese characteristics',
    'collab.creator.wang': 'Music Producer Wang',
    'collab.creator.dj.alex': 'DJ_Alex',
    'collab.creator.ancient': 'Ancient Melody Studio',
    'collab.creator.zhao': 'Lyricist Zhao',
    'collab.creator.chen': 'Guitarist Chen',
    'collab.creator.liu': 'Singer Liu',
    'collab.creator.arranger': 'Arranger Li',
    'collab.creator.mixer': 'Mixer Zhang',
    'collab.creator.guitarist': 'Guitarist Chen',
    'collab.creator.singer': 'Singer Liu',
    'collab.creator.lyricist': 'Lyricist Li',
    'collab.creator.vocalist': 'Vocalist Zhang',
    'collab.creator.mixing.engineer': 'Mixing Engineer Chen',
    'collab.role.arrangement': 'Arrangement',
    'collab.role.lyrics': 'Lyrics',
    'collab.role.vocals': 'Vocals',
    'collab.role.mixing': 'Mixing',
    'collab.role.creator': 'Creator',
    'collab.activity.melody': 'published new melody segments',
    'collab.activity.recording': 'uploaded new recording files',
    'collab.activity.discussion': 'posted opinions in discussion',
    'collab.activity.participated': 'participated in online discussion',
    'collab.activity.created': 'just created collaboration space',
    'collab.time.hours.ago': 'hours ago',
    'collab.time.days.ago': 'days ago',
    'collab.time.yesterday': 'yesterday',
    'collab.privacy.public': 'Public',
    'collab.privacy.private': 'Private',
    'collab.privacy.searchable': 'Searchable',
    'collab.creator.label': 'Creator',
    'collab.source.space': 'Source space',
    'collab.contract.signed': 'Signed',
    'collab.publish': 'Publish',
    'collab.publish.progress': 'Publishing...',
    'collab.invite': 'Invite',
    'collab.sign.contract': 'Sign Contract',
    'collab.daw': 'DAW',
    'collab.copyright.contract': 'Copyright Contract',
    'collab.nft.create': 'NFT',
    'collab.nft.creating': 'Creating...',
    'collab.my.spaces.title': 'My Collaboration Spaces',
    'collab.created.spaces.title': 'My Created Collaboration Spaces',
    'collab.joined.spaces.title': 'My Joined Collaboration Spaces',
    'collab.signed.contracts.title': 'My Signed Contracts',
    'collab.pending.contracts.title': 'Pending Contracts',
    'collab.copyright.contracts.title': 'My Created Copyright Contracts',
    'collab.space.count': '',
    'collab.contract.count': '',
    'collab.contracts.signed': 'Signed',
    'collab.contracts.pending': 'Pending',
    'collab.contract.parties': 'Parties',
    'collab.contract.signed.date': 'Signed Date',
    'collab.contract.status': 'Status',
    'collab.contract.status.active': 'Active',
    'collab.contract.status.pending': 'Pending',
    'collab.contract.created.date': 'Created Date',
    'collab.contract.blockchain.hash': 'Blockchain Hash',
    'collab.contract.revenue.split': 'Revenue Split',
    'collab.contract.view.details': 'View Details',
    'collab.contract.send.invite': 'Send Signing Invitation',
    'collab.contract.edit': 'Edit Contract',
    'collab.contract.create.new': 'Create New Copyright Contract',
    'collab.contract.title.dreams': '"Dreams Take Flight" Copyright Contract',
    'collab.contract.title.folk': '"Folk Time" Copyright Contract',
    'collab.contract.revenue.composition': 'Composition 40% + Lyrics 30% + Vocals 20% + Mixing 10%',
    'collab.contract.revenue.folk': 'Composition 35% + Guitar 20% + Vocals 20% + Production 25%',
    'collab.contract.status.deployed': 'Deployed',
    'collab.contract.status.waiting': 'Waiting for Signature',
    
    // Contract Detail specific keys
    'contract.not.found.title': 'Contract Not Found',
    'contract.not.found.message': 'Contract information not found, please check if the link is correct',
    'contract.detail.title': 'Contract Details',
    'contract.detail.subtitle': 'View detailed contract information and terms',
    'contract.title.copyright': 'Copyright Cooperation Agreement',
    'contract.collaboration.space': 'Collaboration Space',
    'contract.status.signed': 'Signed',
    'contract.status.pending': 'Pending',
    'contract.signed.date': 'Signed Date',
    'contract.participants.count': 'Number of Participants',
    'contract.participants.people': 'people',
    'contract.signing.time': 'Signing Time',
    'contract.blockchain.verification': 'Blockchain Verification',
    'contract.blockchain.verified': 'On Chain',
    'contract.participants.info': 'Participants Information',
    'contract.participant.creator': 'Creator',
    'contract.participant.percentage': 'Revenue Share',
    'contract.terms': 'Contract Terms',
    'contract.clause.cooperation': 'Article 1: Cooperation Content',
    'contract.clause.revenue': 'Article 2: Revenue Distribution',
    'contract.clause.copyright': 'Article 3: Copyright Ownership',
    'contract.clause.breach': 'Article 4: Breach Liability',
    'contract.clause.dispute': 'Article 5: Dispute Resolution',
    'contract.cooperation.content': 'All parties have reached an agreement on the creation, performance and copyright distribution of the musical work "{spaceName}", clarifying the responsibilities and rights of each party in the music creation process. This agreement covers cooperation details in all aspects such as composition, arrangement, recording, and post-production.',
    'contract.revenue.content': 'All parties distribute all revenue generated by the work according to contribution and agreed ratios, including but not limited to royalty income, performance income, licensing fees, etc. The distribution ratio is as shown in the participant information above.',
    'contract.copyright.content': 'The copyright of the work is jointly owned by all participants, and no party may dispose of the copyright alone. For external licensing or transfer, unanimous consent of all participants is required. The copyright protection period shall be implemented in accordance with relevant laws and regulations.',
    'contract.breach.content': 'Any party that violates the provisions of this agreement shall bear corresponding breach liability, including but not limited to compensation for losses, continued performance of the contract, etc. If a breach causes losses to other parties, the breaching party shall compensate.',
    'contract.dispute.content': 'Disputes arising from this agreement shall be resolved through friendly consultation between the parties. If consultation fails, litigation may be brought to a people\'s court with jurisdiction. This agreement is governed by the laws of the People\'s Republic of China.',
    'contract.blockchain.info': 'Blockchain Verification Information',
    'contract.blockchain.hash': 'Blockchain Hash',
    'contract.blockchain.timestamp': 'Timestamp',
    'contract.blockchain.notice': 'This contract has been stored through blockchain technology to ensure the immutability and authenticity of the contract content.',
    'contract.action.back': 'Back',
    'contract.action.sign': 'Sign',
    'contract.action.download': 'Download Contract',
    'contract.action.share': 'Share Contract',
    
    // Contract Signing specific keys
    'contract.signing.title': 'Music Copyright Contract',
    'contract.signing.contract.number': 'Contract Number',
    'contract.signing.signed.by': 'This contract is signed by the following parties on',
    'contract.signing.signed.on': '',
    'contract.signing.party.first': 'Party A',
    'contract.signing.party.second': 'Party B',
    'contract.signing.party.third': 'Party C',
    'contract.signing.role.lyricist': '(Lyricist)',
    'contract.signing.role.composer': '(Composer)',
    'contract.signing.role.vocalist': '(Vocalist)',
    'contract.signing.clause.cooperation': 'Article 1: Cooperation Content',
    'contract.signing.clause.revenue': 'Article 2: Revenue Distribution',
    'contract.signing.cooperation.description': 'All parties have reached the following agreement on the creation, performance and copyright distribution of the musical work "Stars":',
    'contract.signing.cooperation.detail1': 'Party A is responsible for lyric creation and owns lyric copyright;',
    'contract.signing.cooperation.detail2': 'Party B is responsible for music composition and owns composition copyright;',
    'contract.signing.cooperation.detail3': 'Party C is responsible for vocals and owns recording production copyright.',
    'contract.signing.revenue.description': 'Revenue generated by the work is distributed according to the following ratios:',
    'contract.signing.revenue.lyricist': 'Party A (Lyrics): 40%',
    'contract.signing.revenue.composer': 'Party B (Composition): 30%',
    'contract.signing.revenue.vocalist': 'Party C (Vocals): 30%',
    'contract.signing.area': 'Signature Area',
    'contract.signing.signature.of': '\'s signature',
    'contract.signing.please.sign': 'Please sign in the area below:',
    'contract.signing.clear': 'Clear',
    'contract.signing.confirm': 'Confirm Signature',
    'contract.signing.cancel': 'Cancel',
    'contract.signing.click.sign': 'Click to Sign',
    'contract.signing.signed': 'Signed',
    'contract.signing.pending': 'Pending',
    'contract.signing.success': 'Signature successful!',
    'contract.signing.please.sign.first': 'Please sign first',
    'contract.signing.completed': 'Contract signing completed! Returning to collaboration space.',
    'contract.signing.wait.all': 'Please wait for all participants to complete signing.',
    'contract.signing.back.collab': 'Back to Collaboration',
    'contract.signing.notice': 'After all parties sign the contract and submit, you can collaborate on works in the DAW page',
    
    // Copyright Contract specific keys
    'copyright.contract.title': 'Copyright Revenue Contract',
    'copyright.contract.subtitle': 'Create and manage copyright revenue agreements for musical works, ensuring all participants\' rights are protected',
    'copyright.contract.back': 'Back',
    'copyright.work.info': 'Work Information',
    'copyright.work.title': 'Work Title',
    'copyright.work.type': 'Work Type',
    'copyright.work.type.music': 'Musical Work',
    'copyright.work.type.single': 'Single',
    'copyright.work.type.album': 'Album',
    'copyright.work.type.ep': 'EP',
    'copyright.territory': 'Territory',
    'copyright.territory.global': 'Global',
    'copyright.territory.china': 'Mainland China',
    'copyright.territory.apac': 'Asia Pacific',
    'copyright.territory.north.america': 'North America',
    'copyright.territory.europe': 'Europe',
    'copyright.duration': 'Contract Duration',
    'copyright.duration.permanent': 'Permanent',
    'copyright.duration.5years': '5 Years',
    'copyright.duration.10years': '10 Years',
    'copyright.duration.20years': '20 Years',
    'copyright.duration.custom': 'Custom',
    'copyright.effective.date': 'Effective Date',
    'copyright.payment.method': 'Payment Method',
    'copyright.payment.revenue.share': 'Revenue Share',
    'copyright.payment.one.time': 'One-time Payment',
    'copyright.payment.installment': 'Installment Payment',
    'copyright.distribution.platforms': 'Distribution Platforms',
    'copyright.special.terms': 'Special Terms',
    'copyright.special.terms.placeholder': 'Enter any special terms or notes...',
    'copyright.participants.title': 'Participants and Revenue Share',
    'copyright.add.party': 'Add Participant',
    'copyright.party.number': 'Participant',
    'copyright.party.name': 'Name',
    'copyright.party.role': 'Role',
    'copyright.party.percentage': 'Revenue Share (%)',
    'copyright.party.email': 'Email',
    'copyright.party.phone': 'Phone',
    'copyright.party.select.role': 'Select Role',
    'copyright.role.lyricist': 'Lyricist',
    'copyright.role.composer': 'Composer',
    'copyright.role.arranger': 'Arranger',
    'copyright.role.vocalist': 'Vocalist',
    'copyright.role.mixing': 'Mixing',
    'copyright.role.producer': 'Producer',
    'copyright.role.publisher': 'Publisher',
    'copyright.role.other': 'Other',
    'copyright.total.percentage': 'Total Revenue Share:',
    'copyright.percentage.warning': 'Please ensure total revenue share is 100%',
    'copyright.contract.status': 'Contract Status',
    'copyright.status.generation': 'Contract Generation',
    'copyright.status.blockchain': 'Blockchain Deployment',
    'copyright.status.signing': 'Participant Signing',
    'copyright.status.effective': 'Contract Effective',
    'copyright.percentage.preview': 'Revenue Share Preview',
    'copyright.party.unnamed': 'Unnamed',
    'copyright.role.unset': 'Unset',
    'copyright.percentage.complete': 'Revenue share configuration complete',
    'copyright.actions': 'Actions',
    'copyright.generating': 'Generating...',
    'copyright.generate': 'Generate Copyright Contract',
    'copyright.view.details': 'View Contract Details',
    'copyright.send.invitation': 'Send Signing Invitation',
    'copyright.percentage.sum.error': 'Total revenue share must equal 100%',
    'copyright.party.info.incomplete': 'Please complete all participant information',
    'copyright.contract.success': 'Copyright contract generated successfully! Contract has been deployed to blockchain.',
    
    // Create Space specific keys
    'space.create.title': 'Create Collaboration Space',
    'space.create.subtitle': 'Fill in space information and invite members to start music collaboration',
    'space.create.back': 'Back',
    'space.admin': 'Administrator',
    'space.project.progress': 'Project Progress',
    'space.progress.completed': 'Completed',
    'space.progress.team.building': 'Team Building',
    'space.progress.revenue.confirm': 'Revenue Confirmation',
    'space.progress.in.progress': 'In Progress',
    'space.progress.contract.signing': 'Contract Signing',
    'space.progress.pending': 'Pending',
    'space.basic.info': 'Basic Information',
    'space.name': 'Space Name',
    'space.name.placeholder': 'Enter collaboration space name',
    'space.description': 'Space Description',
    'space.description.placeholder': 'Briefly describe the goals and requirements of this collaboration space',
    'space.required.roles': 'Required Roles',
    'space.privacy.settings': 'Privacy Settings',
    'space.privacy.public': 'Public',
    'space.privacy.public.desc': 'Anyone can see and join this space',
    'space.privacy.participants': 'Participants Only',
    'space.privacy.participants.desc': 'Only invited members can see space content',
    'space.privacy.searchable': 'Searchable',
    'space.privacy.searchable.desc': 'Can be found in search but requires approval to join',
    'space.revenue.settings': 'Revenue Share Settings',
    'space.revenue.desc': 'Set revenue share ratio for each role, total should be 100%',
    'space.revenue.total': 'Total:',
    'space.revenue.warning.over': 'Warning: Revenue share exceeds 100%, please adjust',
    'space.revenue.warning.under': 'Note: Revenue share is less than 100%, please adjust',
    'space.revenue.visualization': 'Revenue Share Visualization',
    'space.role.details': 'Role Revenue Details',
    'space.revenue.note': 'Platform standard revenue shares, can be negotiated before collaboration signing',
    'space.invite.members': 'Invite Members',
    'space.platform.recommended': 'Platform Recommendations',
    'space.member.skills': 'Skills:',
    'space.member.expected.share': 'Expected revenue share:',
    'space.member.rating': 'Rating:',
    'space.invite.join': 'Invite to Join',
    'space.condition.search': 'Conditional Search',
    'space.search.placeholder': 'Search username or skills',
    'space.table.index': 'Index',
    'space.table.username': 'Username',
    'space.table.role': 'Role',
    'space.table.expected.share': 'Expected Revenue Share',
    'space.table.rating': 'Platform Rating',
    'space.table.has.works': 'Has Portfolio',
    'space.table.actions': 'Actions',
    'space.table.yes': 'Yes',
    'space.table.no': 'No',
    'space.table.view': 'View',
    'space.table.invite': 'Invite',
    'space.table.placeholder': 'Search for users',
    'space.selected.count': 'Selected',
    'space.selected.members': 'members',
    'space.send.invites': 'Send Invites',
    'space.create.button': 'Create Collaboration Space',
    'space.member.info': 'Member Information',
    'space.member.skills.label': 'Skills:',
    'space.member.expected.share.label': 'Expected Revenue Share:',
    'space.member.works.label': 'Portfolio:',
    'space.member.works.yes': 'Yes',
    'space.member.works.no': 'No',
    'space.modal.close': 'Close',
    'space.invite.member': 'Invite Member',
    'space.invite.message': 'Invitation Message',
    'space.invite.message.placeholder': 'Write an invitation message to let them know about the project...',
    'space.invite.button': 'Invite',
    'space.batch.invite': 'Batch Invite Members',
    'space.invite.targets': 'Invite Targets',
    'space.invite.people': 'people',
    'space.error.select.members': 'Please select members to invite first',
    'space.error.space.name': 'Please enter space name',
    'space.error.select.roles': 'Please select at least one required role',
    'space.error.percentage.total': 'Revenue share total is {total}%, please adjust to 100%',
    'space.success.created': 'Collaboration space created successfully!',
    'space.alert.invites.sent': 'Invitation sent!',
    'space.alert.batch.invites.sent': 'Invitations sent to {count} members!',
    
    // Create space member count
    'createspace.member.count': 'Member Count',
    'createspace.member.count.hint': 'Max 5 people',
    
    // ViewAllSpaces specific keys
    'spaces.hot.title': 'Hot Collaboration Spaces',
    'spaces.search.placeholder': 'Search collaboration spaces',
    'spaces.category.title': 'Categories',
    'spaces.category.all': 'All',
    'spaces.category.lyricist': 'Looking for Lyricist',
    'spaces.category.composer': 'Looking for Composer',
    'spaces.category.vocalist': 'Looking for Vocalist',
    'spaces.category.band': 'Looking for Band',
    'spaces.category.producer': 'Looking for Producer',
    'spaces.join': 'Join',
    'spaces.view.translation': 'View Translation',
    'spaces.like': 'Like',
    'spaces.comment': 'Comment',
    'spaces.comments.count': 'comments',
    'spaces.join.space': 'Join Space',
    'spaces.featured.classical': 'Classical Music Appreciation and Creation',
    'spaces.featured.classical.desc': 'Professional platform for classical music enthusiasts',
    'spaces.featured.electronic': 'Electronic Music Producer Alliance',
    'spaces.featured.electronic.desc': 'Electronic music production techniques and creative sharing',
    'spaces.members.count': 'K members',
    'spaces.posts.yearly': 'Annual posts: ',
    'spaces.posts.count': 'posts',
    
    // MySpaces specific keys
    'myspaces.cover.alt': 'Space Cover',
    'myspaces.edit': 'Edit',
    'myspaces.public.group': 'Public Group',
    'myspaces.members.count': 'members',
    'myspaces.invite': 'Invite',
    'myspaces.share': 'Share',
    'myspaces.tab.discussion': 'Discussion',
    'myspaces.tab.activity': 'Activity',
    'myspaces.tab.content': 'Media Content',
    'myspaces.tab.files': 'Files',
    'myspaces.tab.members': 'Members',
    'myspaces.sign.contract': 'Sign Contract',
    'myspaces.daw': 'DAW',
    'myspaces.share.mood': 'Share your mood...',
    'myspaces.quick.anonymous': 'Anonymous Post',
    'myspaces.quick.vote': 'Vote',
    'myspaces.quick.activity': 'Feeling/Activity',
    'myspaces.created.group': 'created group',
    'myspaces.hours.ago': 'hours ago',
    'myspaces.no.content': 'This post has no content',
    'myspaces.like': 'Like',
    'myspaces.comment': 'Comment',
    'myspaces.send': 'Send',
    'myspaces.share.post': 'Share',
    'myspaces.write.comment': 'Write comment...',
    'myspaces.start.conversation': 'Start Conversation',
    'myspaces.share.ideas': 'Share ideas, ask questions or start discussions',
    'myspaces.first.post': 'Post the First Message',
    'myspaces.content.developing': 'Content under development...',
    'myspaces.create.post': 'Create Post',
    'myspaces.what.thinking': 'What\'s on your mind?',
    'myspaces.post': 'Post',
    
    // AllSpaces discussion data specific keys
    'allspaces.discussion.hello': 'Hello everyone! We\'ve completed the initial recording of the main melody. Please listen to the demo and share any suggestions for discussion here. Especially for the lyrics part, we hope it can be more aligned with the theme.',
    'allspaces.discussion.demo.good': 'Listened to the demo, overall it sounds great! I think the chorus part could emphasize the emotional progression more. I\'ll submit a revised version tonight.',
    'allspaces.discussion.bridge.adjust': 'I agree with Teacher Li\'s suggestion. Also, I think the range of the bridge part could be adjusted slightly to make it more comfortable for singing.',
    'allspaces.discussion.mixing': 'I\'d like to discuss the mixing issue with everyone: Currently the drums and bass layering needs adjustment. I suggest adding some atmospheric sound effects in the chorus to make it more impactful.',
    'allspaces.discussion.mixing.reply': 'Great suggestion! We could try adding some synthesizer pad effects in the chorus to create a wider soundstage. Do you have any specific ideas?',
    'allspaces.discussion.recording': 'Recorded a test vocal today, overall it feels good, but there are a few places where the pronunciation needs adjustment. Please listen and give some feedback!',
    'allspaces.time.hours.ago': 'hours ago',
    'allspaces.time.minutes.ago': 'minutes ago',
    'allspaces.time.days.ago': 'days ago',
    'allspaces.author.producer.wang': 'Producer Wang',
    'allspaces.author.lyricist.li': 'Lyricist Li',
    'allspaces.author.singer.zhang': 'Singer Zhang',
    'allspaces.author.mixer.chen': 'Mixer Chen',
    'allspaces.activity.file.upload': 'Uploaded new file: main_melody_v3.2.wav',
    'allspaces.activity.file.size': 'File size: 25.6MB',
    'allspaces.activity.comment.post': 'Posted a new comment in discussion',
    'allspaces.activity.comment.detail': 'Suggestions about lyric revisions',
    'allspaces.activity.member.join': 'Joined the collaboration space',
    'allspaces.activity.role.mixer': 'Role: Audio Mixer',
    'allspaces.activity.version.create': 'Created new version v3.1',
    'allspaces.activity.version.detail': 'Completed verse recording and initial mixing',
    'allspaces.system': 'System',
    'collab.initiator': 'Initiator',
    'collab.welcome.title': 'My Collaboration Spaces',
    'collab.welcome.desc': 'Manage your collaboration spaces, signed contracts and copyright contracts here',
    'collab.welcome.instruction': 'Please select content to view from the left menu',
    'collab.success.publish': 'Collaboration work has been successfully published to works page!',
    'collab.error.publish': 'Publish failed, please try again later',
    'collab.success.nft': 'NFT created successfully!',
    'collab.nft.details': 'The NFT is now displayed on the work details page with purchase functionality.',
    'collab.error.nft': 'NFT creation failed, please try again later',
    'collab.categories.all': 'All',
    'collab.categories.lyrics': 'Find Lyricist',
    
    // Collaboration page
    'collab.spaces.title': 'Collaboration Spaces',
    'collab.spaces.subtitle': 'Create wonderful music with musicians around the world',
    'collab.search.placeholder': 'Search collaboration spaces...',
    'collab.search.button': 'Search',
    'collab.created.at': 'Created on',
    'collab.join.collaboration': 'Join Collaboration',
    'collab.no.spaces': 'No collaboration spaces',
    'collab.no.spaces.desc': 'Create your first collaboration space and start your musical journey',
    'collab.create.now': 'Create Now',
    'collab.demo.pop.title': 'Pop Music Collaboration',
    'collab.demo.pop.desc': 'A collaboration space focused on pop music creation, welcoming various instrumentalists and songwriters',
    'collab.demo.electronic.title': 'Electronic Music Lab',
    'collab.demo.electronic.desc': 'Explore the infinite possibilities of electronic music with the latest synthesizers and production techniques',
    'collab.demo.rock.title': 'Rock Band Recruitment',
    'collab.demo.rock.desc': 'Find like-minded rock musicians, form a new band, and conquer the stage together',
    
    // AllSpaces members and history data
    'collab.member.arranger.li': 'Arranger Li',
    'collab.member.mixer.zhang': 'Mixer Zhang',
    'collab.member.guitarist.chen': 'Guitarist Chen',
    'collab.member.singer.liu': 'Singer Liu',
    'collab.member.lyricist.zhao': 'Lyricist Zhao',
    'collab.member.lyricist.li': 'Lyricist Li',
    'collab.member.singer.zhang': 'Singer Zhang',
    'collab.member.mixer.chen': 'Mixer Chen',
    'collab.member.producer.wang': 'Producer Wang',
    'collab.member.lyricist.li.full': 'Lyricist Li',
    'collab.member.singer.zhang.full': 'Singer Zhang',
    
    // AllSpaces data section translation keys
    'allspaces.data.pop.title': 'Original Pop Song Production',
    'allspaces.data.pop.desc': 'Looking for excellent lyricists and singers to create beautiful pop songs together',
    'allspaces.data.folk.title': 'Folk Guitar Ensemble Project',
    'allspaces.data.folk.desc': 'Hope to find like-minded folk lovers to create warm music together',
    'allspaces.data.electronic.title': 'Electronic Music Laboratory',
    'allspaces.data.electronic.desc': 'Explore cutting-edge electronic music production technology and create unique sound experiences',
    'allspaces.data.ancient.title': 'Ancient Style Music Studio',
    'allspaces.data.ancient.desc': 'Inherit classics, integrate modern elements, create music works with Chinese characteristics',
    'allspaces.data.rap.title': 'Rap Creation Alliance',
    'allspaces.data.rap.desc': 'Gather rap enthusiasts to create the most exciting rap works',
    'allspaces.data.piano.title': 'Piano Concerto Creation',
    'allspaces.data.piano.desc': 'Perfect fusion of classical and modern, creating beautiful piano concertos',
    'allspaces.data.rock.title': 'Rock Band Formation',
    'allspaces.data.rock.desc': 'Looking for rock band members to rock the stage together',
    'allspaces.data.jazz.title': 'Jazz Music Salon',
    'allspaces.data.jazz.desc': 'Taste classic jazz, create modern jazz music',
    'allspaces.status.recruiting': 'Recruiting',
    'allspaces.status.ongoing': 'In Progress',
    'allspaces.status.completed': 'Completed',
    'allspaces.creator.producer.wang': 'Producer Wang',
    'allspaces.creator.folk.lover': 'Folk Lover',
    'allspaces.creator.electronic.producer': 'Electronic Producer',
    'allspaces.creator.ancient': 'Ancient Sound Studio',
    'allspaces.creator.rap.leader': 'Rap Boss',
    'allspaces.creator.piano.poet': 'Piano Poet',
    'allspaces.creator.rock.veteran': 'Rock Veteran',
    'allspaces.creator.jazz.master': 'Jazz Master',
    'allspaces.role.lyrics': 'Lyrics',
    'allspaces.role.vocals': 'Vocals',
    'allspaces.role.composition': 'Composition',
    'allspaces.role.guitar': 'Guitar',
    'allspaces.role.mixing': 'Mixing',
    'allspaces.role.production': 'Production',
    'allspaces.role.arrangement': 'Arrangement',
    'allspaces.role.piano': 'Piano',
    'allspaces.role.lead.vocals': 'Lead Vocals',
    'allspaces.role.bass': 'Bass',
    'allspaces.role.jazz.piano': 'Jazz Piano',
    'allspaces.role.saxophone': 'Saxophone',
    'allspaces.role.rap': 'Rap',
    'allspaces.role.beat.production': 'Beat Production',
    'allspaces.time.ago.hours': 'hours ago',
    'allspaces.time.ago.days': 'days ago',
    'allspaces.members.count': 'members',
    'allspaces.apply.success': 'Application submitted! The creator will review and you will see it in your applied collaboration spaces after approval',
    
    // Modal and UI translations
    'allspaces.modal.invite.title': 'Invite Members',
    'allspaces.modal.invite.username': 'Username or Email',
    'allspaces.modal.invite.placeholder': 'Enter username or email address',
    'allspaces.modal.invite.role': 'Role',
    'allspaces.modal.invite.role.select': 'Select Role',
    'allspaces.modal.invite.message': 'Invitation Message',
    'allspaces.modal.invite.message.placeholder': 'Write some invitation message...',
    'allspaces.modal.invite.cancel': 'Cancel',
    'allspaces.modal.invite.send': 'Send Invitation',
    'allspaces.modal.invite.sent': 'Invitation sent!',
    
    'allspaces.modal.share.title': 'Share Collaboration Space',
    'allspaces.modal.share.link': 'Share Link',
    'allspaces.modal.share.copy': 'Copy',
    'allspaces.modal.share.social': 'Share to Social Media',
    'allspaces.modal.share.wechat': 'WeChat',
    'allspaces.modal.share.weibo': 'Weibo',
    'allspaces.modal.share.qq': 'QQ',
    'allspaces.modal.share.close': 'Close',
    'allspaces.modal.share.copied': 'Link copied to clipboard!',
    
    'allspaces.modal.discussion.title': 'Start New Discussion',
    'allspaces.modal.discussion.title.label': 'Discussion Title',
    'allspaces.modal.discussion.title.placeholder': 'Enter discussion title',
    'allspaces.modal.discussion.content': 'Discussion Content',
    'allspaces.modal.discussion.content.placeholder': 'Describe what you want to discuss...',
    'allspaces.modal.discussion.files': 'Related Files (Optional)',
    'allspaces.modal.discussion.cancel': 'Cancel',
    'allspaces.modal.discussion.start': 'Start Discussion',
    'allspaces.modal.discussion.started': 'Discussion started!',
    
    // Tab and section labels
    'allspaces.tab.discussion': 'Discussion',
    'allspaces.tab.activity': 'Activity',
    'allspaces.tab.media': 'Media & Files',
    'allspaces.tab.members': 'Members',
    'allspaces.tab.history': 'Version History',
    'allspaces.privacy.public': 'Public',
    'allspaces.privacy.searchable': 'Searchable',
    'allspaces.privacy.private': 'Private',
    'allspaces.creator.label': 'Creator:',
    'allspaces.button.invite': 'Invite',
    'allspaces.button.share': 'Share',
    'allspaces.discussion.team': 'Team Discussion',
    'allspaces.discussion.start.new': 'Start New Discussion',
    'allspaces.discussion.no.content': 'No discussion content yet',
    'allspaces.discussion.first.message': 'Start your first team discussion!',
    'allspaces.discussion.reply': 'Reply',
    'allspaces.discussion.join': 'Join discussion...',
    'allspaces.discussion.post': 'Post',
    'allspaces.activity.record': 'Activity Record',
    'allspaces.activity.recent.days': 'Recent 7 days',
    'allspaces.activity.no.record': 'No activity records yet',
    'allspaces.activity.when.collaborate': 'Activity records will appear here when members start collaborating',
    'allspaces.activity.load.more': 'View more activity records',
    'allspaces.media.title': 'Media & Files',
    'allspaces.media.upload': 'Upload File',
    'allspaces.media.no.files': 'No files uploaded yet',
    'allspaces.media.start.upload': 'Start uploading audio files, videos and other collaboration materials',
    'allspaces.media.upload.first': 'Upload first file',
    'allspaces.media.audio.files': 'Audio Files',
    'allspaces.media.document.files': 'Document Files',
    'allspaces.media.image.files': 'Image Files',
    'allspaces.media.uploader': 'Uploader:',
    'allspaces.media.preview': 'Preview',
    'allspaces.media.download': 'Download',
    'allspaces.media.waveform.preview': 'Audio waveform preview',
    'allspaces.members.list': 'Member List',
    'allspaces.members.count.label': 'members',
    'allspaces.members.role.label': 'Role:',
    'allspaces.members.specialty.label': 'Specialties:',
    'allspaces.members.join.time': 'Join time:',
    'allspaces.members.contributions': 'Contributions:',
    'allspaces.members.contributions.count': 'times',
    'allspaces.members.last.active': 'Last active:',
    'allspaces.members.view.profile': 'View Profile',
    'allspaces.members.remove': 'Remove',
    'allspaces.members.invite.new': 'Invite New Member',
    'allspaces.history.title': 'Version History',
    'allspaces.history.versions': 'versions',
    'allspaces.history.no.versions': 'No version records yet',
    'allspaces.history.when.update': 'Version history will appear here when works are updated',
    'allspaces.history.latest': 'Latest',
    'allspaces.history.main.changes': 'Main Changes:',
    'allspaces.history.related.files': 'Related Files:',
    'allspaces.history.download': 'Download',
    'allspaces.history.load.more': 'Load more version history',
    
    // Button actions English translations
    'allspaces.button.sign.contract': 'Sign Contract',
    'allspaces.button.publish.work': 'Publish Work',
    'allspaces.button.publishing': 'Publishing...',
    'allspaces.button.create.nft': 'Create NFT',
    'allspaces.button.creating.nft': 'Creating...',
    
    // Sidebar menu English translations
    'allspaces.sidebar.my.spaces': 'My Collaboration Spaces',
    'allspaces.sidebar.expand.indicator': '▼',
    'allspaces.sidebar.collapse.indicator': '▶',
    
    // Media and files data English translations
    'allspaces.media.file.name.melody': 'Main_melody_v3.2.wav',
    'allspaces.media.file.name.vocal': 'Vocal_demo_v2.1.mp3',
    'allspaces.media.file.name.arrangement': 'Arrangement_sketch_v3.0.mid',
    'allspaces.media.file.name.lyrics': 'Lyrics_final.txt',
    'allspaces.media.file.name.chords': 'Chord_chart_v3.0.pdf',
    'allspaces.media.file.name.project.plan': 'Project_plan.pdf',
    'allspaces.media.file.name.album.cover': 'Album_cover_design_v1.jpg',
    'allspaces.media.file.name.sheet.music': 'Sheet_music_screenshot.png',
    'allspaces.media.file.uploaded.by.producer': 'Producer Wang',
    'allspaces.media.file.uploaded.by.singer': 'Singer Zhang',
    'allspaces.media.file.uploaded.by.lyricist': 'Lyricist Li',
    'allspaces.media.time.hours.ago': 'hours ago',
    'allspaces.media.time.day.ago': 'day ago',
    'allspaces.media.time.days.ago': 'days ago',
    
    // Contract related English translations
    'allspaces.contract.source.space': 'Source Space: ',
    'allspaces.contract.created.time': 'Created Time: ',
    'allspaces.contract.status.deployed': 'Deployed',
    'allspaces.contract.status.pending': 'Pending Signature',
    'allspaces.contract.status.draft': 'Draft',
    
    // DAW project English translations
    'allspaces.daw.project.edm': 'EDM Production Project',
    'allspaces.daw.project.edm.desc': 'Create original EDM track, basic arrangement completed',
    'allspaces.daw.project.folk': 'Folk Guitar Recording Project',
    'allspaces.daw.project.folk.desc': 'Record warm folk guitar compositions',
    'allspaces.daw.project.traditional': 'Traditional Music Arrangement',
    'allspaces.daw.project.traditional.desc': 'Perfect combination of traditional instruments and modern arrangement',
    'allspaces.daw.project.rock': 'Rock Band Recording',
    'allspaces.daw.project.rock.desc': 'Participate in rock band drum recording and mixing',
    'allspaces.daw.project.hiphop': 'Hip-Hop Beat Production',
    'allspaces.daw.project.hiphop.desc': 'Create original beats for rap artists',
    'allspaces.daw.project.piano': 'Piano Solo Recording',
    'allspaces.daw.project.piano.desc': 'Assist in recording classical piano compositions',
    'allspaces.daw.project.jazz': 'Jazz Improvisation',
    'allspaces.daw.project.jazz.desc': 'Participate in jazz band improvisation recording',
    'allspaces.daw.status.in.progress': 'In Progress',
    'allspaces.daw.status.recruiting': 'Recruiting',
    'allspaces.daw.status.completed': 'Completed',
    
    // General translations
    'allspaces.role.all': 'All Roles',
    'allspaces.time.just.now': 'Just now',
    
    // Apply to join related translations
    'allspaces.space.not.found': 'Collaboration space not found',
    'allspaces.revenue.modal.title': 'Revenue Share Confirmation',
    'allspaces.revenue.modal.subtitle': 'Please confirm the revenue share distribution for your application to join "{spaceName}"',
    'allspaces.revenue.current.distribution': 'Revenue Share Distribution',
    'allspaces.revenue.your.proposed.role': 'Your Proposed Role',
    'allspaces.revenue.total': 'Total',
    'allspaces.revenue.modal.hint': 'You can adjust the revenue share percentages. Make sure the total equals 100%. This proposal will be sent to the creator for review.',
    'allspaces.revenue.modal.cancel': 'Cancel',
    'allspaces.revenue.modal.apply': 'Submit Application',
    'allspaces.revenue.modal.success': 'Application submitted! Your revenue share proposal will be sent to the creator for review',
    'allspaces.revenue.modal.error.total': 'Revenue share total is {total}%, please adjust to 100%',
    
    // CreativeCenter works English translations
    'creative.work.nocturne': 'Nocturne',
    'creative.work.spring.story': 'Spring Story',
    'creative.work.untitled': 'Untitled Work',
    'creative.genre.classical': 'Classical',
    'creative.genre.folk': 'Folk',
    'creative.genre.electronic': 'Electronic',
    
    // CreativeCenter collaboration English translations
    'creative.collab.dream.concerto': 'Dream Concerto',
    'creative.collab.youth.memories': 'Youth Memories',
    'creative.role.producer': 'Music Producer',
    'creative.role.pianist': 'Pianist',
    'creative.role.violinist': 'Violinist',
    'creative.role.arranger': 'Arranger',
    'creative.role.singer': 'Singer-Songwriter',
    'creative.role.guitarist': 'Guitarist',
    'creative.role.lyricist': 'Lyricist',
    
    // CreativeCenter playlist English translations
    'creative.playlist.my.originals': 'My Originals',
    'creative.playlist.favorite.classical': 'Favorite Classical',
    'creative.playlist.relaxing.time': 'Relaxing Time',
    'allspaces.sidebar.sort.hot': 'Sort by Popularity',
    'allspaces.sidebar.sort.new': 'Sort by Time',
    'allspaces.sidebar.sort.members': 'Sort by Member Count',
    'allspaces.load.more': 'Load More',
    
    // Media files translations
    'allspaces.media.file.main.melody': 'Main_melody_v3.2.wav',
    'allspaces.media.file.demo.version': 'Demo_version_v2.1.mp3',
    'allspaces.media.file.arrangement.sketch': 'Arrangement_sketch_v3.0.mid',
    'allspaces.media.file.lyrics.final': 'Lyrics_final_version.txt',
    'allspaces.media.file.chord.chart': 'Chord_chart_v3.0.pdf',
    'allspaces.media.file.project.plan': 'Project_plan.pdf',
    'allspaces.media.file.album.cover': 'Album_cover_design_v1.jpg',
    'allspaces.media.file.sheet.screenshot': 'Sheet_music_screenshot.png',
    'allspaces.uploader.producer': 'Producer Wang',
    'allspaces.uploader.singer': 'Singer Zhang',
    'allspaces.uploader.lyricist': 'Lyricist Li',
    
    // DAW projects translations
    'allspaces.daw.edm.project': 'Electronic Dance Music Production Project',
    'allspaces.daw.edm.desc': 'Creating original EDM tracks, basic arrangement completed',
    'allspaces.daw.folk.project': 'Folk Guitar Recording Project',
    'allspaces.daw.folk.desc': 'Recording warm folk guitar works',
    'allspaces.daw.ancient.project': 'Ancient Style Music Arrangement',
    'allspaces.daw.ancient.desc': 'Perfect combination of traditional instruments and modern arrangement',
    'allspaces.daw.rock.project': 'Rock Band Recording',
    'allspaces.daw.rock.desc': 'Participating in rock band drum recording and mixing',
    'allspaces.daw.hiphop.project': 'Hip-Hop Beat Production',
    'allspaces.daw.hiphop.desc': 'Creating original beats for rappers',
    'allspaces.daw.piano.project': 'Piano Solo Recording',
    'allspaces.daw.piano.desc': 'Assisting in completing classical piano work recording',
    'allspaces.daw.jazz.project': 'Jazz Improvisation Performance',
    'allspaces.daw.jazz.desc': 'Participating in jazz band improvisation recording',
    'allspaces.daw.collaborators': 'collaborators',
    'allspaces.daw.progress': 'Project Progress',
    'allspaces.daw.open': 'Open DAW',
    'allspaces.daw.settings': 'Project Settings',
    
    // Contract translations
    'allspaces.contract.view.details': 'View Details',
    'allspaces.contract.send.invitation': 'Send Signing Invitation',
    'allspaces.contract.edit': 'Edit Contract',
    'allspaces.contract.create.new': 'Create New Copyright Revenue Contract',
    'allspaces.contract.signed': 'Signed',
    'allspaces.contract.pending': 'Pending Signature',
    'allspaces.contract.parties': 'Parties:',
    'allspaces.contract.sign.time': 'Signing Time:',
    'allspaces.contract.status.label': 'Status:',
    'allspaces.contract.creation.time': 'Creation Time:',
    'allspaces.contract.revenue.share': 'Revenue Share:',
    'allspaces.contract.blockchain.hash': 'Blockchain Hash:',
    
    // CreateSpace component translations
    'createspace.back': 'Back',
    'createspace.title': 'Create Collaboration Space',
    'createspace.subtitle': 'Fill in space information and invite members to start musical collaboration',
    'createspace.admin': 'Admin',
    'createspace.progress.title': 'Project Progress',
    'createspace.progress.completed': 'Completed',
    'createspace.progress.team.building': 'Team Building',
    'createspace.progress.revenue.confirmation': 'Revenue Confirmation',
    'createspace.progress.ongoing': 'In Progress',
    'createspace.progress.contract.signing': 'Contract Signing',
    'createspace.progress.pending': 'Pending',
    
    // Basic information section
    'createspace.basic.info': 'Basic Information',
    'createspace.space.name': 'Space Name',
    'createspace.space.name.placeholder': 'Enter collaboration space name',
    'createspace.space.description': 'Space Description',
    'createspace.space.description.placeholder': 'Briefly describe the goals and requirements of this collaboration space',
    'createspace.required.roles': 'Required Personnel',
    
    // Privacy settings
    'createspace.privacy.settings': 'Privacy Settings',
    'createspace.privacy.public': 'Public',
    'createspace.privacy.public.desc': 'Anyone can see and join this space',
    'createspace.privacy.participants': 'Participants Only',
    'createspace.privacy.participants.desc': 'Only invited members can see space content',
    'createspace.privacy.searchable': 'Searchable',
    'createspace.privacy.searchable.desc': 'Can be found in search, but requires application to join',
    
    // Revenue share settings
    'createspace.revenue.settings': 'Revenue Share Settings',
    'createspace.revenue.description': 'Set revenue share percentage for each role, total should be 100%',
    'createspace.revenue.total': 'Total:',
    'createspace.revenue.warning': 'Warning: Revenue share exceeds 100%, please adjust',
    'createspace.revenue.hint': 'Hint: Revenue share is less than 100%, please adjust',
    'createspace.revenue.visualization': 'Revenue Share Visualization',
    'createspace.revenue.details': 'Role Revenue Details',
    'createspace.revenue.platform.standard': 'Platform standard revenue share, can be negotiated before collaboration signing',
    
    // Member invitation section
    'createspace.invite.members': 'Invite Members',
    'createspace.platform.recommendations': 'Platform Recommendations',
    'createspace.member.specialties': 'Specialties:',
    'createspace.member.expected.share': 'Expected share:',
    'createspace.member.rating': 'Rating:',
    'createspace.invite.join': 'Invite to Join',
    'createspace.conditional.search': 'Conditional Search',
    'createspace.search.placeholder': 'Search username or skills',
    
    // Member table headers
    'createspace.table.number': 'No.',
    'createspace.table.username': 'Username',
    'createspace.table.role': 'Role',
    'createspace.table.expected.share': 'Expected Share',
    'createspace.table.rating': 'Platform Rating',
    'createspace.table.portfolio': 'Portfolio Available',
    'createspace.table.actions': 'Actions',
    'createspace.table.view': 'View',
    'createspace.table.invite': 'Invite to Join',
    'createspace.table.awaiting.users': 'Awaiting search users',
    'createspace.table.yes': 'Yes',
    'createspace.table.no': 'No',
    'createspace.selected.members': 'Selected {count} members',
    'createspace.send.invitations': 'Send Invitations ({count})',
    'createspace.create.button': 'Create Collaboration Space',
    
    // Modals
    'createspace.modal.member.info': 'Member Information',
    'createspace.modal.platform.rating': 'Platform Rating:',
    'createspace.modal.specialties.label': 'Specialties:',
    'createspace.modal.expected.share.label': 'Expected Share:',
    'createspace.modal.portfolio.label': 'Portfolio:',
    'createspace.modal.portfolio.available': 'Available',
    'createspace.modal.portfolio.unavailable': 'Not Available',
    'createspace.modal.close': 'Close',
    'createspace.modal.invite.member': 'Invite Member',
    'createspace.modal.role.share': 'Role share:',
    'createspace.modal.invitation.message': 'Invitation Message',
    'createspace.modal.invitation.placeholder': 'Write some invitation message to let them know about the project details...',
    'createspace.modal.invitation.default': 'Hello! I am creating a music collaboration space "{spaceName}" and would like to invite you to participate as {roles}. Based on your preferences, the suggested share percentage is {percentage}%. Looking forward to collaborating with you!',
    'createspace.modal.invitation.sent': 'Invitation sent!',
    'createspace.modal.invite.action': 'Invite',
    'createspace.modal.batch.invite': 'Batch Invite Members',
    'createspace.modal.invite.targets': 'Invitation Targets ({count} people)',
    'createspace.modal.batch.default': 'Hello! I am creating a music collaboration space "{spaceName}" and would like to invite you to participate in this project. We need roles like {roles}, looking forward to collaborating with you!',
    'createspace.modal.batch.sent': 'Invitations sent to {count} members!',
    'createspace.modal.batch.invite.action': 'Invite ({count} people)',
    
    // Role translations
    'createspace.role.lyrics': 'Lyrics',
    'createspace.role.composition': 'Composition', 
    'createspace.role.arrangement': 'Arrangement',
    'createspace.role.vocals': 'Vocals',
    'createspace.role.mixing': 'Mixing',
    'createspace.role.production': 'Production & Distribution',
    'createspace.role.other': 'Other',
    'createspace.role.singing': 'Singing',
    
    // Search categories
    'createspace.category.all': 'All',
    
    // Member names (sample data)
    'createspace.member.lin.yuwei': 'Lin Yuwei',
    'createspace.member.chen.junjie': 'Chen Junjie', 
    'createspace.member.zhang.mengqi': 'Zhang Mengqi',
    
    // Validation messages
    'createspace.validation.select.members': 'Please select members to invite first',
    'createspace.validation.space.name': 'Please enter space name',
    'createspace.validation.select.roles': 'Please select at least one required role',
    'createspace.validation.revenue.total': 'Revenue share total is {percentage}%, please adjust to 100%',
    'createspace.success.created': 'Collaboration space created successfully!',
    'createspace.current.user': 'Current User',
    'createspace.status.recruiting': 'Recruiting',
    'collab.member.mixer.chen.full': 'Mixer Chen',
    'collab.role.lyricist': 'Lyricist',
    'collab.role.vocalist': 'Vocalist',
    'collab.role.mixer': 'Mixer',
    'collab.specialty.composition': 'Composition',
    'collab.specialty.arrangement': 'Arrangement',
    'collab.specialty.lyrics': 'Lyrics',
    'collab.specialty.vocals': 'Vocals',
    'collab.specialty.harmony': 'Harmony',
    'collab.specialty.mixing': 'Mixing',
    'collab.specialty.mastering': 'Mastering',
    'collab.time.2hours.ago': '2 hours ago',
    'collab.time.1day.ago': '1 day ago',
    'collab.time.5hours.ago': '5 hours ago',
    'collab.time.3hours.ago': '3 hours ago',
    
    // Version history translations
    'collab.version.title.main.melody': 'Completed main melody recording and initial mixing',
    'collab.version.title.lyrics.vocals': 'Lyrics creation completed, vocal recording finished',
    'collab.version.title.arrangement.framework': 'Basic arrangement framework built',
    'collab.version.title.concept.style': 'Concept and style positioning determined',
    'collab.author.team.discussion': 'Team Discussion',
    'collab.change.main.recording': 'Completed main verse recording',
    'collab.change.drums.bass': 'Added drums and bass',
    'collab.change.initial.mixing': 'Initial mixing adjustments',
    'collab.change.beat.sync.fix': 'Fixed beat synchronization issues',
    'collab.change.lyrics.complete': 'Completed full lyrics creation',
    'collab.change.demo.recording': 'Recorded demo version',
    'collab.change.lyrics.rhythm': 'Adjusted lyrics rhythm',
    'collab.change.harmony.arrangement': 'Added harmony arrangement',
    'collab.change.song.structure': 'Determined overall song structure',
    'collab.change.chord.progression': 'Created basic chord progression',
    'collab.change.melody.framework': 'Designed main melody framework',
    'collab.change.production.timeline': 'Established production timeline',
    'collab.change.pop.rock.style': 'Determined pop rock style',
    'collab.change.theme.direction': 'Discussed song theme direction',
    'collab.change.division.plan': 'Established division plan',
    'collab.change.project.milestones': 'Set project milestones',
    
    // File name translations
    'collab.file.main.melody': 'Main Melody',
    'collab.file.drums': 'Drums',
    'collab.file.lyrics.final': 'Lyrics_Final',
    'collab.file.demo.vocal': 'Demo Vocal',
    'collab.file.arrangement.sketch': 'Arrangement Sketch',
    'collab.file.chord.chart': 'Chord Chart',
    'collab.file.project.plan': 'Project Plan',
    'collab.file.style.reference': 'Style Reference',
    'collab.categories.composition': 'Find Composer',
    'collab.categories.singer': 'Find Singer',
    'collab.categories.mixing': 'Find Mixing',
    'collab.categories.production': 'Find Production',
    
    // Musicians
    'musicians.title': 'Musicians',
    'musicians.subtitle': 'Discover talented music creators and start your musical collaboration journey',
    'musicians.search.placeholder': 'Search musicians...',
    'musicians.category.title': 'Categories',
    'musicians.category.all': 'All',
    'musicians.category.singer': 'Singer',
    'musicians.category.producer': 'Producer',
    'musicians.category.composer': 'Composer',
    'musicians.category.lyricist': 'Lyricist',
    'musicians.category.rapper': 'Rapper',
    'musicians.followers': 'Followers',
    'musicians.works': 'Works',
    'musicians.rating': 'Rating',
    'musicians.verified': 'Verified',
    'musicians.follow': 'Follow Musician',
    'musicians.follow.success': 'Followed',
    'musicians.message': 'Message',
    'musicians.view.profile': 'View Profile',
    'musicians.no.results': 'No musicians found',
    'musicians.no.results.desc': 'Try adjusting search criteria or browse other categories',
    
    // Musician mock data  
    'musician.1.name': 'Lin Yuhan',
    'musician.1.description': 'Focuses on modern pop music creation and singing, excels at integrating classical elements into modern music to create unique musical atmospheres',
    'musician.2.name': 'Zhang Zhihua',
    'musician.2.description': 'Experienced music producer with rich experience in electronic music production, has produced albums for many well-known artists',
    'musician.3.name': 'Chen Mengqi',
    'musician.3.description': 'Emerging musician who stands out in the music industry with unique voice and strong personal style, deeply loved by young audiences',
    'musician.4.name': 'Liu Dongjing',
    'musician.4.description': 'Independent musician proficient in multiple instruments with diverse musical styles ranging from folk to rock',
    'musician.5.name': 'Wang Yimeng',
    'musician.5.description': 'Talented lyricist with sincere emotions and beautiful words, skilled at conveying deep emotions and thoughts through music',
    'musician.6.name': 'Zheng Jianguo',
    'musician.6.description': 'Jazz performer proficient in piano and saxophone, elegant and infectious musical style, frequently performs at major music festivals',
    
    // Genre translations
    'genre.singer': 'Singer',
    'genre.composer': 'Composer',
    'genre.lyricist': 'Lyricist', 
    'genre.instrumental': 'Instrumental',
    
    // Tag translations
    'tag.pop': 'Pop',
    'tag.classical': 'Classical',
    'tag.creative': 'Creative',
    'tag.electronic': 'Electronic',
    'tag.production': 'Production',
    'tag.arrangement': 'Arrangement',
    'tag.independent': 'Independent',
    'tag.folk': 'Folk',
    'tag.rock': 'Rock',
    'tag.multiinstrument': 'Multi-instrument',
    'tag.lyrics': 'Lyrics',
    'tag.literature': 'Literature',
    'tag.emotional': 'Emotional',
    'tag.jazz': 'Jazz',
    'tag.piano': 'Piano',
    'tag.saxophone': 'Saxophone',
    
    // Location translations
    'location.beijing': 'Beijing',
    'location.shanghai': 'Shanghai',
    'location.guangzhou': 'Guangzhou',
    'location.shenzhen': 'Shenzhen',
    'location.chengdu': 'Chengdu',
    'location.hangzhou': 'Hangzhou',
    
    // Musician Detail Data
    'musician.detail.name': 'Eason Chan',
    'musician.detail.genre': 'Singer',
    'musician.detail.location': 'Famous Hong Kong singer, beloved by audiences for his unique voice and emotionally rich singing style. His musical works span multiple genres including pop, rock, and electronic.',
    'musician.detail.description': 'Famous Hong Kong singer, beloved by audiences for his unique voice and emotionally rich singing style. His musical works span multiple genres including pop, rock, and electronic, committed to bringing more high-quality music to listeners.',
    'musician.detail.tag.pop': 'Pop',
    'musician.detail.tag.rock': 'Rock', 
    'musician.detail.tag.creative': 'Creative',
    'musician.detail.tag.vocal': 'Vocal',
    
    // Song titles
    'song.1.title': 'Training Camp Singing',
    'song.2.title': 'Under Mount Fuji',
    'song.3.title': 'Accompany You Through Long Years',
    'song.4.title': 'Crying for You',
    'song.5.title': 'Best Bad Friend',
    'song.6.title': 'Ten Years',
    'song.7.title': 'Thousands',
    'song.8.title': 'Don\'t Say a Word',
    'song.9.title': 'Ambush from Ten Sides',
    'song.10.title': 'Long Time No See',
    
    // Album titles
    'album.1.title': 'Collection Album',
    'album.2.title': 'Greatest Hits',
    'album.3.title': 'New Works',
    'musicians.loading': 'Loading...',
    
    // DAW Project Manager
    'daw.demo.project1': 'My New Work',
    'daw.demo.project2': 'Summer Memories',
    'daw.project.manager.title': 'Project Manager',
    'daw.recent.projects': 'Recent Projects',
    'daw.project.templates': 'Project Templates',
    'daw.shared.projects': 'Shared Projects',
    'daw.create.new.project': 'Create New Project',
    'daw.project.name': 'Project Name',
    'daw.create': 'Create',
    'daw.cancel': 'Cancel',
    'daw.last.modified': 'Last Modified',
    'daw.collaborators': 'Collaborators',
    'daw.no.projects': 'No Projects',
    'daw.create.first.project': 'Create Your First Project',
    
    // DAW TrackList translations
    'daw.tracks.title': 'Tracks',
    'daw.add.track': 'Add Track',
    'daw.audio.track': 'Audio Track',
    'daw.audio.recording.track': 'Audio Recording Track',
    'daw.midi.instruments': 'MIDI Instruments',
    
    // Account MyHomepage translations
    'account.homepage.quick.actions.live': 'Live Video',
    'account.homepage.quick.actions.photo': 'Photo/Video',
    'account.homepage.quick.actions.music.life': 'Music Life',
    'account.homepage.post.minutes.ago': 'minutes ago',
    'account.homepage.post.author': 'Fred Yee',
    'account.homepage.post.content1': 'Looking for musicians to collaborate on several songs',
    'account.homepage.post.content2': 'Looking for a composer, lyrics are ready',
    
    // Musician Detail
    'musician.detail.followers': 'Followers',
    'musician.detail.following': 'Following',
    'musician.detail.works': 'Works',
    'musician.detail.follow': '+ Follow',
    'musician.detail.followed': 'Following',
    'musician.detail.message': 'Message',
    'musician.detail.tabs.works': 'Works',
    'musician.detail.tabs.albums': 'Albums',
    'musician.detail.tabs.related': 'Related MV',
    'musician.detail.play.all': 'Play All',
    'musician.detail.collected.songs': 'Collected Songs',
    'musician.detail.sort.popular': 'Popular Works',
    'musician.detail.sort.latest': 'Latest Works',
    'musician.detail.sort.plays': 'Play Count',
    'musician.detail.recent.works': 'Recent Works',
    'musician.detail.plays': 'Plays',
    'musician.detail.related.musicians': 'Related Musicians',
    
    // Works
    'works.title': 'Works Subscription',
    'works.subtitle': 'Discover and subscribe to amazing musical works',
    'works.search.placeholder': 'Search works...',
    'works.category.title': 'Categories',
    'works.category.top': 'Top',
    'works.category.hot': 'Hot',
    'works.category.style': 'Style',
    'works.category.musician': 'Musicians',
    'works.subscription': 'Premium Subscription',
    'works.collaborators': ' collaborators',
    'works.play': 'Play',
    'works.like': 'Like',
    'works.favorite': 'Favorite',
    'works.share': 'Share',
    'works.comments': 'Comments',
    'works.no.results': 'No works found',
    'works.no.results.desc': 'Try adjusting search criteria or browse other categories',
    'works.loading': 'Loading...',
    'works.playing': 'Now playing: ',
    'works.liked': 'Liked work: ',
    'works.favorited': 'Favorited work: ',
    'works.share.dev': 'Share feature under development!',
    'works.my.works': 'My Works',
    'works.published': 'Published',
    'works.drafts': 'Drafts',
    'works.likes': 'Likes',
    'works.plays': 'Plays',
    'works.shares': 'Shares',
    
    // Work Detail
    'work.detail.play': 'Play',
    'work.detail.pause': 'Pause',
    'work.detail.like': 'Like',
    'work.detail.share': 'Share',
    'work.detail.comment': 'Comment',
    'work.detail.download': 'Download',
    'work.detail.collect': 'Collect',
    'work.detail.artist': 'Artist',
    'work.detail.duration': 'Duration',
    'work.detail.genre': 'Genre',
    'work.detail.release.date': 'Release Date',
    'work.detail.description': 'Description',
    'work.detail.tags': 'Tags',
    'work.detail.related.works': 'Related Works',
    'work.detail.comments.title': 'Comments',
    'work.detail.add.comment': 'Add Comment',
    'work.detail.post.comment': 'Post Comment',
    'work.detail.not.exist': 'Work does not exist',
    'work.detail.back.to.list': 'Back to works list',
    'work.detail.intro': 'Work Description',
    'work.detail.copyright': 'Copyright Info',
    'work.detail.composer': 'Composer',
    'work.detail.lyricist': 'Lyricist',
    'work.detail.producer': 'Producer',
    'work.detail.label': 'Publisher',
    'work.detail.release.time': 'Release Time',
    'work.detail.plays.count': 'Play Count',
    'work.detail.write.comment': 'Write comment...',
    'work.detail.post': 'Comment',
    'work.detail.reply': 'Reply',
    'work.detail.verified': 'Verified',
    'work.detail.just.now': 'Just now',
    'work.detail.likes': ' likes',
    'work.detail.plays.k': 'k plays',
    
    // DAW
    'daw.title': 'DAW',
    'daw.projects': 'Projects',
    'daw.save.project': 'Save Project',
    'daw.export.data': 'Export Project Data',
    'daw.show.automation': 'Show Automation',
    'daw.hide.automation': 'Hide Automation',
    'daw.connected': 'Backend Connected',
    'daw.offline': 'Offline Mode',
    'daw.tracks': 'Tracks',
    'daw.clips': 'Clips',
    'daw.events': 'Events',
    'daw.online': 'Online',
    'daw.saved': 'Saved',
    'daw.save.failed': 'Save Failed',
    'daw.instruments.piano': 'Piano',
    'daw.instruments.guitar': 'Guitar',
    'daw.instruments.bass': 'Bass',
    'daw.instruments.trumpet': 'Trumpet',
    'daw.instruments.violin': 'Violin',
    'daw.instruments.drums': 'Drums',
    'daw.instruments.synth': 'Synth',
    'daw.instruments.flute': 'Flute',
    'daw.instruments.saxophone': 'Saxophone',
    'daw.instruments.clarinet': 'Clarinet',
    'daw.instruments.cello': 'Cello',
    'daw.instruments.harp': 'Harp',
    'daw.instruments.organ': 'Organ',
    'daw.instruments.electricpiano': 'Electric Piano',
    'daw.instruments.electricguitar': 'Electric Guitar',
    'daw.instruments.acousticguitar': 'Acoustic Guitar',
    'daw.instruments.electricbass': 'Electric Bass',
    'daw.instruments.upright_bass': 'Upright Bass',
    'daw.instruments.french_horn': 'French Horn',
    'daw.instruments.trombone': 'Trombone',
    'daw.instruments.tuba': 'Tuba',
    'daw.instruments.oboe': 'Oboe',
    'daw.instruments.piccolo': 'Piccolo',
    'daw.instruments.banjo': 'Banjo',
    'daw.instruments.mandolin': 'Mandolin',
    'daw.instruments.marimba': 'Marimba',
    'daw.instruments.vibraphone': 'Vibraphone',
    'daw.instruments.xylophone': 'Xylophone',
    'daw.instruments.timpani': 'Timpani',
    'daw.instruments.pad': 'Pad',
    'daw.instruments.lead': 'Lead Synth',
    'daw.instruments.bass_synth': 'Bass Synth',
    'daw.instruments.arpeggiator': 'Arpeggiator',
    'daw.instruments.string_ensemble': 'String Ensemble',
    'daw.track.audio': 'Audio Track',
    
    // DAW Project Manager translations
    'daw.new.project': 'New Project',
    'daw.loading.projects': 'Loading projects...',
    'daw.no.shared': 'No shared projects yet.',
    'daw.create.new': 'Create New Project',
    'daw.name.placeholder': 'Enter project name...',
    'daw.close': 'Close',
    'daw.use.template': 'Use Template',
    
    // Additional DAW translations
    'daw.tempo': 'Tempo',
    'daw.bpm': 'BPM',
    'daw.time.signature': 'Time Signature',
    'daw.visibility.private': 'Private',
    'daw.visibility.public': 'Public',
    'daw.visibility.collaborative': 'Collaborative',
    'daw.project.from': 'from',
    
    // Chat/Social
    'social.new.post.placeholder': 'Share your musical thoughts...',
    'social.image': 'Image',
    'social.audio': 'Audio',
    'social.video': 'Video',
    'social.publish': 'Publish',
    'social.publishing': 'Publishing...',
    'social.just.now': 'Just now',
    'social.hours.ago': 'hours ago',
    'social.days.ago': 'days ago',
    'social.current.user': 'Current User',
    
    // Account Details
    'account.basic.info': 'Basic Info',
    'account.security': 'Account Security',
    'account.privacy': 'Privacy Settings',
    'account.notifications': 'Notification Settings',
    'account.demo.user': 'Demo User',
    'account.change.avatar': 'Change Avatar',
    'account.username': 'Username',
    'account.nickname': 'Nickname',
    'account.email': 'Email',
    'account.phone': 'Phone',
    'account.birthday': 'Birthday',
    'account.gender': 'Gender',
    'account.bio': 'Bio',
    'account.location': 'Location',
    'account.save': 'Save',
    'account.cancel': 'Cancel',
    'account.settings': 'Account Settings',
    'account.bio.demo': 'This is a demo user bio. Love music, enjoy creating, often participate in collaborative projects.',
    'account.save.changes': 'Save Changes',
    'account.security.tip.title': 'Account Security Tips',
    'account.security.tip.desc': 'Regularly update passwords, enable two-factor authentication to protect account security',
    'account.password': 'Login Password',
    'account.password.last.modified': 'Last modified: 2024-01-01',
    'account.change.password': 'Change Password',
    'account.two.factor.title': 'Two-Factor Authentication',
    'account.two.factor.desc': 'Improve account security',
    'account.enable': 'Enable',
    'account.device.management.title': 'Login Device Management',
    'account.device.management.desc': 'View and manage login devices',
    'account.view.devices': 'View Devices',
    'account.profile.visibility.title': 'Profile Visibility',
    'account.profile.visibility.desc': 'Control who can view your profile',
    'account.works.visibility.title': 'Works Visibility',
    'account.works.visibility.desc': 'Control who can view your works',
    'account.allow.messages.title': 'Allow Messages',
    'account.allow.messages.desc': 'Control who can send you messages',
    'account.visibility.everyone': 'Everyone',
    'account.visibility.following': 'Following',
    'account.visibility.self': 'Only Me',
    'account.visibility.none': 'Not Allowed',
    'account.notification.followers.title': 'New Follower Notifications',
    'account.notification.followers.desc': 'Notify when someone follows you',
    'account.notification.comments.title': 'Work Comment Notifications',
    'account.notification.comments.desc': 'Notify when works are commented',
    'account.notification.system.title': 'System Notifications',
    'account.notification.system.desc': 'System messages and update notifications',
    'account.notification.email.title': 'Email Notifications',
    'account.notification.email.desc': 'Send important notifications via email',
    
    // Message Center translations
    'message.center': 'Message Center',
    'message.search.placeholder': 'Search messages...',
    'message.my.messages': 'My Messages',
    'message.replies': 'Replies to Me',
    'message.mentions': 'Mentions',
    'message.likes': 'Received Likes',
    'message.system': 'System Notifications',
    'message.private': 'Private Messages',
    'message.mark.all.read': 'Mark All Read',
    'message.clear': 'Clear',
    'message.no.messages': 'No messages',
    
    // MessageCenter mock data English translations
    'message.user.music.lover': 'Music Lover',
    'message.user.creator': 'Creator',
    'message.user.producer': 'Music Producer',
    'message.user.system': 'System Message',
    'message.user.partner': 'Collaboration Partner',
    'message.user.student': 'Music Student',
    'message.user.band.leader': 'Band Leader',
    'message.user.fan': 'Fan',
    'message.user.company': 'Music Company',
    'message.content.liked.work': 'liked your work "Nocturne"',
    'message.content.replied.comment': 'replied to your comment: This song has great artistic conception, looking forward to more works',
    'message.content.mentioned': 'mentioned you in a post: @demo_user let\'s listen to this new work',
    'message.content.work.approved': 'Your work "Spring Story" has been approved',
    'message.content.collaboration.discuss': 'Want to discuss new project collaboration with you',
    'message.content.replied.thanks': 'replied to you: Thank you teacher for your guidance, I will continue to work hard',
    'message.content.mentioned.arrangement': 'mentioned you in comment: @demo_user what do you think of this arrangement?',
    'message.content.liked.post': 'liked your post',
    'message.content.collaboration.proposal': 'Hope to collaborate with you, please check the collaboration proposal',
    'message.time.2min.ago': '2 minutes ago',
    'message.time.15min.ago': '15 minutes ago',
    'message.time.1hour.ago': '1 hour ago',
    'message.time.2hours.ago': '2 hours ago',
    'message.time.3hours.ago': '3 hours ago',
    'message.time.30min.ago': '30 minutes ago',
    'message.time.1day.ago': '1 day ago',
    'message.time.2days.ago': '2 days ago',
    'allspaces.discussion.title': 'Team Discussion',
    'allspaces.discussion.new': 'Start New Discussion',
    'allspaces.discussion.empty.title': 'No discussions yet',
    'allspaces.discussion.empty.desc': 'Start your first team discussion!',
    'allspaces.discussion.placeholder': 'Join the discussion...',
    'allspaces.activity.title': 'Activity Log',
    'allspaces.activity.recent': 'Last 7 days',
    'allspaces.activity.empty.title': 'No activity records yet',
    'allspaces.activity.empty.desc': 'Activity records will appear here when members start collaborating',
    'allspaces.activity.view.more': 'View More Activity Records',
    'allspaces.invite.modal.title': 'Invite Member',
    'allspaces.invite.role': 'Role',
    'allspaces.invite.role.select': 'Select Role',
    'allspaces.invite.role.lyrics': 'Lyrics',
    'allspaces.invite.role.compose': 'Composition',
    'allspaces.invite.role.arrange': 'Arrangement',
    'allspaces.invite.role.sing': 'Singer',
    'allspaces.invite.role.mix': 'Mixing',
    'allspaces.invite.message': 'Invitation Message',
    'allspaces.share.social': 'Share to Social Media',
    'allspaces.share.wechat': 'WeChat',
    'allspaces.share.weibo': 'Weibo',
    'allspaces.share.qq': 'QQ',
    'allspaces.share.copy': 'Copy Link',
    'allspaces.discussion.modal.title': 'Start New Discussion',
    'allspaces.discussion.modal.title.label': 'Discussion Title',
    'allspaces.discussion.modal.title.placeholder': 'Enter discussion title',
    'allspaces.discussion.modal.content.label': 'Discussion Content',
    'allspaces.discussion.modal.content.placeholder': 'Describe what you want to discuss...',
    'allspaces.discussion.modal.start': 'Start Discussion',
    'allspaces.discussion.started': 'Discussion started!',
    
    // Creative Center translations
    'creative.my.works': 'My Works',
    'creative.my.collaborations': 'My Collaborations',
    'creative.my.playlists': 'My Playlists',
    'creative.my.favorites': 'My Favorites',
    'creative.upload.work': 'Upload New Work',
    'creative.join.collaboration': 'Join Collaboration',
    'creative.create.playlist': 'Create Playlist',
    'creative.published': 'Published',
    'creative.draft': 'Draft',
    'creative.edit': 'Edit',
    'creative.classical': 'Classical',
    'creative.folk': 'Folk',
    'creative.electronic': 'Electronic',
    'creative.collaboration.members': 'Collaboration Members',
    'creative.my.role': 'My Role',
    'creative.arrangement': 'Arrangement',
    'creative.lyrics': 'Lyrics',
    'creative.completed': 'Completed',
    'creative.in.progress': 'In Progress',
    'creative.progress': 'Progress',
    'creative.deadline': 'Deadline',
    'creative.songs.count': ' songs',
    'creative.public': 'Public',
    'creative.private': 'Private',
    'creative.created.at': 'Created on',
    'creative.total.songs': 'Total',
    'creative.added.at': 'Added on',
    'creative.member.producer': 'Music Producer',
    'creative.member.pianist': 'Pianist',
    'creative.member.violinist': 'Violinist',
    'creative.member.singer': 'Singer-Songwriter',
    'creative.member.guitarist': 'Guitarist',
    'creative.role.arrangement': 'Arrangement',
    'creative.role.lyrics': 'Lyrics',
    'creative.playlist.my.original': 'My Originals',
    'creative.playlist.classical.favorites': 'Classical Favorites',
    'creative.playlist.relaxation': 'Relaxation Time',
    'creative.favorite.moonlight.sonata': 'Moonlight Sonata',
    'creative.favorite.jasmine.flower': 'Jasmine Flower',
    'creative.artist.beethoven': 'Beethoven',
    'creative.artist.folk.music': 'Folk Music',
    'creative.upload.new.work': 'Upload New Work',
    'creative.songs': ' songs',
    'creative.created.on': 'Created on',
    'creative.total': 'Total',
    'creative.favorited.on': 'Favorited on',
    
    // MyHomepage English translations
    'myhomepage.cover.add': 'Add Cover Photo',
    'myhomepage.cover.alt': 'Cover',
    'myhomepage.publish.news': 'Publish News',
    'myhomepage.edit.profile': 'Edit Profile',
    'myhomepage.personal.profile': 'Personal Profile',
    'myhomepage.add.signature': 'Add Signature',
    'myhomepage.edit.details': 'Edit Details',
    'myhomepage.add.featured': 'Add Featured',
    'myhomepage.works.title': 'Works',
    'myhomepage.all.works': 'All Works',
    'myhomepage.no.works': 'No works',
    'myhomepage.friends.title': 'Friends',
    'myhomepage.all.friends': 'All Friends',
    'myhomepage.no.friends': 'No friends',
    'myhomepage.privacy.policy': 'Privacy Policy',
    'myhomepage.terms.service': 'Terms of Service',
    'myhomepage.advertising': 'Advertising',
    'myhomepage.ad.choices': 'Ad Choices',
    'myhomepage.cookies': 'Cookies',
    'myhomepage.more': 'More',
    'myhomepage.copyright': 'iBOM © 2025',
    'myhomepage.share.music': 'Good music to share',
    'myhomepage.introduce.yourself': 'Introduce yourself',
    'myhomepage.public': 'Public',
    'myhomepage.cancel': 'Cancel',
    'myhomepage.save': 'Save',
    'myhomepage.updated.status': 'updated status',
    'myhomepage.like.action': 'Like',
    'myhomepage.comment.action': 'Comment',
    'myhomepage.send.action': 'Send',
    'myhomepage.share.action': 'Share',
    'myhomepage.write.comment': 'Write comment...',
    'myhomepage.support.cv.link': 'Support CV link generation',
    'myhomepage.avatar.alt': 'Avatar',
    
    // PersonalProfile English translations
    'profile.basic.info': 'Basic Info',
    'profile.certification': 'Certification Info',
    'profile.binding': 'Account Binding',
    'profile.security': 'Account Security',
    'profile.preferences': 'Preferences',
    'profile.demo.user': 'Demo User',
    'profile.normal.user': 'Normal User',
    'profile.verified': 'Real Name Verified',
    'profile.real.name': 'Real Name',
    'profile.gender': 'Gender',
    'profile.male': 'Male',
    'profile.female': 'Female',
    'profile.secret': 'Private',
    'profile.birth.date': 'Birth Date',
    'profile.location': 'Location',
    'profile.bio': 'Bio',
    'profile.bio.demo': 'This is a demo user bio. Love music, enjoy creating, often participate in collaborative projects.',
    'profile.music.style': 'Music Style Preference',
    'profile.style.pop': 'Pop',
    'profile.style.classical': 'Classical',
    'profile.style.rock': 'Rock',
    'profile.style.jazz': 'Jazz',
    'profile.style.electronic': 'Electronic',
    'profile.style.folk': 'Folk',
    'profile.style.hiphop': 'Hip-Hop',
    'profile.style.country': 'Country',
    'profile.save.changes': 'Save Changes',
    'profile.cert.description': 'Certification Info',
    'profile.cert.benefit': 'Complete certification to get more privileges and trust',
    'profile.real.name.cert': 'Real Name Certification',
    'profile.musician.cert': 'Musician Certification',
    'profile.company.cert': 'Company Certification',
    'profile.certified': 'Certified',
    'profile.apply.cert': 'Apply Certification',
    'profile.cert.time': 'Certified: 2024-01-01',
    'profile.musician.cert.desc': 'Musician certification provides professional badge and more exposure opportunities',
    'profile.musician.cert.requires': 'Required documents:',
    'profile.musician.works': 'Music portfolio',
    'profile.musician.id': 'ID proof',
    'profile.musician.certificates': 'Music-related certificates or proofs',
    'profile.company.cert.desc': 'Company certification for music companies, studios and other institutions',
    'profile.company.requires': 'Required documents:',
    'profile.business.license': 'Business license',
    'profile.company.intro': 'Company introduction',
    'profile.legal.person.id': 'Legal person ID proof',
    'profile.binding.tip.title': 'Binding Tips',
    'profile.binding.tip.desc': 'Bind third-party accounts for quick login and sharing',
    'profile.wechat': 'WeChat',
    'profile.wechat.bound': 'Bound: wx****8888',
    'profile.qq': 'QQ',
    'profile.github': 'GitHub',
    'profile.weibo': 'Weibo',
    'profile.not.bound': 'Not bound',
    'profile.bind': 'Bind',
    'profile.unbind': 'Unbind',
    'profile.security.tip.title': 'Security Tips',
    'profile.security.tip.desc': 'Please update password regularly to protect account security',
    'profile.login.password': 'Login Password',
    'profile.password.modified': 'Last modified: 2024-01-01',
    'profile.change.password': 'Change Password',
    'profile.phone.number': 'Phone Number',
    'profile.change.phone': 'Change Phone',
    'profile.email.address': 'Email Address',
    'profile.change.email': 'Change Email',
    'profile.two.factor': 'Two-Factor Authentication',
    'profile.two.factor.disabled': 'Disabled',
    'profile.enable': 'Enable',
    'profile.interface.settings': 'Interface Settings',
    'profile.dark.mode': 'Dark Mode',
    'profile.font.size': 'Font Size',
    'profile.font.small': 'Small',
    'profile.font.medium': 'Medium',
    'profile.font.large': 'Large',
    'profile.language': 'Language',
    'profile.chinese': '中文',
    'profile.english': 'English',
    'profile.music.playback': 'Music Playback',
    'profile.auto.play': 'Auto Play',
    'profile.audio.quality': 'Audio Quality',
    'profile.quality.standard': 'Standard',
    'profile.quality.high': 'High Quality',
    'profile.quality.lossless': 'Lossless',
    'profile.volume': 'Volume',
    'profile.privacy.settings': 'Privacy Settings',
    'profile.profile.visibility': 'Profile Visibility',
    'profile.visibility.everyone': 'Everyone',
    'profile.visibility.friends': 'Friends',
    'profile.visibility.self': 'Only Me',
    'profile.allow.private.messages': 'Allow Private Messages',
    'profile.show.online.status': 'Show Online Status',
    'profile.save.settings': 'Save Settings',
    
    // Rewards translations
    'rewards.overview': 'Rewards Overview',
    'rewards.earn.methods': 'Quick Earn Methods',
    'rewards.daily.checkin': 'Daily Check-in',
    'rewards.invite.friends': 'Invite Friends',
    'rewards.history': 'Rewards History',
    'rewards.rules': 'View Rules',
    'rewards.total.points': 'Total Points',
    'rewards.gold.coins': 'Gold Coins',
    'rewards.crystal.coins': 'Crystal Coins',
    'rewards.level.progress': 'Level Progress',
    'rewards.current.level': 'Current Level',
    'rewards.need.points': 'Need',
    'rewards.days': 'Days',
    'rewards.day': 'Day',
    'rewards.points.needed': 'Need',
    'rewards.points.to.next': 'more points',
    'rewards.checkin.stats': 'Check-in Stats',
    'rewards.consecutive.days': 'Consecutive check-in days',
    'rewards.invite.stats': 'Invite Stats',
    'rewards.successful.invites': 'Successfully invited friends',
    'rewards.quick.earn.points': 'Quick ways to earn points',
    'rewards.daily.checkin.desc': 'Get more rewards for consecutive check-ins',
    'rewards.upload.work': 'Upload Work',
    'rewards.upload.work.desc': 'Publish original music works',
    'rewards.invite.friend.desc': 'Invite friends to register and verify',
    'rewards.complete.profile': 'Complete Profile',
    'rewards.complete.profile.desc': 'Complete personal profile verification',
    'rewards.go.complete': 'Go Complete',
    'rewards.consecutive.checkin': 'Consecutive check-in',
    'rewards.checkin.now': 'Check In Now',
    'rewards.already.checked': 'Already checked in today',
    'rewards.type': 'Type',
    'rewards.description': 'Description',
    'rewards.points': 'Points',
    'rewards.coins': 'Coins',
    'rewards.crystals': 'Crystals',
    'rewards.time': 'Time',
    'rewards.checkin': 'Check-in',
    'rewards.upload': 'Upload',
    'rewards.invite': 'Invite',
    'rewards.level.up': 'Level Up',
    'rewards.invite.friends.title': 'Invite Friends',
    'rewards.invite.desc': 'Invite friends to register and complete verification, both you and your friends can get generous rewards!',
    'rewards.invite.reward': 'Invite Rewards',
    'rewards.invite.link': 'Your invite link:',
    'rewards.copy': 'Copy',
    'rewards.successful.invites.count': 'Successfully Invited',
    'rewards.earned.points': 'Earned Points',
    'rewards.earn.rules': 'Reward Rules',
    'rewards.points.rules': 'Points Earning Rules',
    'rewards.coins.rules': 'Coins Earning Rules',
    'rewards.crystals.rules': 'Crystals Earning Rules',
    'rewards.notes': 'Notes',
    
    // Wallet translations
    'wallet.balance.display': 'Balance Display',
    'wallet.transaction.details': 'Transaction Details',
    'wallet.purchase.history': 'Purchase History',
    'wallet.gold.balance': 'Gold Balance',
    'wallet.gold.usage': 'For purchasing memberships, items, etc.',
    'wallet.crystal.balance': 'Crystal Balance',
    'wallet.crystal.usage': 'Can be withdrawn to bank account',
    'wallet.purchase.coins': 'Purchase Coins',
    'wallet.withdraw.crystals': 'Withdraw Crystals',
    'wallet.transaction.history': 'Transaction History',
    'wallet.spending.stats': 'Spending Statistics',
    'wallet.monthly.spending': 'Monthly Spending',
    'wallet.monthly.income': 'Monthly Income',
    'wallet.usage.guide': 'Usage Guide',
    'wallet.all.types': 'All Types',
    'wallet.purchase': 'Purchase',
    'wallet.spending': 'Spending',
    'wallet.income': 'Income',
    'wallet.withdraw': 'Withdraw',
    'wallet.all.currencies': 'All Currencies',
    'wallet.gold.coin': 'Gold Coins',
    'wallet.crystal.coin': 'Crystal Coins',
    'wallet.amount': 'Amount',
    'wallet.status': 'Status',
    'wallet.completed': 'Completed',
    'wallet.pending': 'Pending',
    'wallet.purchase.amount': 'Amount',
    'wallet.currency.type': 'Type',
    'wallet.price': 'Price',
    'wallet.payment.method': 'Payment Method',
    'wallet.wechat.pay': 'WeChat Pay',
    'wallet.alipay': 'Alipay',
    'wallet.payment.success': 'Payment Successful',
    
    // MyWallet Complete English Translations
    'wallet.tab.balance': 'Balance',
    'wallet.tab.transactions': 'Transactions', 
    'wallet.tab.purchase.history': 'Purchase History',
    'wallet.gold.description': 'For purchasing memberships, items, etc.',
    'wallet.crystal.description': 'Can be withdrawn to bank account',
    'wallet.crystal.withdraw': 'Withdraw',
    'wallet.purchase.gold': 'Purchase Gold',
    'wallet.currency.gold': 'Gold Coins',
    'wallet.currency.crystal': 'Crystal Coins',
    'wallet.filter.all.types': 'All Types',
    'wallet.filter.all.currencies': 'All Currencies',
    'wallet.type.purchase': 'Purchase',
    'wallet.type.spend': 'Spend',
    'wallet.type.earn': 'Earn',
    'wallet.type.withdraw': 'Withdraw',
    'wallet.status.completed': 'Completed',
    'wallet.status.pending': 'Pending',
    'wallet.status.payment.success': 'Payment Successful',
    'wallet.table.description': 'Description',
    'wallet.table.amount': 'Amount',
    'wallet.table.type': 'Type',
    'wallet.table.status': 'Status',
    'wallet.table.time': 'Time',
    'wallet.table.price': 'Price',
    'wallet.table.payment.method': 'Payment Method',
    'wallet.payment.wechat': 'WeChat Pay',
    'wallet.payment.alipay': 'Alipay',
    'wallet.spending.statistics': 'Spending Statistics',
    'wallet.transaction.purchase.gold': 'Purchase Gold Coins',
    'wallet.transaction.purchase.vip': 'Purchase VIP Membership',
    'wallet.transaction.purchase.crystal': 'Purchase Crystal Coins',
    'wallet.transaction.reward.income': 'Reward Income',
    'wallet.transaction.crystal.withdraw': 'Crystal Coins Withdrawal',
    'wallet.transactions': 'Transactions',
    'wallet.usage.gold.description': 'Gold coins can be used to purchase VIP memberships, virtual items, etc.',
    'wallet.usage.crystal.earn': 'Earn crystal coins through creation and collaboration',
    'wallet.usage.crystal.withdraw': 'Crystal coins can be withdrawn to bank account',
    'wallet.usage.transaction.view': 'View detailed transaction records',
    
    // Certification Complete English Translations
    'certification.artist.title': 'Artist Certification',
    'certification.user.title': 'User Certification',
    'certification.qualification.title': 'Qualification Certification',
    'certification.artist.description': 'Artist verification is for music professionals like musicians, singers, producers. After verification, you get professional badges and more platform benefits.',
    'certification.user.description': 'User verification is for regular users. After verification, you get more platform functionality.',
    'certification.qualification.description': 'Upload relevant qualification documents to help improve verification pass rate and account authority.',
    'certification.artist.info': 'Artist Information',
    'certification.user.info': 'User Information',
    'certification.artist.stageName': 'Stage Name/Real Name',
    'certification.artist.stageNamePlaceholder': 'Please enter stage name or real name',
    'certification.realName': 'Real Name',
    'certification.realNamePlaceholder': 'Please enter real name',
    'certification.idNumber': 'ID Number',
    'certification.idNumberPlaceholder': 'Please enter ID number',
    'certification.phone': 'Phone Number',
    'certification.phonePlaceholder': 'Please enter phone number',
    'certification.mobile': 'Mobile Number',
    'certification.mobilePlaceholder': 'Please enter mobile number',
    'certification.email': 'Email Address',
    'certification.emailPlaceholder': 'Please enter email address',
    'certification.birthDate': 'Birth Date',
    'certification.gender': 'Gender',
    'certification.genders.male': 'Male',
    'certification.genders.female': 'Female',
    'certification.genders.other': 'Other',
    'certification.selectGender': 'Please select gender',
    'certification.occupation': 'Occupation',
    'certification.occupationPlaceholder': 'Please enter occupation',
    'certification.address': 'Address',
    'certification.addressPlaceholder': 'Please enter address',
    'certification.musicType': 'Music Type',
    'certification.selectMusicType': 'Please select music type',
    'certification.musicTypes.pop': 'Pop Music',
    'certification.musicTypes.rock': 'Rock Music',
    'certification.musicTypes.jazz': 'Jazz Music',
    'certification.musicTypes.classical': 'Classical Music',
    'certification.musicTypes.electronic': 'Electronic Music',
    'certification.musicTypes.folk': 'Folk Music',
    'certification.musicTypes.hiphop': 'Hip-Hop Music',
    'certification.musicTypes.country': 'Country Music',
    'certification.musicTypes.other': 'Other',
    'certification.experience': 'Years of Experience',
    'certification.selectExperience': 'Please select years of experience',
    'certification.experienceYears.0-1': '0-1 years',
    'certification.experienceYears.1-3': '1-3 years',
    'certification.experienceYears.3-5': '3-5 years',
    'certification.experienceYears.5-10': '5-10 years',
    'certification.experienceYears.10+': '10+ years',
    'certification.bio': 'Biography',
    'certification.bioPlaceholder': 'Please briefly introduce your music experience and expertise',
    'certification.majorWorks': 'Major Works',
    'certification.majorWorksPlaceholder': 'Please list your major musical works',
    'certification.awards': 'Awards',
    'certification.awardsPlaceholder': 'Please describe your awards (optional)',
    'certification.personalPhoto': 'Personal Photo',
    'certification.idCardFront': 'ID Card Front',
    'certification.idCardBack': 'ID Card Back',
    'certification.idCardClear': 'Please ensure ID card photos are clear',
    'certification.workProof': 'Work Proof',
    'certification.workProofDescription': 'Upload files that prove your work',
    'certification.education': 'Education Certificate',
    'certification.educationDescription': 'Upload education certificate or related background proof',
    'certification.certificate': 'Professional Certificate',
    'certification.certificateDescription': 'Upload professional skill certificates or qualifications',
    'certification.awardCertificate': 'Award Certificate',
    'certification.awardDescription': 'Upload award certificates or related honor proof',
    'certification.copyrightProof': 'Copyright Proof',
    'certification.copyrightDescription': 'Upload copyright registration proof or related files',
    'certification.mediaReport': 'Media Report',
    'certification.mediaDescription': 'Upload media report screenshots or links',
    'certification.otherProof': 'Other Proof',
    'certification.otherDescription': 'Upload other relevant proof materials',
    'certification.additionalInfo': 'Additional Information',
    'certification.additionalInfoPlaceholder': 'Please provide additional information if needed',
    'certification.uploadPersonalPhoto': 'Click to upload personal photo',
    'certification.uploadIdCardFront': 'Click to upload ID card front',
    'certification.uploadIdCardBack': 'Click to upload ID card back',
    'certification.uploadWorkProof': 'Click to upload work proof',
    'certification.uploadEducation': 'Click to upload education certificate',
    'certification.uploadCertificate': 'Click to upload professional certificate',
    'certification.uploadAwardCertificate': 'Click to upload award certificate',
    'certification.uploadCopyrightProof': 'Click to upload copyright proof',
    'certification.uploadMediaReport': 'Click to upload media report',
    'certification.uploadOtherProof': 'Click to upload other proof',
    'certification.photoFormat': 'Supports JPG, PNG formats, file size not exceeding 5MB',
    'certification.benefits': 'Certification Benefits',
    'certification.benefitsList.verification': 'Get certification badge to improve account credibility',
    'certification.benefitsList.features': 'Enjoy more platform functions and services',
    'certification.benefitsList.events': 'Priority participation in platform activities and promotions',
    'certification.benefitsList.security': 'Enhanced account security and permissions',
    'certification.notes': 'Important Notes',
    'certification.notesList.authentic': 'Please ensure all provided information is authentic and valid',
    'certification.notesList.clarity': 'Please keep uploaded images clear and readable',
    'certification.notesList.formats': 'Supports common image formats (JPG, PNG, etc.)',
    'certification.notesList.fileSize': 'Single file size should not exceed 5MB',
    'certification.notesList.quality': 'Higher photo quality leads to higher approval rate',
    'certification.reviewNotice': 'Review Instructions',
    'certification.reviewRules.timeframe': 'Review Time: Usually completed within 3-7 business days',
    'certification.reviewRules.truthful': 'Please ensure provided information is truthful and valid, false information will be rejected',
    'certification.reviewRules.contact': 'If you have questions, you can contact us through customer service',
    'certification.reviewRules.badge': 'You will receive corresponding certification badge after approval',
    'certification.submit': 'Submit Certification Application',
    
    // Homepage Community Section
    'homepage.stats.creators': 'Music Creators',
    'homepage.stats.works': 'Musical Works',
    'homepage.stats.projects': 'Collaboration Projects',
    'homepage.stats.events': 'Monthly Events',
    'homepage.join.now': 'Join Now',
    'homepage.form.name.placeholder': 'Your stage name or real name',
    'homepage.form.email.placeholder': 'Email address',
    'homepage.form.style.placeholder': 'Choose your music style',
    'homepage.form.style.pop': 'Pop Music',
    'homepage.form.style.rock': 'Rock Music',
    'homepage.form.style.electronic': 'Electronic Music',
    'homepage.form.style.jazz': 'Jazz Music',
    'homepage.form.style.classical': 'Classical Music',
    'homepage.form.style.hiphop': 'Hip-Hop Music',
    
    // Footer
    'footer.description': 'Professional music collaboration social platform connecting musicians worldwide',
    'footer.products': 'Products',
    'footer.products.collaboration': 'Collaboration Spaces',
    'footer.products.daw': 'DAW Tools',
    'footer.products.live': 'Live Features',
    'footer.products.ai': 'AI Assistant',
    'footer.community': 'Community',
    'footer.community.musicians': 'Musicians',
    'footer.community.works': 'Works Showcase',
    'footer.community.events': 'Event Center',
    'footer.community.help': 'Help Center',
    'footer.about': 'About Us',
    'footer.about.company': 'Company Info',
    'footer.about.contact': 'Contact Us',
    'footer.about.privacy': 'Privacy Policy',
    'footer.about.terms': 'Terms of Service',
    'footer.copyright': '© 2024 iBOM. All rights reserved.',
    
    // Common
    'common.home': 'Home',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.sort': 'Sort',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.confirm': 'Confirm',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.view': 'View',
    'common.more': 'More',
    'common.loading': 'Loading...',
    'common.error': 'Error occurred',
    'common.success': 'Success',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    
    // Language
    'lang.chinese': '中文',
    'lang.english': 'English',
    
    // Time
    'time.now': 'just now',
    'time.minutes.ago': 'minutes ago',
    'time.hours.ago': 'hours ago',
    'time.days.ago': 'days ago',
    'time.weeks.ago': 'weeks ago',
    'time.months.ago': 'months ago',
    
    // Mock Work Titles
    'mock.work.title.summer.breeze': 'Summer Breeze',
    'mock.work.title.city.lights': 'City Lights',
    'mock.work.title.between.mountains.sea': 'Between Mountains and Sea',
    'mock.work.title.starry.walk': 'Starry Walk',
    'mock.work.title.city.rhythm': 'City Rhythm',
    'mock.work.title.deep.sea.sound': 'Deep Sea Sound',
    'mock.work.title.galaxy.travel': 'Galaxy Travel',
    'mock.work.title.mirage': 'Mirage',
    'mock.work.title.night.sky.wish': 'Night Sky Wish',
    'mock.work.title.spring.story': 'Spring Story',
    'mock.work.title.city.nightscape': 'City Nightscape',
    
    // Mock Work Descriptions
    'mock.work.desc.summer.breeze': 'Light and pleasant summer love song with memorable melodies',
    'mock.work.desc.city.lights': 'Electronic music describing modern urban nights',
    'mock.work.desc.between.mountains.sea': 'Creative work blending ethnic elements, showcasing the magnificence of nature',
    'mock.work.desc.starry.walk': 'Dreamy coastal music piece',
    'mock.work.desc.city.rhythm': 'Silent city rhythm, reflecting modern life',
    'mock.work.desc.deep.sea.sound': 'Immersive ambient music bringing tranquility',
    'mock.work.desc.galaxy.travel': 'A dreamlike musical piece that blends modern pop elements with traditional music essence, bringing listeners a brand new auditory experience.',
    
    // Mock Artist Names
    'mock.artist.chen.xiaowei': 'Chen Xiaowei',
    'mock.artist.marco': 'Marco',
    'mock.artist.wang.xiaoming': 'Wang Xiaoming',
    'mock.artist.li.shiyu': 'Li Shiyu',
    'mock.artist.zhang.sanhe': 'Zhang Sanhe',
    'mock.artist.zhao.liu': 'Zhao Liu',
    'mock.artist.qian.qi': 'Qian Qi',
    'mock.artist.sun.ba': 'Sun Ba',
    'mock.artist.li.si': 'Li Si',
    'mock.artist.wang.wu': 'Wang Wu',
    'mock.artist.tianyu.music': 'Tianyu Music',
    'mock.artist.yuan.yuxi': 'Yuan Yuxi',
    'mock.artist.yang.yuming': 'Yang Yuming',
    'mock.artist.xiaoyu.music': 'Xiaoyu Music',
    'mock.artist.electronic.band': 'Electronic Band',
    'mock.artist.zhang.meiqi': 'Zhang Meiqi',
    'mock.artist.wang.zhiqiang': 'Wang Zhiqiang',
    
    // Mock Genres
    'mock.genre.pop': 'Pop',
    'mock.genre.electronic': 'Electronic',
    'mock.genre.folk': 'Folk',
    'mock.genre.light.music': 'Light Music',
    'mock.genre.hiphop': 'Hip-Hop',
    'mock.genre.ambient': 'Ambient',
    'mock.genre.healing': 'Healing',
    'mock.genre.original': 'Original',
    
    // Mock Studio Names
    'mock.studio.tianyu': 'Tianyu Music Studio',
    'mock.label.ibom': 'iBOM Music',
    
    // Mock Comment Content
    'mock.comment.1': 'The melody is so beautiful, on infinite loop, such a wonderful song! Looking forward to more works.',
    'mock.comment.2': 'This song is absolutely amazing! The music production quality is very high, every detail is handled perfectly. I\'ve listened to it many times and never get tired of it, highly recommend to everyone!',
    
    // Mock User Names
    'mock.user.me': 'Me',
    
    // NFT Related
    'nft.digital.collectible': 'NFT Digital Collectible',
    'nft.limited.edition': 'Limited Edition',
    'nft.id': 'NFT ID',
    'nft.token.id': 'Token ID',
    'nft.price': 'Release Price',
    'nft.release.time': 'Release Time',
    'nft.buy.now': 'Buy NFT Now',
    'nft.buy.alert': 'NFT purchase feature is under development...',
    'nft.buy.work': 'Work',
    'nft.buy.price': 'Price',
    'nft.buy.coming.soon': 'Coming soon!',
  },
};

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // 从localStorage读取保存的语言设置，默认为'zh'
  const [language, setLanguage] = useState<Language>(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    return savedLanguage || 'zh';
  });

  // 当语言改变时保存到localStorage
  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};