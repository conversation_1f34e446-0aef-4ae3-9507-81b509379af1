import React, { useState, useMemo } from 'react';
import { UserPlusIcon, MagnifyingGlassIcon, StarIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid, CheckCircleIcon } from '@heroicons/react/24/solid';
import { useLanguage } from '../../../contexts/LanguageContext';

interface InviteMembersStepProps {
  spaceData: any;
  setSpaceData: (data: any) => void;
}

interface RecommendedMember {
  id: number;
  name: string;
  avatar: string;
  level: number;
  skills: string[];
  rating: number;
  experience: string;
  portfolio: number;
  recentWorks: string[];
  expectedShare: number;
  isAvailable: boolean;
}

const InviteMembersStep: React.FC<InviteMembersStepProps> = ({ spaceData, setSpaceData }) => {
  const { language } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [showInviteModal, setShowInviteModal] = useState<RecommendedMember | null>(null);
  const [showBatchInviteModal, setShowBatchInviteModal] = useState(false);
  const [inviteMessage, setInviteMessage] = useState('');
  const [batchInviteMessage, setBatchInviteMessage] = useState('');
  const [invitedMemberIds, setInvitedMemberIds] = useState<number[]>([]);
  const [selectedMemberIds, setSelectedMemberIds] = useState<number[]>([]);

  const recommendedMembers: RecommendedMember[] = useMemo(() => [
    {
      id: 1,
      name: language === 'zh' ? '林雨薇' : 'Lin Yuwei',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?w=80&h=80&fit=crop&crop=face',
      level: 8,
      skills: [language === 'zh' ? '作词' : 'Lyrics', language === 'zh' ? '作曲' : 'Composition'],
      rating: 4.9,
      experience: language === 'zh' ? '5年音乐创作经验' : '5 years of music creation experience',
      portfolio: 23,
      recentWorks: [language === 'zh' ? '《春天的故事》' : 'Spring Story', language === 'zh' ? '《夜空中最亮的星》' : 'Brightest Star in the Night Sky'],
      expectedShare: 25,
      isAvailable: true
    },
    {
      id: 2,
      name: language === 'zh' ? '陈俊杰' : 'Chen Junjie',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face',
      level: 7,
      skills: [language === 'zh' ? '编曲' : 'Arrangement', language === 'zh' ? '混音' : 'Mixing'],
      rating: 4.8,
      experience: language === 'zh' ? '专业编曲师，流行音乐专家' : 'Professional arranger, pop music specialist',
      portfolio: 31,
      recentWorks: [language === 'zh' ? '《青春无悔》' : 'Youth Without Regrets', language === 'zh' ? '《梦想起航》' : 'Dreams Take Flight'],
      expectedShare: 20,
      isAvailable: true
    },
    {
      id: 3,
      name: language === 'zh' ? '张梦琪' : 'Zhang Mengqi',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face',
      level: 9,
      skills: [language === 'zh' ? '演唱' : 'Vocals', language === 'zh' ? '作词' : 'Lyrics'],
      rating: 4.9,
      experience: language === 'zh' ? '知名独立歌手，音域宽广' : 'Renowned independent singer with wide vocal range',
      portfolio: 18,
      recentWorks: [language === 'zh' ? '《远方的呼唤》' : 'Call from Afar', language === 'zh' ? '《心中的歌》' : 'Song in My Heart'],
      expectedShare: 30,
      isAvailable: false
    },
    {
      id: 4,
      name: language === 'zh' ? '李明轩' : 'Li Mingxuan',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face',
      level: 6,
      skills: [language === 'zh' ? '制作' : 'Production', language === 'zh' ? '混音' : 'Mixing'],
      rating: 4.6,
      experience: language === 'zh' ? '资深音乐制作人' : 'Senior music producer',
      portfolio: 42,
      recentWorks: [language === 'zh' ? '《电子迷城》' : 'Electronic Maze', language === 'zh' ? '《都市夜晚》' : 'Urban Night'],
      expectedShare: 18,
      isAvailable: true
    },
    {
      id: 5,
      name: language === 'zh' ? '王诗雨' : 'Wang Shiyu',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=80&h=80&fit=crop&crop=face',
      level: 7,
      skills: [language === 'zh' ? '作曲' : 'Composition', language === 'zh' ? '演唱' : 'Vocals'],
      rating: 4.7,
      experience: language === 'zh' ? '创作型歌手，擅长抒情歌曲' : 'Singer-songwriter, specializes in ballads',
      portfolio: 16,
      recentWorks: [language === 'zh' ? '《月光下的秘密》' : 'Secret Under Moonlight', language === 'zh' ? '《时光倒流》' : 'Time Rewind'],
      expectedShare: 28,
      isAvailable: true
    },
    {
      id: 6,
      name: language === 'zh' ? '赵子涵' : 'Zhao Zihan',
      avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=80&h=80&fit=crop&crop=face',
      level: 8,
      skills: [language === 'zh' ? '编曲' : 'Arrangement', language === 'zh' ? '制作' : 'Production'],
      rating: 4.8,
      experience: language === 'zh' ? '电子音乐制作专家' : 'Electronic music production expert',
      portfolio: 27,
      recentWorks: [language === 'zh' ? '《数字梦境》' : 'Digital Dreams', language === 'zh' ? '《未来之声》' : 'Voice of the Future'],
      expectedShare: 22,
      isAvailable: true
    }
  ], [language]);

  const searchCategories = useMemo(() => [
    { value: 'all', label: language === 'zh' ? '全部角色' : 'All Roles' },
    ...spaceData.selectedRoles.map((role: string) => ({ value: role, label: role }))
  ], [spaceData.selectedRoles, language]);

  const filteredMembers = recommendedMembers.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesRole = selectedRole === 'all' || member.skills.includes(selectedRole);
    return matchesSearch && matchesRole;
  });

  const handleInviteMember = (member: RecommendedMember) => {
    setShowInviteModal(member);
    
    // 生成角色分成信息
    const roleShareInfo = spaceData.selectedRoles.map((role: string) => {
      const share = spaceData.revenueShares[role] || 0;
      const count = spaceData.roleAssignments[role]?.count || 1;
      return `${role}: ${share}%${count > 1 ? ` (${count}${language === 'zh' ? '人，每人' : ' people, '}${(share/count).toFixed(1)}%${language === 'zh' ? '' : ' each'})` : ''}`;
    }).join('\n');

    setInviteMessage(
      language === 'zh'
        ? `您好！我正在创建音乐协创空间"${spaceData.spaceName}"，希望邀请您以${member.skills.join('、')}的角色参与项目。

【项目简介】
${spaceData.spaceDescription || '暂无详细描述，期待在项目中与您详细沟通。'}

【角色分成比例】
${roleShareInfo}

根据您的专业水平和技能匹配，建议您的期望分成比例为${member.expectedShare}%。具体分成比例我们可以进一步协商确定。

期待与您合作，共同创造优秀的音乐作品！`
        : `Hello! I'm creating a music collaboration space "${spaceData.spaceName}" and would like to invite you to participate as ${member.skills.join(', ')}.

【Project Description】
${spaceData.spaceDescription || 'No detailed description available yet, looking forward to discussing details with you in the project.'}

【Role Revenue Sharing】
${roleShareInfo}

Based on your professional level and skill match, we suggest your expected revenue share to be ${member.expectedShare}%. We can further negotiate the specific revenue sharing ratio.

Looking forward to collaborating with you to create excellent music works!`
    );
  };

  const handleMemberSelect = (memberId: number) => {
    setSelectedMemberIds(prev => 
      prev.includes(memberId) 
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    );
  };

  const handleSelectAll = () => {
    const availableMembers = filteredMembers.filter(m => m.isAvailable && !invitedMemberIds.includes(m.id));
    if (selectedMemberIds.length === availableMembers.length) {
      setSelectedMemberIds([]);
    } else {
      setSelectedMemberIds(availableMembers.map(m => m.id));
    }
  };

  const sendInvitation = () => {
    if (showInviteModal) {
      setInvitedMemberIds(prev => [...prev, showInviteModal.id]);
      setSpaceData((prev: any) => ({
        ...prev,
        invitedMembers: [...prev.invitedMembers, {
          id: showInviteModal.id,
          username: showInviteModal.name,
          skills: showInviteModal.skills,
          rating: showInviteModal.rating,
          expectedShare: showInviteModal.expectedShare,
          inviteMessage: inviteMessage,
          invitedAt: new Date().toISOString()
        }]
      }));
      setShowInviteModal(null);
      setInviteMessage('');
      alert(language === 'zh' ? '邀请已发送！' : 'Invitation sent!');
    }
  };

  const sendBatchInvitations = () => {
    const selectedMembers = recommendedMembers.filter(m => selectedMemberIds.includes(m.id));
    setInvitedMemberIds(prev => [...prev, ...selectedMemberIds]);
    setSpaceData((prev: any) => ({
      ...prev,
      invitedMembers: [...prev.invitedMembers, ...selectedMembers.map(member => ({
        id: member.id,
        username: member.name,
        skills: member.skills,
        rating: member.rating,
        expectedShare: member.expectedShare,
        inviteMessage: batchInviteMessage,
        invitedAt: new Date().toISOString()
      }))]
    }));
    setShowBatchInviteModal(false);
    setSelectedMemberIds([]);
    setBatchInviteMessage('');
    alert(language === 'zh' ? `已向${selectedMembers.length}位创作者发送邀请！` : `Invitations sent to ${selectedMembers.length} creators!`);
  };

  return (
    <div className="space-y-8">
      {/* Step Title */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {language === 'zh' ? '邀请协作成员' : 'Invite Collaboration Members'}
        </h2>
        <p className="text-gray-600">
          {language === 'zh' ? '浏览平台推荐的优质创作者，发送邀请开始协作' : 'Browse platform-recommended quality creators and send invitations to start collaborating'}
        </p>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={language === 'zh' ? '搜索创作者姓名或技能...' : 'Search creator name or skills...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <select
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
            className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[150px]"
          >
            {searchCategories.map((category) => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
        </div>

        {/* Results Summary and Batch Actions */}
        <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">
              {language === 'zh' 
                ? `找到 ${filteredMembers.length} 位匹配的创作者`
                : `Found ${filteredMembers.length} matching creators`
              }
            </span>
            {selectedMemberIds.length > 0 && (
              <span className="text-sm text-blue-600 font-medium">
                {language === 'zh' 
                  ? `已选择 ${selectedMemberIds.length} 位`
                  : `${selectedMemberIds.length} selected`
                }
              </span>
            )}
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handleSelectAll}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              {selectedMemberIds.length === filteredMembers.filter(m => m.isAvailable && !invitedMemberIds.includes(m.id)).length
                ? (language === 'zh' ? '取消全选' : 'Deselect All')
                : (language === 'zh' ? '全选' : 'Select All')
              }
            </button>
            {selectedMemberIds.length > 0 && (
              <button
                onClick={() => {
                  // 生成详细的角色分成信息
                  const roleShareInfo = spaceData.selectedRoles.map((role: string) => {
                    const share = spaceData.revenueShares[role] || 0;
                    const count = spaceData.roleAssignments[role]?.count || 1;
                    return `${role}: ${share}%${count > 1 ? ` (${language === 'zh' ? '需要' : 'Need '}${count}${language === 'zh' ? '人，每人约' : ' people, approximately '}${(share/count).toFixed(1)}%${language === 'zh' ? '' : ' each'})` : ''}`;
                  }).join('\n');

                  setBatchInviteMessage(
                    language === 'zh'
                      ? `您好！我正在创建音乐协创空间"${spaceData.spaceName}"，希望邀请您参与这个项目。

【项目简介】
${spaceData.spaceDescription || '我们正在筹备一个音乐协创项目，期待有才华的创作者加入我们的团队。'}

【所需角色及分成比例】
${roleShareInfo}

我们正在寻找${spaceData.selectedRoles.join('、')}等不同角色的优秀创作者。根据您的专业技能和经验，我们相信您会是这个项目的宝贵成员。

具体的角色分配和分成比例我们可以根据项目进展和个人贡献进行协商调整。

期待与您合作，共同创造出色的音乐作品！`
                      : `Hello! I'm creating a music collaboration space "${spaceData.spaceName}" and would like to invite you to participate in this project.

【Project Description】
${spaceData.spaceDescription || 'We are preparing a music collaboration project and looking forward to talented creators joining our team.'}

【Required Roles and Revenue Sharing】
${roleShareInfo}

We are looking for excellent creators in different roles such as ${spaceData.selectedRoles.join(', ')}. Based on your professional skills and experience, we believe you would be a valuable member of this project.

The specific role assignments and revenue sharing ratios can be negotiated and adjusted based on project progress and individual contributions.

Looking forward to collaborating with you to create outstanding music works!`
                  );
                  setShowBatchInviteModal(true);
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                {language === 'zh' ? `批量邀请 (${selectedMemberIds.length})` : `Batch Invite (${selectedMemberIds.length})`}
              </button>
            )}
            <span className="text-sm text-gray-600">
              {language === 'zh' 
                ? `已邀请 ${invitedMemberIds.length} 位`
                : `${invitedMemberIds.length} invited`
              }
            </span>
          </div>
        </div>

        {/* Recommended Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <div 
              key={member.id} 
              className={`border-2 rounded-lg p-6 hover:shadow-md transition-all flex flex-col ${
                selectedMemberIds.includes(member.id) 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              {/* Selection Checkbox */}
              {member.isAvailable && !invitedMemberIds.includes(member.id) && (
                <div className="flex justify-end mb-2">
                  <input
                    type="checkbox"
                    checked={selectedMemberIds.includes(member.id)}
                    onChange={() => handleMemberSelect(member.id)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  />
                </div>
              )}

              {/* Member Header */}
              <div className="flex items-start gap-4 mb-4">
                <img
                  src={member.avatar}
                  alt={member.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-gray-900">{member.name}</h3>
                    {!member.isAvailable && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                        {language === 'zh' ? '忙碌' : 'Busy'}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-1 mb-2">
                    <StarIconSolid className="w-4 h-4 text-yellow-400" />
                    <span className="text-sm text-gray-600">{member.rating}</span>
                    <span className="text-xs text-gray-500 ml-1">Lv.{member.level}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {member.skills.map((skill, index) => (
                      <span 
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Member Details */}
              <div className="space-y-3 mb-4">
                <div>
                  <p className="text-sm text-gray-600">{member.experience}</p>
                </div>
                <div className="text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>{language === 'zh' ? '作品数量:' : 'Portfolio:'}</span>
                    <span className="font-medium">{member.portfolio}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{language === 'zh' ? '期望分成:' : 'Expected Share:'}</span>
                    <span className="font-medium">{member.expectedShare}%</span>
                  </div>
                </div>
                {member.recentWorks.length > 0 && (
                  <div>
                    <p className="text-xs text-gray-500 mb-1">
                      {language === 'zh' ? '近期作品:' : 'Recent Works:'}
                    </p>
                    <div className="space-y-1">
                      {member.recentWorks.slice(0, 2).map((work, index) => (
                        <p key={index} className="text-xs text-gray-600 truncate">• {work}</p>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="space-y-2 mt-auto">
                {invitedMemberIds.includes(member.id) ? (
                  <div className="flex items-center justify-center py-2 px-4 bg-green-100 text-green-800 rounded-lg">
                    <CheckCircleIcon className="w-4 h-4 mr-2" />
                    <span className="text-sm font-medium">
                      {language === 'zh' ? '已邀请' : 'Invited'}
                    </span>
                  </div>
                ) : (
                  <button
                    onClick={() => handleInviteMember(member)}
                    disabled={!member.isAvailable}
                    className={`w-full flex items-center justify-center gap-2 py-2 px-4 rounded-lg transition-colors ${
                      member.isAvailable
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    <UserPlusIcon className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      {language === 'zh' ? '邀请加入' : 'Invite'}
                    </span>
                  </button>
                )}
                <button className="w-full py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  <span className="text-sm">
                    {language === 'zh' ? '查看详情' : 'View Details'}
                  </span>
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredMembers.length === 0 && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MagnifyingGlassIcon className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {language === 'zh' ? '没有找到匹配的创作者' : 'No matching creators found'}
            </h3>
            <p className="text-gray-500">
              {language === 'zh' ? '尝试调整搜索条件或角色筛选' : 'Try adjusting search terms or role filters'}
            </p>
          </div>
        )}
      </div>

      {/* Invitation Summary */}
      {invitedMemberIds.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-green-800 mb-2">
            {language === 'zh' ? '邀请发送成功' : 'Invitations Sent Successfully'}
          </h3>
          <p className="text-green-700 text-sm">
            {language === 'zh' 
              ? `您已向 ${invitedMemberIds.length} 位创作者发送了邀请。他们将收到邀请通知，您可以在协创空间中查看回复状态。`
              : `You have sent invitations to ${invitedMemberIds.length} creators. They will receive invitation notifications, and you can check reply status in the collaboration space.`
            }
          </p>
        </div>
      )}

      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-lg w-full mx-4">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  {language === 'zh' ? '邀请创作者' : 'Invite Creator'}
                </h3>
                <button
                  onClick={() => setShowInviteModal(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              {/* Member Preview */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="flex items-center gap-3 mb-3">
                  <img
                    src={showInviteModal.avatar}
                    alt={showInviteModal.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div>
                    <h4 className="font-semibold text-gray-900">{showInviteModal.name}</h4>
                    <p className="text-sm text-gray-600">{showInviteModal.skills.join('、')}</p>
                  </div>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div className="flex justify-between">
                    <span>{language === 'zh' ? '期望分成:' : 'Expected Share:'}</span>
                    <span>{showInviteModal.expectedShare}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{language === 'zh' ? '平台评分:' : 'Platform Rating:'}</span>
                    <span>{showInviteModal.rating}/5.0</span>
                  </div>
                </div>
              </div>

              {/* Project Summary */}
              <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2 text-left">
                  {language === 'zh' ? '项目概览' : 'Project Overview'}
                </h4>
                <div className="text-sm text-blue-800 text-left">
                  {spaceData.spaceDescription ? (
                    <p>{spaceData.spaceDescription}</p>
                  ) : (
                    <p className="text-gray-500 italic">
                      {language === 'zh' ? '暂无项目描述' : 'No project description available'}
                    </p>
                  )}
                </div>
              </div>

              {/* Invitation Message */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    {language === 'zh' ? '邀请消息' : 'Invitation Message'}
                    <span className="text-gray-500 font-normal ml-2">
                      ({language === 'zh' ? '已包含项目详情和分成信息' : 'Includes project details and revenue sharing info'})
                    </span>
                  </label>
                  <button
                    onClick={() => handleInviteMember(showInviteModal!)}
                    className="text-xs text-blue-600 hover:text-blue-800 underline"
                  >
                    {language === 'zh' ? '重新生成' : 'Regenerate'}
                  </button>
                </div>
                <textarea
                  value={inviteMessage}
                  onChange={(e) => setInviteMessage(e.target.value)}
                  placeholder={language === 'zh' ? '邀请消息已自动生成，您可以进一步编辑和个性化...' : 'Invitation message has been auto-generated, you can further edit and personalize...'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={8}
                  maxLength={1000}
                />
                <div className="flex justify-between items-center mt-2">
                  <p className="text-xs text-gray-500">
                    {language === 'zh' ? '💡 提示：消息包含项目简介、角色分成等详细信息' : '💡 Tip: Message includes project description, role revenue sharing and other details'}
                  </p>
                  <span className="text-sm text-gray-400">
                    {inviteMessage.length}/1000
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setShowInviteModal(null)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {language === 'zh' ? '取消' : 'Cancel'}
                </button>
                <button
                  onClick={sendInvitation}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {language === 'zh' ? '发送邀请' : 'Send Invitation'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Batch Invite Modal */}
      {showBatchInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  {language === 'zh' ? '批量邀请创作者' : 'Batch Invite Creators'}
                </h3>
                <button
                  onClick={() => setShowBatchInviteModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              {/* Project Overview */}
              <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2 text-left">
                  {language === 'zh' ? '项目概览' : 'Project Overview'}
                </h4>
                <div className="text-sm text-blue-800 text-left">
                  {spaceData.spaceDescription ? (
                    <p>{spaceData.spaceDescription}</p>
                  ) : (
                    <p className="text-gray-500 italic">
                      {language === 'zh' ? '暂无项目描述' : 'No project description available'}
                    </p>
                  )}
                </div>
              </div>

              {/* Selected Members Preview */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 mb-3">
                  {language === 'zh' ? `邀请对象 (${selectedMemberIds.length}人)` : `Invitation Targets (${selectedMemberIds.length} people)`}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-40 overflow-y-auto">
                  {recommendedMembers
                    .filter(member => selectedMemberIds.includes(member.id))
                    .map(member => (
                      <div key={member.id} className="flex items-center gap-3 p-3 bg-white rounded border">
                        <img
                          src={member.avatar}
                          alt={member.name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 text-sm truncate">{member.name}</p>
                          <p className="text-gray-600 text-xs truncate">
                            {member.skills.join('、')} - {member.expectedShare}%
                          </p>
                        </div>
                        <div className="flex items-center">
                          <StarIconSolid className="w-3 h-3 text-yellow-400 mr-1" />
                          <span className="text-xs text-gray-600">{member.rating}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Batch Invitation Message */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'zh' ? '批量邀请消息' : 'Batch Invitation Message'}
                  <span className="text-gray-500 font-normal ml-2">
                    ({language === 'zh' ? '已包含完整项目信息' : 'Includes complete project information'})
                  </span>
                </label>
                <textarea
                  value={batchInviteMessage}
                  onChange={(e) => setBatchInviteMessage(e.target.value)}
                  placeholder={language === 'zh' ? '批量邀请消息已自动生成，包含项目简介和分成信息...' : 'Batch invitation message has been auto-generated, including project description and revenue sharing info...'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={8}
                  maxLength={1000}
                />
                <div className="flex justify-between items-center mt-2">
                  <p className="text-xs text-gray-500">
                    {language === 'zh' ? '💡 提示：统一消息将发送给所有选中的创作者，包含项目详情和角色分成信息' : '💡 Tip: Unified message will be sent to all selected creators, including project details and role revenue sharing info'}
                  </p>
                  <span className="text-sm text-gray-400">
                    {batchInviteMessage.length}/1000
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setShowBatchInviteModal(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {language === 'zh' ? '取消' : 'Cancel'}
                </button>
                <button
                  onClick={sendBatchInvitations}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  {language === 'zh' ? `发送邀请 (${selectedMemberIds.length}人)` : `Send Invitations (${selectedMemberIds.length} people)`}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InviteMembersStep;