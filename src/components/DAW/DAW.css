.daw-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

.daw-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
  min-height: 60px;
}

.daw-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
  color: #00d4ff;
}

.daw-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.daw-sidebar {
  width: 250px;
  background: #252525;
  border-right: 1px solid #3a3a3a;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.daw-workspace {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

.daw-sequencer-section {
  flex: 1;
  min-height: 200px;
  background: #1e1e1e;
  border-bottom: 1px solid #3a3a3a;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.daw-pianoroll-section {
  height: 250px;
  background: #1e1e1e;
  border-bottom: 1px solid #3a3a3a;
  overflow: auto;
}

.daw-mixer-section {
  width: 480px;
  background: #252525;
  border-left: 1px solid #3a3a3a;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Transport controls */
.transport-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.transport-button {
  background: #404040;
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.transport-button:hover {
  background: #505050;
}

.transport-button.active {
  background: #00d4ff;
}

.tempo-input {
  background: #404040;
  border: 1px solid #505050;
  color: white;
  padding: 5px 8px;
  border-radius: 4px;
  width: 80px;
}

/* Track list */
.track-list {
  padding: 10px;
}

.track-item {
  display: flex;
  flex-direction: column;
  padding: 12px;
  margin-bottom: 8px;
  background: #303030;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  border: 1px solid #3a3a3a;
}

.track-item:hover {
  background: #404040;
  border-color: #505050;
}

.track-item.selected {
  background: #2a3d4a;
  color: #ffffff;
  border-color: #3a5d7a;
  box-shadow: 0 0 8px rgba(58, 93, 122, 0.3);
}

.track-item > div {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.track-item > div:last-child {
  margin-bottom: 0;
}

.track-controls {
  display: flex;
  gap: 5px;
  margin-left: auto;
}

.track-faders {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.fader-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fader-label {
  font-size: 10px;
  color: #aaa;
  min-width: 40px;
  text-align: left;
}

.horizontal-slider {
  flex: 1;
  height: 4px;
  background: #404040;
  border-radius: 2px;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

.horizontal-slider::-webkit-slider-thumb {
  appearance: none;
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #00d4ff;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 4px rgba(0, 212, 255, 0.5);
}

.horizontal-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #00d4ff;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 4px rgba(0, 212, 255, 0.5);
}

.volume-slider {
  background: linear-gradient(to right, #404040 0%, #00d4ff 100%);
}

.pan-slider {
  background: linear-gradient(to right, #ff4444 0%, #404040 50%, #44ff44 100%);
}

/* Automation Editor */
.daw-automation-section {
  height: 200px;
  background: #1e1e1e;
  border-top: 1px solid #3a3a3a;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.automation-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1e1e1e;
  border-radius: 6px;
  overflow: hidden;
}

.automation-editor-placeholder {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1e1e1e;
  border-radius: 6px;
  overflow: hidden;
}

.automation-header {
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
}

.automation-content {
  flex: 1;
  background: #1a1a1a;
  overflow: auto;
}

.automation-svg {
  display: block;
  background: #1a1a1a;
  cursor: crosshair;
}

.automation-curve {
  transition: stroke-width 0.2s ease;
}

.automation-curve:hover {
  stroke-width: 3;
}

.automation-point {
  transition: all 0.2s ease;
}

.automation-point:hover {
  r: 6;
  stroke-width: 2;
  filter: drop-shadow(0 0 4px currentColor);
}

.playhead {
  pointer-events: none;
  filter: drop-shadow(0 0 2px #ff4444);
}

.fader-value {
  font-size: 10px;
  color: #ccc;
  min-width: 30px;
  text-align: center;
  font-weight: bold;
}

.track-control-btn {
  background: none;
  border: 1px solid #505050;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
}

.track-control-btn.active {
  background: #ff4444;
}

.add-track-section {
  padding: 10px;
  border-top: 1px solid #3a3a3a;
}

.add-track-btn {
  width: 100%;
  background: #404040;
  border: 1px solid #505050;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 5px;
  font-size: 12px;
  font-weight: 500;
  text-align: left;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-track-btn:hover {
  background: #00d4ff;
  border-color: #00b8e6;
  color: #000000;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 212, 255, 0.3);
}

.add-track-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 212, 255, 0.2);
}

.instrument-category {
  margin-bottom: 8px;
}

.instrument-btn {
  background: #3a4a5a !important;
  color: #ffffff !important;
  font-size: 11px !important;
  padding: 6px 8px !important;
  text-align: left !important;
  border: 1px solid #4a5a6a;
  transition: all 0.2s ease;
  cursor: pointer !important;
  pointer-events: auto !important;
}

.instrument-btn:hover {
  background: #00d4ff !important;
  border-color: #00b8e6;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 212, 255, 0.3);
  color: #000000 !important;
}

.audio-track-btn {
  background: #2a4a3a !important;
  border-color: #3a5a4a !important;
}

.audio-track-btn:hover {
  background: #10b981 !important;
  border-color: #059669 !important;
  color: #ffffff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

/* Sequencer */
.sequencer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 10px;
  overflow: hidden;
}

/* Workspace Divider */
.workspace-divider {
  transition: all 0.2s ease;
}

.workspace-divider:hover {
  background: linear-gradient(90deg, #4a4a4a 0%, #5a5a5a 50%, #4a4a4a 100%) !important;
  border-color: #6a6a6a !important;
}

.sequencer-content {
  position: relative;
  flex: 1;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.sequencer-timeline {
  height: 40px;
  background: #2a2a2a;
  border: 1px solid #3a3a3a;
  margin-bottom: 10px;
  position: relative;
  overflow-x: auto;
  border-radius: 4px;
}

.sequencer-tracks {
  flex: 1;
  overflow: auto;
  position: relative;
  border-radius: 4px;
  background: #1a1a1a;
  border: 1px solid #3a3a3a;
  min-height: 320px;
  max-height: 720px;
}

.sequencer-tracks::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.sequencer-tracks::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 6px;
}

.sequencer-tracks::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 6px;
  border: 2px solid #1a1a1a;
}

.sequencer-tracks::-webkit-scrollbar-thumb:hover {
  background: #505050;
}

.sequencer-tracks::-webkit-scrollbar-corner {
  background: #1a1a1a;
}

.sequencer-track {
  height: 54px;
  background: #252525;
  border-bottom: 1px solid #3a3a3a;
  position: relative;
  overflow: hidden;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.sequencer-track:hover {
  background: #303030;
}

.sequencer-track.border-blue-400 {
  border-color: #3a5d7a;
  background: #2a3d4a;
  box-shadow: 0 0 8px rgba(58, 93, 122, 0.2);
}

.sequencer-clip {
  position: absolute;
  top: 5px;
  bottom: 5px;
  background: linear-gradient(135deg, #00d4ff, #0066cc);
  border-radius: 3px;
  border: 1px solid #005599;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Piano roll */
.pianoroll-container {
  display: flex;
  height: 100%;
}

.pianoroll-keys {
  width: 80px;
  background: #2a2a2a;
  border-right: 1px solid #3a3a3a;
  overflow: hidden;
}

.pianoroll-grid {
  flex: 1;
  background: #1a1a1a;
  position: relative;
  overflow: auto;
}

.piano-key {
  height: 20px;
  border-bottom: 1px solid #3a3a3a;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  cursor: pointer;
}

.piano-key.white {
  background: #f0f0f0;
  color: #000;
}

.piano-key.black {
  background: #2a2a2a;
  color: #fff;
}

.piano-key:hover {
  background: #00d4ff;
}

/* Mixer */
.mixer-container {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mixer-channel {
  background: #2a2a2a;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  padding: 10px;
}

.mixer-channel-name {
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.mixer-fader {
  height: 100px;
  width: 20px;
  margin: 10px auto;
  background: #404040;
  border-radius: 10px;
  position: relative;
  cursor: pointer;
}

.mixer-fader-handle {
  width: 30px;
  height: 15px;
  background: #00d4ff;
  border-radius: 7px;
  position: absolute;
  left: -5px;
  cursor: pointer;
}

.mixer-controls {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.mixer-control {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
}

.mixer-knob {
  width: 30px;
  height: 30px;
  background: #404040;
  border: 2px solid #606060;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
}

/* Effects panel */
.effects-panel {
  padding: 10px;
  border-top: 1px solid #3a3a3a;
}

.effects-panel h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #00d4ff;
}

.effect-slot {
  background: #2a2a2a;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.effect-name {
  font-size: 12px;
}

.effect-remove {
  background: #ff4444;
  border: none;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #505050;
}

/* New Layout Styles */
.daw-main-new {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.daw-track-section {
  height: 80px;
  background: #252525;
  border-bottom: 1px solid #3a3a3a;
  overflow-x: auto;
  overflow-y: hidden;
}

.daw-workspace-new {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.daw-effects-section {
  width: 250px;
  background: #252525;
  border-left: 1px solid #3a3a3a;
  overflow-y: auto;
}

.daw-mixer-bottom {
  height: 400px;
  background: #1a1a1a;
  border-top: 1px solid #3a3a3a;
  overflow-x: auto;
  overflow-y: hidden;
}

/* Horizontal Track List */
.track-list-horizontal {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.track-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.add-track-buttons {
  display: flex;
  gap: 5px;
}

.add-track-btn-compact {
  background: #404040;
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-track-btn-compact:hover {
  background: #505050;
}

.track-list-content {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  flex: 1;
}

.track-item-horizontal {
  min-width: 120px;
  background: #303030;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 50px;
}

.track-item-horizontal:hover {
  background: #404040;
}

.track-item-horizontal.selected {
  background: #00d4ff;
  color: #000;
}

.track-info-horizontal {
  flex: 1;
}

.track-controls-horizontal {
  display: flex;
  gap: 2px;
  margin-top: 5px;
}

.track-control-btn-small {
  background: none;
  border: 1px solid #505050;
  color: white;
  padding: 1px 4px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 9px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.track-control-btn-small.active {
  background: #ff4444;
}

.track-item-horizontal.selected .track-control-btn-small {
  color: #000;
  border-color: #333;
}

/* Pro Tools Style Mixer */
.mixer-container-protools {
  padding: 10px;
  background: #1a1a1a;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.mixer-header {
  margin-bottom: 12px;
  border-bottom: 2px solid #3a3a3a;
  padding-bottom: 8px;
  background: linear-gradient(90deg, rgba(0,212,255,0.1) 0%, transparent 100%);
  border-radius: 4px;
  padding-left: 8px;
}

.mixer-channels-horizontal {
  display: flex;
  gap: 3px;
  min-height: 350px;
}

.mixer-channels-vertical {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: auto;
  padding: 5px 0;
}

.mixer-channel-protools {
  width: 100%;
  background: linear-gradient(180deg, #2a2a2a 0%, #1f1f1f 100%);
  border: 1px solid #3a3a3a;
  border-radius: 6px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  min-height: 320px;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.1), 0 2px 4px rgba(0,0,0,0.3);
  position: relative;
  transition: all 0.2s ease;
}

.mixer-channel-protools.master-channel {
  background: linear-gradient(180deg, #2d2d2d 0%, #222222 100%);
  border: 2px solid #4a4a4a;
  margin-top: 10px;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.15), 0 3px 6px rgba(0,0,0,0.4);
}

.mixer-channel-name {
  font-size: 11px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 12px;
  padding: 4px;
  background: rgba(0,0,0,0.3);
  border-radius: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mixer-fader-protools {
  height: 120px;
  width: 10px;
  margin: 8px auto;
  background: linear-gradient(180deg, #4a4a4a 0%, #2a2a2a 100%);
  border: 1px solid #1a1a1a;
  border-radius: 5px;
  position: relative;
  cursor: pointer;
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.6), 0 1px 2px rgba(255,255,255,0.1);
  transition: box-shadow 0.2s ease;
}

.mixer-fader-protools:hover {
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.6), 0 1px 2px rgba(0,212,255,0.3);
}

.mixer-fader-handle-protools {
  width: 14px;
  height: 10px;
  background: linear-gradient(180deg, #e0e0e0 0%, #a0a0a0 100%);
  border: 1px solid #808080;
  border-radius: 3px;
  position: absolute;
  left: -3px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.4);
  transition: all 0.2s ease;
}

.mixer-fader-handle-protools:hover {
  background: linear-gradient(180deg, #f0f0f0 0%, #b0b0b0 100%);
  box-shadow: 0 2px 6px rgba(0,0,0,0.5), 0 0 4px rgba(0,212,255,0.4);
}

.mixer-knob {
  background: radial-gradient(circle at 30% 30%, #4a4a4a, #2a2a2a);
  border: 1px solid #1a1a1a;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.1), 0 2px 4px rgba(0,0,0,0.4);
  flex-shrink: 0;
  transition: all 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.mixer-knob:hover {
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.15), 0 2px 6px rgba(0,0,0,0.5), 0 0 4px rgba(0,212,255,0.3);
  transform: translateY(-1px);
}

.mixer-knob:active {
  transform: translateY(0);
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 2px rgba(0,0,0,0.3);
}

.mixer-controls {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.mixer-controls button {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Insert slot styling */
.mixer-insert-slot {
  transition: all 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.mixer-insert-slot:active {
  transform: scale(0.98);
}

.mixer-insert-slot.clicked {
  background-color: #10b981 !important;
  color: white !important;
  border-color: #059669 !important;
  transform: scale(0.98);
}

/* Horizontal Piano Roll Styles */
.pianoroll-keys-horizontal {
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 5px;
  position: relative;
}

.piano-key-horizontal {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #404040;
  cursor: pointer;
  transition: all 0.1s ease;
  position: relative;
  flex-shrink: 0;
  box-sizing: border-box;
}

.piano-key-horizontal.white {
  background: linear-gradient(180deg, #f8f8f8 0%, #e0e0e0 100%);
  color: #000;
  border-color: #ccc;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.piano-key-horizontal.black {
  background: linear-gradient(180deg, #3a3a3a 0%, #1a1a1a 100%);
  color: #fff;
  border-color: #2a2a2a;
  height: 36px !important;
  margin-top: 0;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  z-index: 2;
}

.piano-key-horizontal:hover {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.piano-key-horizontal.white:hover {
  background: linear-gradient(180deg, #fff 0%, #f0f0f0 100%);
}

.piano-key-horizontal.black:hover {
  background: linear-gradient(180deg, #4a4a4a 0%, #2a2a2a 100%);
}

.piano-key-horizontal:active {
  transform: translateY(2px);
  box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}

.pianoroll-grid-horizontal {
  background: #1a1a1a;
  position: relative;
  border-top: 1px solid #3a3a3a;
}

/* Piano Roll Container */
.pianoroll-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1e1e1e;
  border-radius: 4px;
  overflow: hidden;
}

/* Waveform Editor */
.waveform-editor {
  background: #1e1e1e;
  border-radius: 6px;
  overflow: hidden;
}

.waveform-header {
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
}

.waveform-content {
  background: #1a1a1a;
}

.waveform-display {
  background: #1a1a1a;
  min-height: 120px;
  border-radius: 4px;
  margin: 4px;
}

.waveform-controls {
  background: #333;
  border-top: 1px solid #3a3a3a;
}

/* Piano Roll Enhancements */
.pianoroll-keys-horizontal {
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
  overflow-x: auto;
  border-radius: 4px 4px 0 0;
}

.pianoroll-grid-horizontal {
  background: #1a1a1a;
  border-radius: 0 0 4px 4px;
}

/* React Piano Overrides */
.ReactPiano__Key {
  transition: all 0.1s ease;
}

.ReactPiano__Key--natural {
  background: #f8f9fa !important;
  border: 1px solid #dee2e6 !important;
}

.ReactPiano__Key--natural:hover {
  background: #e9ecef !important;
}

.ReactPiano__Key--natural.ReactPiano__Key--active {
  background: #007bff !important;
  color: white !important;
}

.ReactPiano__Key--accidental {
  background: #343a40 !important;
  border: 1px solid #495057 !important;
}

.ReactPiano__Key--accidental:hover {
  background: #495057 !important;
}

.ReactPiano__Key--accidental.ReactPiano__Key--active {
  background: #0056b3 !important;
  color: white !important;
}

/* Horizontal Mixer Layout */
.mixer-horizontal-layout {
  width: 100%;
  height: 100%;
  background: #1a1a1a;
  border-radius: 6px;
  overflow: hidden;
}

.mixer-container-horizontal {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.mixer-header-horizontal {
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
}

.mixer-channels-horizontal {
  flex: 1;
  background: #1e1e1e;
  overflow-x: auto;
  overflow-y: hidden;
}

.mixer-channel-horizontal {
  display: flex;
  flex-direction: column;
  min-width: 100px;
  background: #252525;
  border-radius: 6px;
  border: 1px solid #3a3a3a;
  padding: 8px;
  transition: all 0.2s ease;
}

.mixer-channel-horizontal:hover {
  background: #2a2a2a;
  border-color: #505050;
}

.mixer-channel-horizontal.selected {
  background: #2a3d4a;
  border-color: #3a5d7a;
  box-shadow: 0 0 8px rgba(58, 93, 122, 0.3);
}

.master-channel-horizontal {
  background: #2a3040 !important;
  border-color: #3a4a5a !important;
}

.master-channel-horizontal:hover {
  background: #2f3545 !important;
  border-color: #4a5a6a !important;
}

/* Horizontal Slider Enhancements */
.horizontal-slider {
  height: 6px;
  background: #404040;
  border-radius: 3px;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.horizontal-slider:hover {
  background: #505050;
}

.horizontal-slider::-webkit-slider-thumb {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00d4ff;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 6px rgba(0, 212, 255, 0.5);
  transition: all 0.2s ease;
}

.horizontal-slider::-webkit-slider-thumb:hover {
  background: #00bfed;
  box-shadow: 0 0 8px rgba(0, 212, 255, 0.7);
  transform: scale(1.1);
}

.horizontal-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00d4ff;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 6px rgba(0, 212, 255, 0.5);
  transition: all 0.2s ease;
}

.horizontal-slider::-moz-range-thumb:hover {
  background: #00bfed;
  box-shadow: 0 0 8px rgba(0, 212, 255, 0.7);
  transform: scale(1.1);
}

.volume-slider {
  background: linear-gradient(to right, #404040 0%, #00d4ff 100%);
}

.pan-slider {
  background: linear-gradient(to right, #ff4444 0%, #404040 50%, #44ff44 100%);
}

/* Automation Editor */
.daw-automation-section {
  height: 200px;
  background: #1e1e1e;
  border-top: 1px solid #3a3a3a;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.automation-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1e1e1e;
  border-radius: 6px;
  overflow: hidden;
}

.automation-editor-placeholder {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1e1e1e;
  border-radius: 6px;
  overflow: hidden;
}

.automation-header {
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
}

.automation-content {
  flex: 1;
  background: #1a1a1a;
  overflow: auto;
}

.automation-svg {
  display: block;
  background: #1a1a1a;
  cursor: crosshair;
}

.automation-curve {
  transition: stroke-width 0.2s ease;
}

.automation-curve:hover {
  stroke-width: 3;
}

.automation-point {
  transition: all 0.2s ease;
}

.automation-point:hover {
  r: 6;
  stroke-width: 2;
  filter: drop-shadow(0 0 4px currentColor);
}

.playhead {
  pointer-events: none;
  filter: drop-shadow(0 0 2px #ff4444);
}
/* Recording Manager */
.recording-manager {
  background: #252525;
  border-radius: 6px;
  margin-bottom: 10px;
  border: 1px solid #3a3a3a;
  overflow: hidden;
}

.recording-header {
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
}

.recording-controls {
  background: #2a2a2a;
}

.recording-manager select {
  background: #404040;
  border: 1px solid #505050;
  color: white;
  border-radius: 4px;
}

.recording-manager select:focus {
  outline: none;
  border-color: #00d4ff;
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

.recording-manager button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.recording-level-meter {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 2px;
  overflow: hidden;
}

@keyframes recording-pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Enhanced Automation Editor Styles */
.automation-lane-divider {
  cursor: ns-resize;
  transition: all 0.2s ease;
}

.automation-lane-divider:hover {
  fill: #00d4ff !important;
  filter: drop-shadow(0 0 4px rgba(0, 212, 255, 0.6));
}

.automation-editor .automation-content {
  position: relative;
}

.automation-editor .automation-svg {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
