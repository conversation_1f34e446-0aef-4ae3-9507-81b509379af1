import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { UserProvider, useUser } from './contexts/UserContext';
import { WorksProvider } from './contexts/WorksContext';
import { LanguageProvider } from './contexts/LanguageContext';
import LandingPage from './components/LandingPage';
import Navigation from './components/Navigation';
import Homepage from './components/Homepage';
import Musicians from './components/Musicians';
import MusicianDetail from './components/MusicianDetail';
import WorkDetail from './components/WorkDetail';
import CollaborationLayout from './components/collaboration/CollaborationLayout';
import CopyrightContract from './components/collaboration/CopyrightContract';
import ContractDetail from './components/collaboration/ContractDetail';
import Crowdfunding from './components/Crowdfunding';
import LiveStream from './components/LiveStream';
import LiveRoomDetail from './components/LiveRoomDetail';
import StreamerApplication from './components/StreamerApplication';
import StreamerDashboard from './components/StreamerDashboard';
import DAW from './components/DAW';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import SocialFeed from './components/social/SocialFeed';
import WorksPage from './components/works/WorksPage';
import ProfilePage from './components/user/ProfilePage';
import AccountLayout from './components/account/AccountLayout';
import KaraokePage from './components/KaraokePage';
import AI from './pages/AI';
import './App.css';

// App component that conditionally renders based on authentication state
const AppContent: React.FC = () => {
  const { isAuthenticated } = useUser();

  // Show landing page for non-authenticated users
  if (!isAuthenticated) {
    return <LandingPage />;
  }

  // Show main app for authenticated users
  return (
    <div className="App">
      <Navigation />
      <Routes>
        <Route path="/" element={<Homepage />} />
        <Route path="/musicians" element={<Musicians />} />
        <Route path="/musicians/:id" element={<MusicianDetail />} />
        <Route path="/works/:id" element={<WorkDetail />} />
        <Route path="/collaboration/*" element={<CollaborationLayout />} />
        <Route path="/collaboration/copyright-contract" element={<CopyrightContract />} />
        <Route path="/collaboration/contract-detail" element={<ContractDetail />} />
        <Route path="/crowdfunding/*" element={<Crowdfunding />} />
        <Route path="/live" element={<LiveStream />} />
        <Route path="/live/:roomId" element={<LiveRoomDetail />} />
        <Route path="/streamer" element={<StreamerDashboard />} />
        <Route path="/streamer/apply" element={<StreamerApplication />} />
        <Route path="/music-studio" element={<DAW />} />
        <Route path="/investment" element={<div className="p-8 text-center">投资页面开发中...</div>} />
        <Route path="/publishing" element={<div className="p-8 text-center">发行页面开发中...</div>} />
        <Route path="/works" element={<WorksPage />} />
        <Route path="/karaoke/:id" element={<KaraokePage />} />
        <Route path="/social" element={<SocialFeed />} />
        <Route path="/videos" element={<div className="p-8 text-center">视频页面开发中...</div>} />
        <Route path="/ai" element={<AI />} />
        <Route path="/chat" element={<SocialFeed />} />
        <Route path="/profile" element={<ProfilePage />} />
        <Route path="/profile/:userId" element={<ProfilePage />} />
        <Route path="/account/*" element={<AccountLayout />} />
        <Route path="/settings" element={<div className="p-8 text-center">设置页面开发中...</div>} />
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
      </Routes>
    </div>
  );
};

function App() {
  return (
    <LanguageProvider>
      <UserProvider>
        <WorksProvider>
          <Router>
            <AppContent />
          </Router>
        </WorksProvider>
      </UserProvider>
    </LanguageProvider>
  );
}

export default App;
