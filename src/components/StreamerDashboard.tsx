import React, { useState, useEffect } from 'react';
import { useUser } from '../contexts/UserContext';
import { 
  PlayIcon, 
  StopIcon, 
  CogIcon, 
  PlusIcon,
  VideoCameraIcon,
  EyeIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline';
import StreamingWidget from './StreamingWidget';

interface LiveRoom {
  id: string;
  title: string;
  description: string;
  isLive: boolean;
  streamKey: string;
  rtmpUrl: string;
  webrtcUrl: string;
  viewerCount: number;
  createdAt: string;
}

const StreamerDashboard: React.FC = () => {
  const { hasPermission } = useUser();
  const [rooms, setRooms] = useState<LiveRoom[]>([]);
  const [activeRoom, setActiveRoom] = useState<LiveRoom | null>(null);
  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [newRoom, setNewRoom] = useState({
    title: '',
    description: ''
  });

  useEffect(() => {
    if (hasPermission('canStream')) {
      fetchRooms();
    }
  }, [hasPermission]);

  const fetchRooms = async () => {
    try {
      const { default: ApiService } = await import('../services/api');
      const data = await ApiService.getMyLiveRooms();
      setRooms(data);
    } catch (error) {
      console.error('Failed to fetch rooms:', error);
      // 使用mock数据作为后备
      const mockRooms = [
        {
          id: 'room_1',
          title: '音乐创作分享',
          description: '分享我的音乐创作过程',
          isLive: false,
          streamKey: 'stream_key_123',
          rtmpUrl: 'rtmp://live.example.com/live',
          webrtcUrl: 'wss://webrtc.example.com/room1',
          viewerCount: 0,
          createdAt: new Date().toISOString()
        }
      ];
      setRooms(mockRooms);
    }
  };

  const createRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newRoom.title.trim()) {
      alert('请输入直播间标题');
      return;
    }
    
    try {
      const { default: ApiService } = await import('../services/api');
      const room = await ApiService.createLiveRoom(newRoom);
      setRooms([...rooms, room]);
      setNewRoom({ title: '', description: '' });
      setShowCreateRoom(false);
      alert('直播间创建成功！');
    } catch (error) {
      console.error('Failed to create room:', error);
      
      // 创建mock直播间作为后备
      const mockRoom = {
        id: `room_${Date.now()}`,
        title: newRoom.title,
        description: newRoom.description,
        isLive: false,
        streamKey: `key_${Math.random().toString(36).substr(2, 9)}`,
        rtmpUrl: 'rtmp://live.example.com/live',
        webrtcUrl: `wss://webrtc.example.com/room${Date.now()}`,
        viewerCount: 0,
        createdAt: new Date().toISOString()
      };
      
      setRooms([...rooms, mockRoom]);
      setNewRoom({ title: '', description: '' });
      setShowCreateRoom(false);
      alert('直播间创建成功！(使用本地模拟数据)');
    }
  };

  const startStream = async (roomId: string) => {
    try {
      const { default: ApiService } = await import('../services/api');
      await ApiService.startStream(roomId);
      fetchRooms();
    } catch (error) {
      console.error('Failed to start stream:', error);
    }
  };

  const stopStream = async (roomId: string) => {
    try {
      const { default: ApiService } = await import('../services/api');
      await ApiService.stopStream(roomId);
      fetchRooms();
    } catch (error) {
      console.error('Failed to stop stream:', error);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('已复制到剪贴板');
  };

  if (!hasPermission('canStream')) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <VideoCameraIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h2 className="mt-2 text-lg font-medium text-gray-900">需要主播权限</h2>
          <p className="mt-1 text-sm text-gray-500">您需要先申请成为主播才能使用此功能</p>
          <div className="mt-6">
            <a
              href="/streamer/apply"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              申请成为主播
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900">主播控制台</h1>
            <button
              onClick={() => setShowCreateRoom(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              创建直播间
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 直播间列表 */}
            <div className="lg:col-span-2 space-y-4">
              {rooms.length === 0 ? (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6 text-center">
                    <VideoCameraIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">还没有直播间</h3>
                    <p className="mt-1 text-sm text-gray-500">创建您的第一个直播间开始直播</p>
                  </div>
                </div>
              ) : (
                rooms.map((room) => (
                  <div key={room.id} className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="px-4 py-5 sm:p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900">{room.title}</h3>
                          <p className="mt-1 text-sm text-gray-500">{room.description}</p>
                          <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <EyeIcon className="h-4 w-4 mr-1" />
                              {room.viewerCount} 观看
                            </div>
                            <div className={`flex items-center ${room.isLive ? 'text-green-600' : 'text-gray-400'}`}>
                              {room.isLive ? '● 直播中' : '● 未开播'}
                            </div>
                            <div>
                              创建时间：{new Date(room.createdAt).toLocaleDateString('zh-CN')}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => setActiveRoom(room)}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                          >
                            <CogIcon className="h-4 w-4 mr-1" />
                            设置
                          </button>
                          {room.isLive ? (
                            <button
                              onClick={() => stopStream(room.id)}
                              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                            >
                              <StopIcon className="h-4 w-4 mr-1" />
                              停止直播
                            </button>
                          ) : (
                            <button
                              onClick={() => startStream(room.id)}
                              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                            >
                              <PlayIcon className="h-4 w-4 mr-1" />
                              开始直播
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* 推流控制面板 */}
            <div className="space-y-4">
              <StreamingWidget
                rtmpUrl={activeRoom?.rtmpUrl}
                streamKey={activeRoom?.streamKey}
                webrtcUrl={activeRoom?.webrtcUrl}
                onStreamStart={() => {
                  if (activeRoom) {
                    startStream(activeRoom.id);
                  }
                }}
                onStreamStop={() => {
                  if (activeRoom) {
                    stopStream(activeRoom.id);
                  }
                }}
              />

              {activeRoom && (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">推流地址</h3>
                    
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">RTMP推流地址</label>
                        <div className="flex">
                          <input
                            type="text"
                            value={activeRoom.rtmpUrl}
                            readOnly
                            className="flex-1 border-gray-300 rounded-l-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-50"
                          />
                          <button
                            onClick={() => copyToClipboard(activeRoom.rtmpUrl)}
                            className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100"
                          >
                            <DocumentDuplicateIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">推流密钥</label>
                        <div className="flex">
                          <input
                            type="text"
                            value={activeRoom.streamKey}
                            readOnly
                            className="flex-1 border-gray-300 rounded-l-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-50"
                          />
                          <button
                            onClick={() => copyToClipboard(activeRoom.streamKey)}
                            className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100"
                          >
                            <DocumentDuplicateIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">WebRTC地址</label>
                        <div className="flex">
                          <input
                            type="text"
                            value={activeRoom.webrtcUrl}
                            readOnly
                            className="flex-1 border-gray-300 rounded-l-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-50"
                          />
                          <button
                            onClick={() => copyToClipboard(activeRoom.webrtcUrl)}
                            className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100"
                          >
                            <DocumentDuplicateIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 创建直播间模态框 */}
      {showCreateRoom && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">创建直播间</h3>
                <button
                  onClick={() => setShowCreateRoom(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <form onSubmit={createRoom} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">直播间标题 *</label>
                  <input
                    type="text"
                    required
                    value={newRoom.title}
                    onChange={(e) => setNewRoom({...newRoom, title: e.target.value})}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="输入直播间标题"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">直播间描述</label>
                  <textarea
                    rows={3}
                    value={newRoom.description}
                    onChange={(e) => setNewRoom({...newRoom, description: e.target.value})}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="输入直播间描述..."
                  />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowCreateRoom(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                  >
                    创建
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StreamerDashboard;