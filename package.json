{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@tonaljs/tonal": "^4.10.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "axios": "^1.10.0", "dsp.js": "^1.0.1", "meyda": "^5.6.3", "midi-file": "^1.2.4", "midi-parser-js": "^4.0.4", "peaks.js": "^3.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-piano": "^3.1.3", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "socket.io-client": "^4.8.1", "standardized-audio-context": "^25.3.77", "tone": "^15.1.22", "typescript": "^4.9.5", "wavesurfer.js": "^7.9.9", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "10.4.17", "postcss": "8.4.35", "tailwindcss": "3.4.1"}}