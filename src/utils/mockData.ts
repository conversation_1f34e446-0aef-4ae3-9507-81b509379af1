import { User, UserRole } from '../types/user';

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: '1',
    name: '张音乐',
    email: '<EMAIL>',
    avatar: '/avatars/zhang.jpg',
    role: 'musician',
    verified: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: '李主播',
    email: '<EMAIL>',
    avatar: '/avatars/li.jpg',
    role: 'streamer',
    verified: true,
    createdAt: '2024-01-16T11:00:00Z',
    updatedAt: '2024-01-16T11:00:00Z'
  },
  {
    id: '3',
    name: '王制作',
    email: '<EMAIL>',
    avatar: '/avatars/wang.jpg',
    role: 'producer',
    verified: true,
    createdAt: '2024-01-17T12:00:00Z',
    updatedAt: '2024-01-17T12:00:00Z'
  },
  {
    id: '4',
    name: '赵投资',
    email: '<EMAIL>',
    avatar: '/avatars/zhao.jpg',
    role: 'investor',
    verified: true,
    createdAt: '2024-01-18T13:00:00Z',
    updatedAt: '2024-01-18T13:00:00Z'
  },
  {
    id: '5',
    name: '刘发行',
    email: '<EMAIL>',
    avatar: '/avatars/liu.jpg',
    role: 'publisher',
    verified: true,
    createdAt: '2024-01-19T14:00:00Z',
    updatedAt: '2024-01-19T14:00:00Z'
  },
  {
    id: '6',
    name: '普通用户',
    email: '<EMAIL>',
    role: 'user',
    verified: false,
    createdAt: '2024-01-20T15:00:00Z',
    updatedAt: '2024-01-20T15:00:00Z'
  }
];

// 登录函数 - 用于演示
export const mockLogin = (email: string, password: string): Promise<User | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const user = mockUsers.find(u => u.email === email);
      if (user && password === '123456') {
        resolve(user);
      } else {
        resolve(null);
      }
    }, 1000);
  });
};

// 模拟直播间数据
export const mockLiveRooms = [
  {
    id: '1',
    title: '音乐创作分享直播间',
    description: '分享原创音乐作品，与粉丝互动交流',
    isLive: false,
    streamKey: 'live_stream_key_123456',
    rtmpUrl: 'rtmp://live.example.com/live',
    webrtcUrl: 'wss://webrtc.example.com/live/room1',
    viewerCount: 0,
    createdAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    title: '电音制作教学',
    description: '教大家如何制作电子音乐',
    isLive: true,
    streamKey: 'live_stream_key_789012',
    rtmpUrl: 'rtmp://live.example.com/live',
    webrtcUrl: 'wss://webrtc.example.com/live/room2',
    viewerCount: 156,
    createdAt: '2024-01-16T11:00:00Z'
  }
];

// 快速切换用户函数 - 仅用于开发演示
export const switchToRole = (role: UserRole): User => {
  const user = mockUsers.find(u => u.role === role);
  if (!user) {
    throw new Error(`No mock user found for role: ${role}`);
  }
  return user;
};