import React from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';

interface BasicInfoStepProps {
  spaceData: any;
  setSpaceData: (data: any) => void;
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ spaceData, setSpaceData }) => {
  const { language } = useLanguage();

  const updateSpaceData = (field: string, value: any) => {
    setSpaceData((prev: any) => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-8">
      {/* Step Title */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {language === 'zh' ? '填写基本信息' : 'Fill Basic Information'}
        </h2>
        <p className="text-gray-600">
          {language === 'zh' ? '为您的协创空间设置名称、简介和隐私设置' : 'Set name, description and privacy settings for your collaboration space'}
        </p>
      </div>

      {/* Space Name */}
      <div>
        <label className="block text-lg font-bold text-gray-900 mb-3">
          {language === 'zh' ? '空间名称' : 'Space Name'} <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          placeholder={language === 'zh' ? '请输入空间名称，例如：夏日回忆' : 'Enter space name, e.g.: Summer Memories'}
          value={spaceData.spaceName}
          onChange={(e) => updateSpaceData('spaceName', e.target.value)}
          className="w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400"
          maxLength={50}
        />
        <div className="flex justify-between items-center mt-2">
          <p className="text-sm text-gray-500">
            {language === 'zh' ? '好的名称能吸引更多优质创作者' : 'A good name attracts more quality creators'}
          </p>
          <span className="text-sm text-gray-400">
            {spaceData.spaceName.length}/50
          </span>
        </div>
      </div>

      {/* Space Description */}
      <div>
        <label className="block text-lg font-bold text-gray-900 mb-3">
          {language === 'zh' ? '空间简介' : 'Space Description'}
        </label>
        <textarea
          placeholder={language === 'zh' 
            ? '描述这个协创空间的目标、风格和期望成果。例如：我们要创作一首关于夏日回忆的流行歌曲，希望能表达青春的美好与怀念...' 
            : 'Describe the goals, style and expected outcomes of this collaboration space. E.g.: We want to create a pop song about summer memories, hoping to express the beauty and nostalgia of youth...'}
          value={spaceData.spaceDescription}
          onChange={(e) => updateSpaceData('spaceDescription', e.target.value)}
          rows={5}
          className="w-full px-4 py-3 text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400 resize-none"
          maxLength={500}
        />
        <div className="flex justify-between items-center mt-2">
          <p className="text-sm text-gray-500">
            {language === 'zh' ? '详细的描述能帮助成员更好地理解项目' : 'Detailed descriptions help members better understand the project'}
          </p>
          <span className="text-sm text-gray-400">
            {spaceData.spaceDescription.length}/500
          </span>
        </div>
      </div>

      {/* Privacy Settings */}
      <div>
        <label className="block text-lg font-bold text-gray-900 mb-4">
          {language === 'zh' ? '隐私设置' : 'Privacy Settings'} <span className="text-red-500">*</span>
        </label>
        <div className="space-y-4">
          <div 
            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
              spaceData.privacy === 'public' 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
            onClick={() => updateSpaceData('privacy', 'public')}
          >
            <div className="flex items-start gap-3">
              <input
                type="radio"
                name="privacy"
                value="public"
                checked={spaceData.privacy === 'public'}
                onChange={() => updateSpaceData('privacy', 'public')}
                className="mt-1 text-blue-600 focus:ring-blue-500"
              />
              <div className="flex-1 text-left">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  {language === 'zh' ? '公开空间' : 'Public Space'}
                </h3>
                <p className="text-sm text-gray-600 text-left">
                  {language === 'zh' 
                    ? '所有人都可以看到这个空间，并可以申请加入。适合寻找更多合作伙伴的项目。'
                    : 'Everyone can see this space and apply to join. Suitable for projects looking for more collaborators.'
                  }
                </p>
              </div>
            </div>
          </div>

          <div 
            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
              spaceData.privacy === 'participants' 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
            onClick={() => updateSpaceData('privacy', 'participants')}
          >
            <div className="flex items-start gap-3">
              <input
                type="radio"
                name="privacy"
                value="participants"
                checked={spaceData.privacy === 'participants'}
                onChange={() => updateSpaceData('privacy', 'participants')}
                className="mt-1 text-blue-600 focus:ring-blue-500"
              />
              <div className="flex-1 text-left">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  {language === 'zh' ? '仅参与者可见' : 'Participants Only'}
                </h3>
                <p className="text-sm text-gray-600 text-left">
                  {language === 'zh' 
                    ? '只有被邀请的成员可以看到和参与。适合私密的合作项目。'
                    : 'Only invited members can see and participate. Suitable for private collaboration projects.'
                  }
                </p>
              </div>
            </div>
          </div>

          <div 
            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
              spaceData.privacy === 'searchable' 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
            onClick={() => updateSpaceData('privacy', 'searchable')}
          >
            <div className="flex items-start gap-3">
              <input
                type="radio"
                name="privacy"
                value="searchable"
                checked={spaceData.privacy === 'searchable'}
                onChange={() => updateSpaceData('privacy', 'searchable')}
                className="mt-1 text-blue-600 focus:ring-blue-500"
              />
              <div className="flex-1 text-left">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  {language === 'zh' ? '限制搜索' : 'Limited Search'}
                </h3>
                <p className="text-sm text-gray-600 text-left">
                  {language === 'zh' 
                    ? '可被搜索到，但需要申请才能查看详细内容。适合半开放的合作项目。'
                    : 'Can be found in searches, but requires application to view detailed content. Suitable for semi-open collaboration projects.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Preview */}
      {spaceData.spaceName && (
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {language === 'zh' ? '预览' : 'Preview'}
          </h3>
          <div className="bg-white rounded-lg p-4 border border-gray-200 text-left">
            <div className="flex items-start justify-between mb-3">
              <h4 className="text-lg font-semibold text-gray-900 text-left">{spaceData.spaceName}</h4>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                spaceData.privacy === 'public' ? 'bg-green-100 text-green-800' :
                spaceData.privacy === 'participants' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {spaceData.privacy === 'public' ? 
                  (language === 'zh' ? '公开' : 'Public') :
                  spaceData.privacy === 'participants' ? 
                  (language === 'zh' ? '私密' : 'Private') :
                  (language === 'zh' ? '限制' : 'Limited')
                }
              </span>
            </div>
            {spaceData.spaceDescription && (
              <p className="text-gray-600 text-sm leading-relaxed text-left">
                {spaceData.spaceDescription}
              </p>
            )}
            <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
              <span className="text-left">{language === 'zh' ? '创建者：您' : 'Creator: You'}</span>
              <span className="text-right">{language === 'zh' ? '状态：招募中' : 'Status: Recruiting'}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BasicInfoStep;