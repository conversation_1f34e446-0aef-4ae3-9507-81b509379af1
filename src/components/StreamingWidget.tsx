import React, { useState, useRef, useEffect } from 'react';
import { 
  VideoCameraIcon, 
  MicrophoneIcon, 
  StopIcon, 
  PlayIcon 
} from '@heroicons/react/24/outline';

interface StreamingWidgetProps {
  rtmpUrl?: string;
  streamKey?: string;
  webrtcUrl?: string;
  onStreamStart?: () => void;
  onStreamStop?: () => void;
}

const StreamingWidget: React.FC<StreamingWidgetProps> = ({
  rtmpUrl,
  streamKey,
  webrtcUrl,
  onStreamStart,
  onStreamStop
}) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [hasCamera, setHasCamera] = useState(false);
  const [hasMicrophone, setHasMicrophone] = useState(false);
  const [cameraEnabled, setCameraEnabled] = useState(true);
  const [microphoneEnabled, setMicrophoneEnabled] = useState(true);
  const [streamQuality, setStreamQuality] = useState('720p');
  const [error, setError] = useState<string | null>(null);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);

  useEffect(() => {
    checkMediaDevices();
    return cleanup;
  }, []);

  const checkMediaDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      setHasCamera(devices.some(device => device.kind === 'videoinput'));
      setHasMicrophone(devices.some(device => device.kind === 'audioinput'));
    } catch (error) {
      console.error('Error checking media devices:', error);
      setError('无法检测媒体设备');
    }
  };

  const getMediaConstraints = () => {
    const videoConstraints = cameraEnabled && hasCamera ? {
      width: streamQuality === '1080p' ? 1920 : streamQuality === '720p' ? 1280 : 640,
      height: streamQuality === '1080p' ? 1080 : streamQuality === '720p' ? 720 : 480,
      frameRate: 30
    } : false;

    const audioConstraints = microphoneEnabled && hasMicrophone ? {
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true
    } : false;

    return {
      video: videoConstraints,
      audio: audioConstraints
    };
  };

  const startLocalPreview = async () => {
    try {
      setError(null);
      const constraints = getMediaConstraints();
      
      if (!constraints.video && !constraints.audio) {
        setError('请至少启用摄像头或麦克风');
        return;
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      mediaStreamRef.current = stream;
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      console.error('Error accessing media devices:', error);
      setError('无法访问摄像头或麦克风，请检查权限设置');
    }
  };

  const stopLocalPreview = () => {
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  };

  const startWebRTCStream = async () => {
    if (!webrtcUrl) {
      setError('WebRTC推流地址未配置');
      return;
    }

    try {
      const stream = mediaStreamRef.current;
      if (!stream) {
        await startLocalPreview();
        return;
      }

      // 创建RTCPeerConnection
      const pc = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' }
        ]
      });
      
      peerConnectionRef.current = pc;

      // 添加本地流到连接
      stream.getTracks().forEach(track => {
        pc.addTrack(track, stream);
      });

      // 创建offer并设置本地描述
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      // 这里应该将offer发送到信令服务器
      // 实际实现需要WebSocket连接到信令服务器
      console.log('WebRTC offer created:', offer);
      
      setIsStreaming(true);
      onStreamStart?.();
      
    } catch (error) {
      console.error('Error starting WebRTC stream:', error);
      setError('启动WebRTC推流失败');
    }
  };

  const startRTMPStream = async () => {
    if (!rtmpUrl || !streamKey) {
      setError('RTMP推流地址或密钥未配置');
      return;
    }

    // RTMP推流通常需要服务器端处理
    // 这里只是示例，实际需要通过WebRTC转RTMP服务
    try {
      const stream = mediaStreamRef.current;
      if (!stream) {
        await startLocalPreview();
        return;
      }

      // 发送开始推流请求到后端
      const { default: ApiService } = await import('../services/api');
      await ApiService.startRTMPStream({
        rtmpUrl: `${rtmpUrl}/${streamKey}`,
        quality: streamQuality
      });

      setIsStreaming(true);
      onStreamStart?.();

    } catch (error) {
      console.error('Error starting RTMP stream:', error);
      setError('启动RTMP推流失败');
    }
  };

  const stopStream = async () => {
    try {
      if (peerConnectionRef.current) {
        peerConnectionRef.current.close();
        peerConnectionRef.current = null;
      }

      // 停止RTMP推流
      if (rtmpUrl) {
        const { default: ApiService } = await import('../services/api');
        await ApiService.stopRTMPStream();
      }

      setIsStreaming(false);
      onStreamStop?.();
      
    } catch (error) {
      console.error('Error stopping stream:', error);
    }
  };

  const cleanup = () => {
    stopLocalPreview();
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
    }
  };

  const handleStreamStart = async () => {
    await startLocalPreview();
    
    // 根据配置选择推流方式
    if (webrtcUrl) {
      await startWebRTCStream();
    } else if (rtmpUrl && streamKey) {
      await startRTMPStream();
    } else {
      setError('未配置推流地址');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900 mb-2">直播推流</h3>
        
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* 视频预览 */}
        <div className="relative bg-gray-900 rounded-lg overflow-hidden mb-4" style={{ aspectRatio: '16/9' }}>
          <video
            ref={videoRef}
            autoPlay
            muted
            playsInline
            className="w-full h-full object-cover"
          />
          {!mediaStreamRef.current && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-white">
                <VideoCameraIcon className="mx-auto h-12 w-12 mb-2 opacity-50" />
                <p className="text-sm">点击开始预览</p>
              </div>
            </div>
          )}
          
          {/* 直播状态指示器 */}
          {isStreaming && (
            <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
              <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
              直播中
            </div>
          )}
        </div>

        {/* 控制面板 */}
        <div className="space-y-4">
          {/* 设备控制 */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setCameraEnabled(!cameraEnabled)}
              disabled={!hasCamera}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                cameraEnabled && hasCamera
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-500'
              } ${!hasCamera ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <VideoCameraIcon className="h-4 w-4 mr-2" />
              摄像头
            </button>
            
            <button
              onClick={() => setMicrophoneEnabled(!microphoneEnabled)}
              disabled={!hasMicrophone}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                microphoneEnabled && hasMicrophone
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-500'
              } ${!hasMicrophone ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <MicrophoneIcon className="h-4 w-4 mr-2" />
              麦克风
            </button>

            <select
              value={streamQuality}
              onChange={(e) => setStreamQuality(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="480p">480p (流畅)</option>
              <option value="720p">720p (标清)</option>
              <option value="1080p">1080p (高清)</option>
            </select>
          </div>

          {/* 主要控制按钮 */}
          <div className="flex items-center space-x-3">
            {!isStreaming ? (
              <>
                <button
                  onClick={startLocalPreview}
                  disabled={!hasCamera && !hasMicrophone}
                  className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlayIcon className="h-4 w-4 mr-2" />
                  开始预览
                </button>
                
                <button
                  onClick={handleStreamStart}
                  disabled={!mediaStreamRef.current || (!rtmpUrl && !webrtcUrl)}
                  className="flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlayIcon className="h-4 w-4 mr-2" />
                  开始直播
                </button>
              </>
            ) : (
              <button
                onClick={stopStream}
                className="flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-gray-600 hover:bg-gray-700"
              >
                <StopIcon className="h-4 w-4 mr-2" />
                停止直播
              </button>
            )}

            <button
              onClick={stopLocalPreview}
              disabled={!mediaStreamRef.current}
              className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <StopIcon className="h-4 w-4 mr-2" />
              停止预览
            </button>
          </div>
        </div>

        {/* 推流信息 */}
        {(rtmpUrl || webrtcUrl) && (
          <div className="mt-4 p-3 bg-gray-50 rounded-md">
            <h4 className="text-sm font-medium text-gray-700 mb-2">推流信息</h4>
            <div className="text-xs text-gray-600 space-y-1">
              {rtmpUrl && (
                <div>
                  <span className="font-medium">RTMP:</span> {rtmpUrl}
                </div>
              )}
              {webrtcUrl && (
                <div>
                  <span className="font-medium">WebRTC:</span> {webrtcUrl}
                </div>
              )}
              <div>
                <span className="font-medium">画质:</span> {streamQuality}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StreamingWidget;