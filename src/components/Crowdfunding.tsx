import React, { useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  CurrencyDollarIcon,
  UserGroupIcon,
  CalendarIcon,
  ClockIcon,
  HeartIcon,
  ShareIcon,
  PhotoIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowLeftIcon,
  PlusIcon,
  MusicalNoteIcon,
  TrophyIcon,
  GiftIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../contexts/LanguageContext';

interface CrowdfundingProject {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  targetAmount: number;
  currentAmount: number;
  currency: string;
  deadline: string;
  backers: number;
  category: 'music' | 'album' | 'concert' | 'equipment';
  status: 'active' | 'completed' | 'failed' | 'draft';
  creator: {
    name: string;
    avatar: string;
    verified: boolean;
  };
  rewards: {
    id: number;
    title: string;
    description: string;
    amount: number;
    backers: number;
    delivery: string;
    available: number;
  }[];
  collaborationSpace?: {
    id: number;
    name: string;
    memberCount: number;
  };
}

const Crowdfunding: React.FC = () => {
  const { action } = useParams<{ action?: string }>();
  const navigate = useNavigate();
  const { t, language } = useLanguage();
  const [selectedProject, setSelectedProject] = useState<CrowdfundingProject | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'my-projects' | 'supported'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Mock crowdfunding projects data
  const projects: CrowdfundingProject[] = useMemo(() => [
    {
      id: 1,
      title: language === 'zh' ? '《梦想起飞》专辑制作' : 'Dreams Take Flight Album Production',
      description: language === 'zh' ? '我们正在制作一张融合传统与现代元素的流行专辑，需要资金支持录音棚租赁、混音制作和发行推广。' : 'We are producing a pop album that blends traditional and modern elements, needing funding for studio rental, mixing production, and distribution.',
      coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=400&fit=crop',
      targetAmount: 50000,
      currentAmount: 32500,
      currency: '¥',
      deadline: '2024-03-15',
      backers: 127,
      category: 'album',
      status: 'active',
      creator: {
        name: language === 'zh' ? '王音乐制作人' : 'Wang Music Producer',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        verified: true
      },
      rewards: [
        {
          id: 1,
          title: language === 'zh' ? '数字专辑' : 'Digital Album',
          description: language === 'zh' ? '获得完整数字专辑，包含10首原创歌曲' : 'Get the complete digital album with 10 original songs',
          amount: 99,
          backers: 45,
          delivery: language === 'zh' ? '2024年4月' : 'April 2024',
          available: 500
        },
        {
          id: 2,
          title: language === 'zh' ? '签名实体专辑' : 'Signed Physical Album',
          description: language === 'zh' ? '限量签名版实体专辑 + 数字专辑' : 'Limited signed physical album + digital album',
          amount: 299,
          backers: 32,
          delivery: language === 'zh' ? '2024年5月' : 'May 2024',
          available: 100
        },
        {
          id: 3,
          title: language === 'zh' ? '专属演唱会门票' : 'Exclusive Concert Ticket',
          description: language === 'zh' ? '专辑发布演唱会VIP门票 + 签名专辑' : 'Album release concert VIP ticket + signed album',
          amount: 899,
          backers: 15,
          delivery: language === 'zh' ? '2024年6月' : 'June 2024',
          available: 50
        }
      ],
      collaborationSpace: {
        id: 1,
        name: language === 'zh' ? '梦想起飞协创空间' : 'Dreams Take Flight Collaboration',
        memberCount: 4
      }
    },
    {
      id: 2,
      title: language === 'zh' ? '独立音乐工作室建设' : 'Independent Music Studio Construction',
      description: language === 'zh' ? '建设一个专业的独立音乐录音工作室，为更多音乐人提供高质量的录音服务。' : 'Building a professional independent music recording studio to provide high-quality recording services for more musicians.',
      coverImage: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=600&h=400&fit=crop',
      targetAmount: 100000,
      currentAmount: 15800,
      currency: '¥',
      deadline: '2024-04-30',
      backers: 43,
      category: 'equipment',
      status: 'active',
      creator: {
        name: language === 'zh' ? '李录音师' : 'Li Sound Engineer',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        verified: true
      },
      rewards: [
        {
          id: 4,
          title: language === 'zh' ? '工作室参观' : 'Studio Tour',
          description: language === 'zh' ? '工作室建成后专属参观体验' : 'Exclusive studio tour experience after completion',
          amount: 199,
          backers: 12,
          delivery: language === 'zh' ? '2024年7月' : 'July 2024',
          available: 200
        },
        {
          id: 5,
          title: language === 'zh' ? '免费录音时长' : 'Free Recording Hours',
          description: language === 'zh' ? '获得4小时免费录音时长' : 'Get 4 hours of free recording time',
          amount: 999,
          backers: 8,
          delivery: language === 'zh' ? '2024年8月' : 'August 2024',
          available: 50
        }
      ]
    }
  ], [language]);

  const categories = [
    { key: 'all', label: language === 'zh' ? '全部' : 'All' },
    { key: 'music', label: language === 'zh' ? '音乐制作' : 'Music Production' },
    { key: 'album', label: language === 'zh' ? '专辑发行' : 'Album Release' },
    { key: 'concert', label: language === 'zh' ? '演出活动' : 'Concerts' },
    { key: 'equipment', label: language === 'zh' ? '设备器材' : 'Equipment' }
  ];

  const filteredProjects = useMemo(() => {
    return projects.filter(project => {
      const matchesCategory = selectedCategory === 'all' || project.category === selectedCategory;
      const matchesTab = activeTab === 'all' || 
                        (activeTab === 'my-projects' && project.creator.name.includes('王')) ||
                        (activeTab === 'supported' && project.backers > 100);
      return matchesCategory && matchesTab;
    });
  }, [projects, selectedCategory, activeTab]);

  const getProgressPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const getDaysLeft = (deadline: string) => {
    const today = new Date();
    const endDate = new Date(deadline);
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(diffDays, 0);
  };

  if (action === 'apply') {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center py-4">
              <button
                onClick={() => navigate('/collaboration')}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeftIcon className="h-6 w-6" />
              </button>
              <h1 className="text-2xl font-bold text-gray-900">
                {language === 'zh' ? '申请众筹' : 'Apply for Crowdfunding'}
              </h1>
            </div>
          </div>
        </div>

        {/* Application Form */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-sm border p-8">
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                {language === 'zh' ? '项目基本信息' : 'Project Basic Information'}
              </h2>
              <p className="text-gray-600">
                {language === 'zh' ? '填写您的众筹项目详细信息' : 'Fill in the details of your crowdfunding project'}
              </p>
            </div>

            <form className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'zh' ? '项目标题' : 'Project Title'}
                </label>
                <input
                  type="text"
                  placeholder={language === 'zh' ? '输入项目标题...' : 'Enter project title...'}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'zh' ? '项目描述' : 'Project Description'}
                </label>
                <textarea
                  rows={6}
                  placeholder={language === 'zh' ? '详细描述您的项目...' : 'Describe your project in detail...'}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {language === 'zh' ? '目标金额' : 'Target Amount'}
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                    <input
                      type="number"
                      placeholder="50000"
                      className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {language === 'zh' ? '众筹期限' : 'Crowdfunding Deadline'}
                  </label>
                  <input
                    type="date"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'zh' ? '项目分类' : 'Project Category'}
                </label>
                <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="music">{language === 'zh' ? '音乐制作' : 'Music Production'}</option>
                  <option value="album">{language === 'zh' ? '专辑发行' : 'Album Release'}</option>
                  <option value="concert">{language === 'zh' ? '演出活动' : 'Concerts'}</option>
                  <option value="equipment">{language === 'zh' ? '设备器材' : 'Equipment'}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'zh' ? '项目封面' : 'Project Cover'}
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
                  <PhotoIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600 mb-2">
                    {language === 'zh' ? '点击上传或拖拽图片到此处' : 'Click to upload or drag image here'}
                  </p>
                  <p className="text-sm text-gray-500">
                    {language === 'zh' ? '支持 JPG、PNG 格式，建议尺寸 1200x800' : 'Support JPG, PNG format, recommended size 1200x800'}
                  </p>
                </div>
              </div>

              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {language === 'zh' ? '回报设置' : 'Reward Settings'}
                </h3>
                <div className="space-y-4">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {language === 'zh' ? '回报标题' : 'Reward Title'}
                        </label>
                        <input
                          type="text"
                          placeholder={language === 'zh' ? '数字专辑' : 'Digital Album'}
                          className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {language === 'zh' ? '支持金额' : 'Support Amount'}
                        </label>
                        <input
                          type="number"
                          placeholder="99"
                          className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {language === 'zh' ? '限量数量' : 'Limited Quantity'}
                        </label>
                        <input
                          type="number"
                          placeholder="500"
                          className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>
                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {language === 'zh' ? '回报描述' : 'Reward Description'}
                      </label>
                      <textarea
                        rows={2}
                        placeholder={language === 'zh' ? '详细描述这个回报...' : 'Describe this reward in detail...'}
                        className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      />
                    </div>
                  </div>
                  
                  <button
                    type="button"
                    className="flex items-center gap-2 px-4 py-2 border border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors"
                  >
                    <PlusIcon className="h-5 w-5" />
                    {language === 'zh' ? '添加回报档位' : 'Add Reward Tier'}
                  </button>
                </div>
              </div>

              <div className="flex gap-4 pt-6">
                <button
                  type="button"
                  onClick={() => navigate('/collaboration')}
                  className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {language === 'zh' ? '取消' : 'Cancel'}
                </button>
                <button
                  type="submit"
                  className="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {language === 'zh' ? '提交申请' : 'Submit Application'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }

  if (selectedProject) {
    const progressPercentage = getProgressPercentage(selectedProject.currentAmount, selectedProject.targetAmount);
    const daysLeft = getDaysLeft(selectedProject.deadline);

    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center py-4">
              <button
                onClick={() => setSelectedProject(null)}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeftIcon className="h-6 w-6" />
              </button>
              <h1 className="text-2xl font-bold text-gray-900">
                {language === 'zh' ? '项目详情' : 'Project Details'}
              </h1>
            </div>
          </div>
        </div>

        {/* Project Detail */}
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left - Project Info */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
                <img
                  src={selectedProject.coverImage}
                  alt={selectedProject.title}
                  className="w-full h-64 object-cover"
                />
                <div className="p-6">
                  <h1 className="text-2xl font-bold text-gray-900 mb-4">{selectedProject.title}</h1>
                  <p className="text-gray-600 leading-relaxed">{selectedProject.description}</p>
                </div>
              </div>

              {/* Creator Info */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {language === 'zh' ? '项目发起人' : 'Project Creator'}
                </h3>
                <div className="flex items-center space-x-4">
                  <img
                    src={selectedProject.creator.avatar}
                    alt={selectedProject.creator.name}
                    className="w-12 h-12 rounded-full"
                  />
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-gray-900">{selectedProject.creator.name}</h4>
                      {selectedProject.creator.verified && (
                        <CheckCircleIcon className="h-5 w-5 text-blue-500" />
                      )}
                    </div>
                    {selectedProject.collaborationSpace && (
                      <p className="text-sm text-gray-600">
                        {language === 'zh' ? '来自协创空间：' : 'From collaboration space: '}
                        <span className="text-blue-600">{selectedProject.collaborationSpace.name}</span>
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Right - Support Info */}
            <div className="space-y-6">
              {/* Progress */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="mb-4">
                  <div className="flex justify-between items-baseline mb-2">
                    <span className="text-2xl font-bold text-gray-900">
                      {selectedProject.currency}{selectedProject.currentAmount.toLocaleString()}
                    </span>
                    <span className="text-gray-500">
                      {language === 'zh' ? '目标' : 'Goal'} {selectedProject.currency}{selectedProject.targetAmount.toLocaleString()}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progressPercentage}%` }}
                    ></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-lg font-semibold text-gray-900">{selectedProject.backers}</div>
                      <div className="text-sm text-gray-500">{language === 'zh' ? '支持者' : 'Backers'}</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-gray-900">{daysLeft}</div>
                      <div className="text-sm text-gray-500">{language === 'zh' ? '剩余天数' : 'Days Left'}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Rewards */}
              <div className="bg-white rounded-lg shadow-sm border">
                <div className="p-6 border-b">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {language === 'zh' ? '支持回报' : 'Support Rewards'}
                  </h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {selectedProject.rewards.map((reward) => (
                    <div key={reward.id} className="p-6">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-semibold text-gray-900">
                            {selectedProject.currency}{reward.amount}
                          </span>
                          <GiftIcon className="h-5 w-5 text-blue-500" />
                        </div>
                        <span className="text-sm text-gray-500">
                          {reward.backers} {language === 'zh' ? '人支持' : 'backers'}
                        </span>
                      </div>
                      <h4 className="font-medium text-gray-900 mb-2">{reward.title}</h4>
                      <p className="text-sm text-gray-600 mb-3">{reward.description}</p>
                      <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                        <span>{language === 'zh' ? '预计交付：' : 'Estimated delivery: '}{reward.delivery}</span>
                        <span>{language === 'zh' ? '剩余' : 'Left'} {reward.available}</span>
                      </div>
                      <button className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        {language === 'zh' ? '选择这个回报' : 'Select This Reward'}
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">
              {language === 'zh' ? '众筹平台' : 'Crowdfunding Platform'}
            </h1>
            <button
              onClick={() => navigate('/crowdfunding/apply')}
              className="flex items-center gap-2 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PlusIcon className="h-5 w-5" />
              {language === 'zh' ? '发起众筹' : 'Start Crowdfunding'}
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs and Filters */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <nav className="flex space-x-4">
            {[
              { key: 'all', label: language === 'zh' ? '全部项目' : 'All Projects' },
              { key: 'my-projects', label: language === 'zh' ? '我的项目' : 'My Projects' },
              { key: 'supported', label: language === 'zh' ? '我支持的' : 'Supported' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  activeTab === tab.key
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {categories.map((category) => (
              <option key={category.key} value={category.key}>
                {category.label}
              </option>
            ))}
          </select>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => {
            const progressPercentage = getProgressPercentage(project.currentAmount, project.targetAmount);
            const daysLeft = getDaysLeft(project.deadline);

            return (
              <div
                key={project.id}
                className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => setSelectedProject(project)}
              >
                <div className="relative">
                  <img
                    src={project.coverImage}
                    alt={project.title}
                    className="w-full h-48 object-cover rounded-t-lg"
                  />
                  <div className="absolute top-4 left-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      project.status === 'active' ? 'bg-green-100 text-green-800' :
                      project.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {project.status === 'active' ? (language === 'zh' ? '进行中' : 'Active') :
                       project.status === 'completed' ? (language === 'zh' ? '已完成' : 'Completed') :
                       (language === 'zh' ? '失败' : 'Failed')}
                    </span>
                  </div>
                  {project.collaborationSpace && (
                    <div className="absolute top-4 right-4">
                      <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">
                        {language === 'zh' ? '协创' : 'Collab'}
                      </span>
                    </div>
                  )}
                </div>

                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                    {project.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {project.description}
                  </p>

                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold text-gray-900">
                        {project.currency}{project.currentAmount.toLocaleString()}
                      </span>
                      <span className="text-sm text-gray-500">
                        {progressPercentage.toFixed(0)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progressPercentage}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <UserGroupIcon className="h-4 w-4" />
                      {project.backers} {language === 'zh' ? '支持者' : 'backers'}
                    </span>
                    <span className="flex items-center gap-1">
                      <ClockIcon className="h-4 w-4" />
                      {daysLeft} {language === 'zh' ? '天剩余' : 'days left'}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Crowdfunding;