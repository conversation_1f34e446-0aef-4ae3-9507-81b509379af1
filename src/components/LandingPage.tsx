import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { PlayIcon, UserGroupIcon, MusicalNoteIcon, HeartIcon, StarIcon, DevicePhoneMobileIcon, ComputerDesktopIcon, GlobeAltIcon } from '@heroicons/react/24/outline';
import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { useLanguage } from '../contexts/LanguageContext';
import Login from './auth/Login';
import Register from './auth/Register';

const LandingPage: React.FC = () => {
  const [showLogin, setShowLogin] = useState(false);
  const [showRegister, setShowRegister] = useState(false);
  const [showAppsMenu, setShowAppsMenu] = useState(false);
  const { language, setLanguage } = useLanguage();

  if (showLogin) {
    return <Login onClose={() => setShowLogin(false)} onSwitchToRegister={() => { setShowLogin(false); setShowRegister(true); }} />;
  }

  if (showRegister) {
    return <Register onClose={() => setShowRegister(false)} onSwitchToLogin={() => { setShowRegister(false); setShowLogin(true); }} />;
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Navigation Header */}
      <nav className="bg-black border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex items-center">
                <MusicalNoteIcon className="h-8 w-8 text-blue-500" />
                <span className="ml-2 text-white text-xl font-bold">iBOM</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* Apps Menu */}
              <div className="relative">
                <button
                  onClick={() => setShowAppsMenu(!showAppsMenu)}
                  className="flex items-center space-x-2 text-gray-300 hover:text-white px-3 py-2 text-sm transition-colors"
                >
                  <DevicePhoneMobileIcon className="h-4 w-4" />
                  <span>Apps</span>
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {showAppsMenu && (
                  <div className="absolute top-full right-0 mt-2 w-48 bg-gray-900 rounded-xl shadow-xl border border-gray-700 overflow-hidden z-20">
                    <div className="py-2">
                      <a
                        href="#"
                        className="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-800 transition-colors"
                        onClick={() => setShowAppsMenu(false)}
                      >
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                          </svg>
                        </div>
                        <div>
                          <div className="font-medium text-sm text-white">iOS App</div>
                          <div className="text-xs text-gray-400">Download from App Store</div>
                        </div>
                      </a>
                      <a
                        href="#"
                        className="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-800 transition-colors"
                        onClick={() => setShowAppsMenu(false)}
                      >
                        <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.523 15.3414c-.5511-.2484-.9878-.5894-1.3103-.9878c-.3225-.3984-.5894-.8738-.7765-1.4049c-.1871-.5311-.2806-1.1246-.2806-1.7805c0-.6559.0935-1.2494.2806-1.7805.1871-.5311.4540-1.0065.7765-1.4049.3225-.3984.7594-.7394 1.3103-.9878.5508-.2484 1.1853-.3725 1.9035-.3725.7182 0 1.3527.1241 1.9035.3725.5508.2484.9878.5894 1.3103.9878.3225.3984.5894.8738.7765 1.4049.1871.5311.2806 1.1246.2806 1.7805 0 .6559-.0935 1.2494-.2806 1.7805-.1871.5311-.4540 1.0065-.7765 1.4049-.3225.3984-.7595.7394-1.3103.9878-.5508.2484-1.1853.3725-1.9035.3725-.7182 0-1.3527-.1241-1.9035-.3725z"/>
                          </svg>
                        </div>
                        <div>
                          <div className="font-medium text-sm text-white">Android App</div>
                          <div className="text-xs text-gray-400">Download from Google Play</div>
                        </div>
                      </a>
                    </div>
                  </div>
                )}
              </div>
              
              <button
                onClick={() => setShowLogin(true)}
                className="text-gray-300 hover:text-white px-4 py-2 text-sm transition-colors"
              >
                Log in
              </button>
              <button
                onClick={() => setShowRegister(true)}
                className="bg-white text-black px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-100 transition-colors"
              >
                Sign up free
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative bg-black overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="relative z-10 pb-8 bg-black sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <h1 className="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
                  <span className="block">The Future of Music.</span>
                  <span className="block text-orange-500">Here Today</span>
                </h1>
                <p className="mt-3 text-base text-gray-300 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                  Make music with others online. Add to your tracks and engage with fans – free from the cloud.
                </p>
                <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                  <div className="rounded-md shadow">
                    <button
                      onClick={() => setShowRegister(true)}
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-black bg-white hover:bg-gray-100 md:py-4 md:text-lg md:px-10 transition-colors"
                    >
                      Get started
                    </button>
                  </div>
                </div>
                <p className="mt-3 text-sm text-gray-400">Free to use</p>
              </div>
            </main>
          </div>
        </div>
        <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
          <div className="h-56 w-full bg-gray-900 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center">
            <div className="w-80 h-60 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
              <div className="text-white text-center">
                <div className="text-2xl font-bold mb-2">iBOM Studio</div>
                <PlayIcon className="h-12 w-12 mx-auto" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Partner Logos */}
      <div className="bg-gray-100 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-6 lg:grid-cols-5">
            <div className="col-span-1 flex justify-center md:col-span-2 lg:col-span-1">
              <div className="text-gray-400 font-semibold text-lg">NME</div>
            </div>
            <div className="col-span-1 flex justify-center md:col-span-2 lg:col-span-1">
              <div className="text-gray-400 font-semibold text-lg">Pitchfork</div>
            </div>
            <div className="col-span-1 flex justify-center md:col-span-2 lg:col-span-1">
              <div className="text-gray-400 font-semibold text-lg">Rolling Stone</div>
            </div>
            <div className="col-span-1 flex justify-center md:col-span-3 lg:col-span-1">
              <div className="text-gray-400 font-semibold text-lg">Billboard</div>
            </div>
            <div className="col-span-2 flex justify-center md:col-span-3 lg:col-span-1">
              <div className="text-gray-400 font-semibold text-lg">Mixmag</div>
            </div>
          </div>
        </div>
      </div>

      {/* Music Creation For All */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-orange-600 font-semibold tracking-wide uppercase">Music creation for all</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Create, collaborate and share your music online
            </p>
          </div>

          <div className="mt-16">
            <dl className="space-y-10 md:space-y-0 md:grid md:grid-cols-3 md:gap-x-8 md:gap-y-10">
              <div className="relative">
                <dt>
                  <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-orange-500 text-white">
                    <PlayIcon className="h-6 w-6" />
                  </div>
                  <p className="ml-16 text-lg leading-6 font-medium text-gray-900">Fast</p>
                </dt>
                <dd className="mt-2 ml-16 text-base text-gray-500">
                  Record vocals or instruments, choose from our huge library of samples, loops and stems. Everything you need to get started.
                </dd>
              </div>

              <div className="relative">
                <dt>
                  <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-orange-500 text-white">
                    <UserGroupIcon className="h-6 w-6" />
                  </div>
                  <p className="ml-16 text-lg leading-6 font-medium text-gray-900">Unlimited</p>
                </dt>
                <dd className="mt-2 ml-16 text-base text-gray-500">
                  No limitations on your creativity and songs. Get unlimited storage and track creation with our free music-making tools.
                </dd>
              </div>

              <div className="relative">
                <dt>
                  <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-orange-500 text-white">
                    <HeartIcon className="h-6 w-6" />
                  </div>
                  <p className="ml-16 text-lg leading-6 font-medium text-gray-900">Social</p>
                </dt>
                <dd className="mt-2 ml-16 text-base text-gray-500">
                  Your space to develop your fanbase and share your music with like-minded creators and passionate music fans.
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* App Showcase */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
            <div>
              <h3 className="text-2xl font-extrabold text-gray-900 sm:text-3xl">
                Take your music anywhere
              </h3>
              <p className="mt-3 max-w-3xl text-lg text-gray-500">
                Download our mobile app to create beats, record vocals and collaborate with other musicians on the go.
              </p>
              <dl className="mt-10 space-y-10">
                <div className="relative">
                  <dt>
                    <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-orange-500 text-white">
                      <DevicePhoneMobileIcon className="h-6 w-6" />
                    </div>
                    <p className="ml-16 text-lg leading-6 font-medium text-gray-900">Mobile Studio</p>
                  </dt>
                  <dd className="mt-2 ml-16 text-base text-gray-500">
                    Create music on your phone with our mobile DAW. Record, edit and share your tracks wherever you are.
                  </dd>
                </div>

                <div className="relative">
                  <dt>
                    <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-orange-500 text-white">
                      <ComputerDesktopIcon className="h-6 w-6" />
                    </div>
                    <p className="ml-16 text-lg leading-6 font-medium text-gray-900">Desktop Power</p>
                  </dt>
                  <dd className="mt-2 ml-16 text-base text-gray-500">
                    Use the full power of our web-based studio on desktop for professional music production.
                  </dd>
                </div>
              </dl>
            </div>
            <div className="mt-8 grid grid-cols-2 gap-0.5 md:grid-cols-2 lg:mt-0 lg:grid-cols-2">
              <div className="col-span-1 flex justify-center py-8 px-8 bg-gray-100 rounded-lg">
                <DevicePhoneMobileIcon className="h-24 w-24 text-gray-400" />
              </div>
              <div className="col-span-1 flex justify-center py-8 px-8 bg-gray-100 rounded-lg">
                <ComputerDesktopIcon className="h-24 w-24 text-gray-400" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* What's the word? Reviews */}
      <div className="bg-white py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-base font-semibold text-orange-600 tracking-wide uppercase">What's the word?</h2>
          </div>
          <div className="mt-12 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {[1, 2, 3, 4].map((review) => (
              <div key={review} className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  "Amazing platform for creating music online. Love how easy it is to collaborate with other musicians."
                </p>
                <div className="text-xs text-gray-500">
                  Verified User
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Sound Packs & Mastering */}
      <div className="bg-gray-900 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
            <div>
              <h3 className="text-2xl font-extrabold text-white sm:text-3xl">
                Professional quality sounds, every 2 weeks
              </h3>
              <p className="mt-3 max-w-3xl text-lg text-gray-300">
                Access thousands of high-quality samples, loops and presets from top producers and labels.
              </p>
              <div className="mt-8">
                <button className="bg-orange-500 text-white px-6 py-3 rounded-md hover:bg-orange-600 transition-colors">
                  Explore Sounds
                </button>
              </div>
            </div>
            <div className="mt-8 lg:mt-0">
              <div className="grid grid-cols-3 gap-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-gray-800 rounded-lg p-4 text-center">
                    <MusicalNoteIcon className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                    <div className="text-white text-sm font-medium">Pack {i + 1}</div>
                    <div className="text-gray-400 text-xs">Hip Hop</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Final CTA */}
      <div className="bg-black py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            Join a global movement of over 100 million creators and fans.
          </h2>
          <p className="mt-4 text-lg text-gray-300">
            Start creating, collaborating and sharing your music today.
          </p>
          <div className="mt-8 flex justify-center space-x-4">
            <button
              onClick={() => setShowRegister(true)}
              className="bg-white text-black px-8 py-3 rounded-md hover:bg-gray-100 transition-colors font-medium"
            >
              Download MscBoom
            </button>
            <button
              onClick={() => setShowLogin(true)}
              className="border border-white text-white px-8 py-3 rounded-md hover:bg-white hover:text-black transition-colors font-medium"
            >
              Continue with browser
            </button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-black border-t border-gray-800">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Features</h3>
              <ul className="mt-4 space-y-4">
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Music Maker</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Voice Record</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Beat Maker</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Mastering</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Sounds</h3>
              <ul className="mt-4 space-y-4">
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Samples</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Presets</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Loops</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">MIDI</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Company</h3>
              <ul className="mt-4 space-y-4">
                <li><a href="#" className="text-base text-gray-300 hover:text-white">About</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Careers</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Press</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Terms</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Community</h3>
              <ul className="mt-4 space-y-4">
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Feed</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Discover</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Groups</a></li>
                <li><a href="#" className="text-base text-gray-300 hover:text-white">Events</a></li>
              </ul>
            </div>
          </div>
          <div className="mt-8 border-t border-gray-800 pt-8">
            <div className="flex justify-between items-center">
              <div className="text-white font-bold text-xl">iBOM</div>
              <p className="text-base text-gray-400">&copy; 2024 iBOM Technologies. All rights reserved.</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;