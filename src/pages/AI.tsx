import React, { useState } from 'react';
import LyricsAssistant from '../components/ai/LyricsAssistant';
import SmartComposition from '../components/ai/SmartComposition';
import VideoMaster from '../components/ai/VideoMaster';
import MusicTutor from '../components/ai/MusicTutor';
import { 
  PencilIcon,
  MusicalNoteIcon,
  VideoCameraIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';

const AI: React.FC = () => {
  const [activeTab, setActiveTab] = useState('lyrics');

  const tabs = [
    { id: 'lyrics', label: '歌词助手', component: LyricsAssistant, icon: PencilIcon },
    { id: 'composition', label: '智能编曲', component: SmartComposition, icon: MusicalNoteIcon },
    { id: 'video', label: '影像大师', component: VideoMaster, icon: VideoCameraIcon },
    { id: 'tutor', label: '音乐导师', component: MusicTutor, icon: AcademicCapIcon },
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || LyricsAssistant;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Left Sidebar */}
        <div className="w-80 bg-white shadow-sm border-r min-h-screen">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">AI 音乐助手</h1>
            
            {/* Navigation Menu */}
            <nav>
              <ul className="space-y-2">
                {tabs.map((tab) => {
                  const IconComponent = tab.icon;
                  return (
                    <li key={tab.id}>
                      <button
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full text-left px-4 py-3 rounded-lg transition-colors flex items-center gap-3 ${
                          activeTab === tab.id
                            ? 'bg-blue-50 text-blue-700 border border-blue-200'
                            : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <IconComponent className="w-5 h-5" />
                        {tab.label}
                      </button>
                    </li>
                  );
                })}
              </ul>
            </nav>

            {/* Additional Info */}
            <div className="mt-8 p-4 bg-blue-50 rounded-lg">
              <h3 className="text-sm font-medium text-blue-900 mb-2">AI 助手功能</h3>
              <p className="text-xs text-blue-700 leading-relaxed">
                使用先进的人工智能技术，为您提供全方位的音乐创作支持，从歌词创作到智能编曲，一站式解决您的音乐创作需求。
              </p>
            </div>
          </div>
        </div>

        {/* Right Content Area */}
        <div className="flex-1 p-6">
          <ActiveComponent />
        </div>
      </div>
    </div>
  );
};

export default AI;