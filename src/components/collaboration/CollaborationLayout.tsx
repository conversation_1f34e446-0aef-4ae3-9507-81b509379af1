import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { useUser } from '../../contexts/UserContext';
import AllSpaces from './AllSpaces';
import CreateSpace from './CreateSpace';
import CreateSpaceSteps from './CreateSpaceSteps';
import MyCollaborationSpaces from './MyCollaborationSpaces';
import ContractSigning from './ContractSigning';
import CopyrightContract from './CopyrightContract';
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

const CollaborationLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useUser();
  const [activeTab, setActiveTab] = useState('view-all');
  const [mySpacesExpanded, setMySpacesExpanded] = useState(true);

  // 检查登录状态，如果未登录且访问需要登录的页面，跳转到登录页
  useEffect(() => {
    const protectedRoutes = ['/collaboration/create', '/collaboration/my-spaces', '/collaboration/contract'];
    const currentPath = location.pathname;
    
    if (!isAuthenticated && protectedRoutes.some(route => currentPath.startsWith(route))) {
      navigate('/login', { state: { from: currentPath } });
    }
  }, [isAuthenticated, location.pathname, navigate]);

  // 根据当前路由设置活跃标签
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/view-all') || path === '/collaboration/' || path === '/collaboration') setActiveTab('view-all');
    else if (path.includes('/create')) setActiveTab('create');
    else if (path.includes('/my-spaces')) setActiveTab('my-spaces');
    else if (path.includes('/contract')) setActiveTab('contract');
    else setActiveTab('view-all');
  }, [location.pathname]);


  const navigationItems = [
    { key: 'view-all', label: '所有协创空间', path: '/collaboration/view-all' },
    { key: 'create', label: '创建协创空间', path: '/collaboration/create', requiresAuth: true },
    { key: 'my-spaces', label: '我的协创空间', path: '/collaboration/my-spaces', requiresAuth: true },
    { key: 'contract', label: '签署合约', path: '/collaboration/contract', requiresAuth: true }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full">
        <Routes>
          <Route path="/" element={<Navigate to="view-all" replace />} />
          <Route path="/view-all" element={<AllSpaces />} />
          <Route path="/create" element={<CreateSpaceSteps />} />
          <Route path="/my-spaces/*" element={<MyCollaborationSpaces />} />
          <Route path="/contract" element={<ContractSigning />} />
          <Route path="/copyright-contract" element={<CopyrightContract />} />
          <Route path="*" element={<Navigate to="view-all" replace />} />
        </Routes>
      </div>
    </div>
  );
};

export default CollaborationLayout;