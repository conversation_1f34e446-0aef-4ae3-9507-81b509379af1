import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { ApiService, ApiUser } from '../../services/api';

const ProfilePage: React.FC = () => {
  const { userId } = useParams<{ userId?: string }>();
  const [user, setUser] = useState<ApiUser | null>(null);
  const [isOwnProfile, setIsOwnProfile] = useState(false);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    nickname: '',
    bio: '',
    avatar: ''
  });

  useEffect(() => {
    loadUserProfile();
  }, [userId]);

  const loadUserProfile = async () => {
    try {
      if (userId) {
        const profile = await ApiService.getUserProfile(parseInt(userId));
        setUser(profile);
        setEditForm({
          nickname: profile.nickname || '',
          bio: profile.bio || '',
          avatar: profile.avatar || ''
        });
      } else {
        // 加载当前用户资料
        const currentUser = await ApiService.getCurrentUser();
        const userProfile: ApiUser = {
          ...currentUser,
          id: parseInt(currentUser.id),
          username: currentUser.name,
          nickname: currentUser.name,
          bio: ''
        };
        setUser(userProfile);
        setIsOwnProfile(true);
        setEditForm({
          nickname: userProfile.nickname || '',
          bio: userProfile.bio || '',
          avatar: userProfile.avatar || ''
        });
      }
    } catch (error) {
      console.error('Failed to load user profile:', error);
      // 使用模拟数据
      const mockUser: ApiUser = {
        id: parseInt(userId || '1'),
        username: 'demo_user',
        nickname: '演示用户',
        email: '<EMAIL>',
        avatar: '',
        bio: '这是一个演示用户的个人简介。热爱音乐，喜欢创作，经常参与协作项目。',
        role: 'user',
        name: '演示用户',
        verified: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setUser(mockUser);
      setIsOwnProfile(!userId);
      setEditForm({
        nickname: mockUser.nickname || '',
        bio: mockUser.bio || '',
        avatar: mockUser.avatar || ''
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!user || !isOwnProfile) return;

    try {
      await ApiService.updateUserProfile(user.id, editForm);
      setUser({
        ...user,
        nickname: editForm.nickname,
        bio: editForm.bio,
        avatar: editForm.avatar
      });
      setEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
      // 模拟更新成功
      setUser({
        ...user,
        nickname: editForm.nickname,
        bio: editForm.bio,
        avatar: editForm.avatar
      });
      setEditing(false);
    }
  };

  const handleFollow = async () => {
    if (!user) return;
    
    try {
      await ApiService.followUser(user.id);
      // 更新关注状态的逻辑
    } catch (error) {
      console.error('Failed to follow user:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">用户不存在</h3>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {/* 头部背景 */}
        <div className="h-32 bg-gradient-to-r from-indigo-500 to-purple-600"></div>
        
        {/* 用户信息 */}
        <div className="relative px-6 pb-6">
          <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
            {/* 头像 */}
            <div className="relative -mt-16 mb-4 sm:mb-0">
              <div className="w-32 h-32 bg-white rounded-full p-2 shadow-lg">
                {user.avatar ? (
                  <img 
                    src={user.avatar} 
                    alt={user.nickname || user.username}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full rounded-full bg-indigo-100 flex items-center justify-center">
                    <span className="text-3xl font-bold text-indigo-600">
                      {(user.nickname || user.username).charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
            </div>
            
            {/* 用户信息和操作按钮 */}
            <div className="flex-1 sm:pb-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {user.nickname || user.username}
                  </h1>
                  <p className="text-gray-500">@{user.username}</p>
                  <p className="text-sm text-gray-600 mt-1">
                    {user.role === 'musician' ? '认证音乐人' : '普通用户'}
                  </p>
                </div>
                
                <div className="mt-4 sm:mt-0 flex space-x-3">
                  {isOwnProfile ? (
                    <button
                      onClick={() => setEditing(!editing)}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      {editing ? '取消编辑' : '编辑资料'}
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={handleFollow}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
                      >
                        关注
                      </button>
                      <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                        消息
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* 个人简介 */}
          <div className="mt-6">
            {editing ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    昵称
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    value={editForm.nickname}
                    onChange={(e) => setEditForm({...editForm, nickname: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    个人简介
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    rows={4}
                    value={editForm.bio}
                    onChange={(e) => setEditForm({...editForm, bio: e.target.value})}
                  />
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={handleSaveProfile}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
                  >
                    保存
                  </button>
                  <button
                    onClick={() => setEditing(false)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    取消
                  </button>
                </div>
              </div>
            ) : (
              <p className="text-gray-700 leading-relaxed">
                {user.bio || '这个用户还没有添加个人简介。'}
              </p>
            )}
          </div>
        </div>
      </div>
      
      {/* 统计信息 */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-2xl font-bold text-indigo-600">12</div>
          <div className="text-sm text-gray-600">发布作品</div>
        </div>
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-2xl font-bold text-indigo-600">156</div>
          <div className="text-sm text-gray-600">关注者</div>
        </div>
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-2xl font-bold text-indigo-600">89</div>
          <div className="text-sm text-gray-600">关注中</div>
        </div>
      </div>
      
      {/* 作品展示 */}
      <div className="mt-6 bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">最新作品</h2>
        </div>
        <div className="p-6">
          <div className="text-center py-8 text-gray-500">
            暂无作品展示
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;