import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeftIcon,
  EyeIcon,
  HeartIcon,
  ShareIcon,
  SpeakerWaveIcon,
  MicrophoneIcon,
  VideoCameraIcon,
  ChatBubbleLeftIcon,
  FaceSmileIcon,
  PaperAirplaneIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';

interface LiveRoomDetailProps {}

interface ChatMessage {
  id: number;
  user: string;
  userAvatar: string;
  content: string;
  time: string;
  type?: 'message' | 'gift' | 'follow';
}

interface LiveRoom {
  id: string;
  title: string;
  streamer: string;
  streamerAvatar: string;
  viewerCount: number;
  status: 'live' | 'offline';
  category: string;
  description: string;
}

const LiveRoomDetail: React.FC<LiveRoomDetailProps> = () => {
  const { roomId } = useParams<{ roomId: string }>();
  const navigate = useNavigate();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [newMessage, setNewMessage] = useState('');
  const [isFollowing, setIsFollowing] = useState(false);
  const [viewerCount, setViewerCount] = useState(1250);
  const [showSettings, setShowSettings] = useState(false);

  // Mock live room data
  const liveRoom: LiveRoom = {
    id: roomId || '1',
    title: '午夜音乐电台 - 轻松民谣时光',
    streamer: '音乐主播小王',
    streamerAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    viewerCount: viewerCount,
    status: 'live',
    category: '流行音乐',
    description: '欢迎来到我的音乐直播间，今晚为大家带来轻松的民谣音乐'
  };

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 1,
      user: 'NavHub',
      userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?w=40&h=40&fit=crop&crop=face',
      content: 'Welcome to the stream 🎵',
      time: '刚刚',
      type: 'message'
    },
    {
      id: 2,
      user: '音乐爱好者',
      userAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
      content: 'Hope you enjoy the stream! I hope you like the support and please follow and add Pro directly to this!',
      time: '1分钟前',
      type: 'message'
    },
    {
      id: 3,
      user: '小李',
      userAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      content: '声音很清晰！',
      time: '2分钟前',
      type: 'message'
    }
  ]);

  // Simulate viewer count changes
  useEffect(() => {
    const interval = setInterval(() => {
      setViewerCount(prev => prev + Math.floor(Math.random() * 5) - 2);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;
    
    const message: ChatMessage = {
      id: Date.now(),
      user: '我',
      userAvatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=40&h=40&fit=crop&crop=face',
      content: newMessage,
      time: '刚刚',
      type: 'message'
    };
    
    setMessages(prev => [message, ...prev.slice(0, 19)]);
    setNewMessage('');
  };

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
  };

  const reactions = ['👍', '❤️', '😂', '😮', '😢', '😡', '👏'];

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="bg-gray-900 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/live')}
                className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5" />
                返回
              </button>
              <div className="flex items-center gap-3">
                <img
                  src={liveRoom.streamerAvatar}
                  alt={liveRoom.streamer}
                  className="w-8 h-8 rounded-full"
                />
                <div>
                  <h1 className="font-semibold">{liveRoom.streamer}</h1>
                  <p className="text-xs text-gray-400">{liveRoom.category}</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                <span>直播中</span>
                <EyeIcon className="w-4 h-4 ml-2" />
                <span>{viewerCount.toLocaleString()}</span>
              </div>
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
              >
                <Cog6ToothIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto flex h-[calc(100vh-80px)]">
        {/* Main Video Area */}
        <div className="flex-1 flex flex-col h-[calc(100vh-80px)]">
          {/* Video Player */}
          <div className="flex-1 relative bg-gray-800 overflow-hidden">
            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              autoPlay
              muted
              playsInline
              poster="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop"
            />
            
            {/* Video Overlay */}
            <div className="absolute inset-0">
              {/* Live indicator */}
              <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                LIVE
              </div>
              
              {/* Viewer count */}
              <div className="absolute top-4 right-4 bg-black bg-opacity-60 text-white px-3 py-1 rounded-full text-sm flex items-center">
                <EyeIcon className="h-4 w-4 mr-1" />
                {viewerCount.toLocaleString()}
              </div>

              {/* Video Controls */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-4">
                <button className="p-3 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70 transition-opacity">
                  <MicrophoneIcon className="w-5 h-5" />
                </button>
                <button className="p-3 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70 transition-opacity">
                  <SpeakerWaveIcon className="w-5 h-5" />
                </button>
                <button className="p-3 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70 transition-opacity">
                  <VideoCameraIcon className="w-5 h-5" />
                </button>
                <button className="p-3 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70 transition-opacity">
                  <AdjustmentsHorizontalIcon className="w-5 h-5" />
                </button>
              </div>

              {/* Reaction Bar */}
              <div className="absolute bottom-4 left-4 flex items-center gap-2">
                {reactions.map((reaction, index) => (
                  <button
                    key={index}
                    className="w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center hover:bg-opacity-70 transition-opacity text-lg"
                  >
                    {reaction}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Bottom Info Bar - Fixed height */}
          <div className="bg-gray-900 p-4 border-t border-gray-700 h-20 flex-shrink-0">
            <div className="flex items-center justify-between h-full">
              <div>
                <h2 className="font-semibold text-lg">{liveRoom.title}</h2>
                <p className="text-gray-400 text-sm">{liveRoom.description}</p>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={handleFollow}
                  className={`px-6 py-2 rounded-full font-medium transition-colors ${
                    isFollowing 
                      ? 'bg-gray-700 text-gray-300' 
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isFollowing ? '已关注' : '关注'}
                </button>
                <button className="p-2 hover:bg-gray-800 rounded-lg transition-colors">
                  <ShareIcon className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Right Chat Panel */}
        <div className="w-80 bg-gray-900 border-l border-gray-700 flex flex-col h-[calc(100vh-80px)]">
          {/* Chat Header */}
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold flex items-center gap-2">
                <ChatBubbleLeftIcon className="w-5 h-5" />
                聊天
              </h3>
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <UserGroupIcon className="w-4 h-4" />
                {viewerCount.toLocaleString()}
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div key={message.id} className="flex gap-3">
                <img
                  src={message.userAvatar}
                  alt={message.user}
                  className="w-8 h-8 rounded-full flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm text-blue-400">{message.user}</span>
                    <span className="text-xs text-gray-500">{message.time}</span>
                  </div>
                  <p className="text-sm text-gray-300 leading-relaxed break-words">
                    {message.content}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Message Input */}
          <div className="p-4 border-t border-gray-700">
            <div className="flex items-center gap-2 mb-3">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="说点什么..."
                className="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
              />
              <button
                onClick={handleSendMessage}
                className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PaperAirplaneIcon className="w-4 h-4" />
              </button>
            </div>
            
            <div className="flex items-center justify-between">
              <button className="flex items-center gap-2 text-sm text-gray-400 hover:text-white transition-colors">
                <FaceSmileIcon className="w-4 h-4" />
                表情
              </button>
              <button className="flex items-center gap-2 text-sm text-red-400 hover:text-red-300 transition-colors">
                <HeartIcon className="w-4 h-4" />
                点赞支持
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveRoomDetail;