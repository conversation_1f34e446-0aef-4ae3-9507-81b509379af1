import React, { useState } from 'react';
import { 
  GiftIcon, 
  StarIcon, 
  CalendarDaysIcon,
  UserPlusIcon,
  ListBulletIcon,
  QuestionMarkCircleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

const MyRewards: React.FC = () => {
  const [activeSection, setActiveSection] = useState('overview');
  const [checkedIn, setCheckedIn] = useState(false);
  const { t } = useLanguage();

  const sections = [
    { id: 'overview', name: t('rewards.overview'), icon: GiftIcon },
    { id: 'earn', name: t('rewards.earn.methods'), icon: StarIcon },
    { id: 'checkin', name: t('rewards.daily.checkin'), icon: CalendarDaysIcon },
    { id: 'invite', name: t('rewards.invite.friends'), icon: UserPlusIcon },
    { id: 'history', name: t('rewards.history'), icon: ListBulletIcon },
    { id: 'rules', name: t('rewards.rules'), icon: QuestionMarkCircleIcon }
  ];

  const userRewards = {
    totalPoints: 12580,
    totalCoins: 368,
    totalCrystals: 45,
    level: 'VIP2',
    nextLevel: 'VIP3',
    nextLevelPoints: 15000,
    checkinStreak: 7,
    invitedFriends: 12
  };

  const quickEarnMethods = [
    {
      id: 'daily_checkin',
      name: t('rewards.daily.checkin'),
      description: t('rewards.checkin.description'),
      reward: t('rewards.checkin.reward'),
      icon: CalendarDaysIcon,
      color: 'bg-green-100 text-green-800'
    },
    {
      id: 'upload_work',
      name: t('rewards.upload.work'),
      description: t('rewards.upload.description'),
      reward: t('rewards.upload.reward'),
      icon: GiftIcon,
      color: 'bg-blue-100 text-blue-800'
    },
    {
      id: 'invite_friend',
      name: t('rewards.invite.friends'),
      description: t('rewards.invite.description'),
      reward: t('rewards.invite.reward'),
      icon: UserPlusIcon,
      color: 'bg-purple-100 text-purple-800'
    },
    {
      id: 'complete_profile',
      name: t('rewards.complete.profile'),
      description: t('rewards.profile.description'),
      reward: t('rewards.profile.reward'),
      icon: StarIcon,
      color: 'bg-yellow-100 text-yellow-800'
    }
  ];

  const rewardHistory = [
    {
      id: 1,
      type: 'checkin',
      description: t('rewards.checkin.daily.reward'),
      points: 10,
      coins: 0,
      crystals: 0,
      time: '2024-01-20 09:00:00',
      status: 'completed'
    },
    {
      id: 2,
      type: 'upload',
      description: t('rewards.upload.work.specific'),
      points: 50,
      coins: 5,
      crystals: 0,
      time: '2024-01-19 14:30:00',
      status: 'completed'
    },
    {
      id: 3,
      type: 'invite',
      description: t('rewards.invite.friend.register'),
      points: 100,
      coins: 10,
      crystals: 1,
      time: '2024-01-18 16:45:00',
      status: 'completed'
    },
    {
      id: 4,
      type: 'level_up',
      description: t('rewards.level.up.vip2'),
      points: 0,
      coins: 20,
      crystals: 5,
      time: '2024-01-17 12:00:00',
      status: 'completed'
    }
  ];

  const checkinRewards = [
    { day: 1, reward: t('rewards.checkin.day1'), claimed: true },
    { day: 2, reward: t('rewards.checkin.day2'), claimed: true },
    { day: 3, reward: t('rewards.checkin.day3'), claimed: true },
    { day: 4, reward: t('rewards.checkin.day4'), claimed: true },
    { day: 5, reward: t('rewards.checkin.day5'), claimed: true },
    { day: 6, reward: t('rewards.checkin.day6'), claimed: true },
    { day: 7, reward: t('rewards.checkin.day7'), claimed: true },
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* {t('rewards.assets.overview')} */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100">{t('rewards.total.points')}</p>
              <p className="text-2xl font-bold">{userRewards.totalPoints.toLocaleString()}</p>
            </div>
            <StarIcon className="h-8 w-8 text-blue-200" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-yellow-500 to-orange-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-100">{t('rewards.coins')}</p>
              <p className="text-2xl font-bold">{userRewards.totalCoins}</p>
            </div>
            <div className="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
              <span className="text-yellow-800 font-bold text-sm">¥</span>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100">{t('rewards.crystals')}</p>
              <p className="text-2xl font-bold">{userRewards.totalCrystals}</p>
            </div>
            <div className="w-8 h-8 bg-purple-400 rounded-full flex items-center justify-center">
              <span className="text-purple-800 font-bold text-sm">◆</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* {t('rewards.level.progress')} */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">{t('rewards.level.progress')}</h3>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center">
              <span className="text-indigo-600 font-bold text-lg">{userRewards.level}</span>
            </div>
            <div>
              <p className="font-semibold text-gray-900">{t('rewards.current.level')}: {userRewards.level}</p>
              <p className="text-gray-600">{t('rewards.next.level.points')} {userRewards.nextLevel} {t('rewards.need.points')} {userRewards.nextLevelPoints - userRewards.totalPoints}</p>
            </div>
          </div>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className="bg-indigo-600 h-3 rounded-full transition-all duration-300"
            style={{ width: `${(userRewards.totalPoints / userRewards.nextLevelPoints) * 100}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-sm text-gray-600 mt-2">
          <span>{userRewards.totalPoints}</span>
          <span>{userRewards.nextLevelPoints}</span>
        </div>
      </div>
      
      {/* {t('rewards.achievements.stats')} */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold mb-4">{t('rewards.checkin.stats')}</h3>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold text-indigo-600">{userRewards.checkinStreak}</p>
              <p className="text-gray-600">{t('rewards.checkin.consecutive.days')}</p>
            </div>
            <CalendarDaysIcon className="h-8 w-8 text-indigo-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold mb-4">{t('rewards.invite.stats')}</h3>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold text-green-600">{userRewards.invitedFriends}</p>
              <p className="text-gray-600">{t('rewards.invite.successful')}</p>
            </div>
            <UserPlusIcon className="h-8 w-8 text-green-500" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderEarnMethods = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">{t('rewards.earn.quick.methods')}</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {quickEarnMethods.map((method) => {
          const Icon = method.icon;
          return (
            <div key={method.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start space-x-4">
                <div className={`p-3 rounded-lg ${method.color}`}>
                  <Icon className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900">{method.name}</h4>
                  <p className="text-gray-600 text-sm mt-1">{method.description}</p>
                  <div className="flex items-center justify-between mt-3">
                    <span className="text-indigo-600 font-medium">{method.reward}</span>
                    <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm">
                      {t('rewards.go.complete')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  const renderCheckin = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">{t('rewards.daily.checkin')}</h3>
          <div className="text-sm text-gray-600">
{t('rewards.checkin.consecutive')} {userRewards.checkinStreak} {t('rewards.days')}
          </div>
        </div>
        
        <div className="grid grid-cols-7 gap-4 mb-6">
          {checkinRewards.map((reward, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border-2 text-center ${
                reward.claimed
                  ? 'border-green-200 bg-green-50'
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="text-sm font-medium text-gray-700 mb-2">
{t('rewards.day')} {reward.day}
              </div>
              <div className="text-xs text-gray-600 mb-2">
                {reward.reward}
              </div>
              {reward.claimed ? (
                <CheckCircleIcon className="h-5 w-5 text-green-500 mx-auto" />
              ) : (
                <div className="w-5 h-5 border-2 border-gray-300 rounded-full mx-auto"></div>
              )}
            </div>
          ))}
        </div>
        
        <div className="text-center">
          <button
            onClick={() => setCheckedIn(true)}
            disabled={checkedIn}
            className={`px-8 py-3 rounded-lg font-medium ${
              checkedIn
                ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                : 'bg-indigo-600 text-white hover:bg-indigo-700'
            }`}
          >
{checkedIn ? t('rewards.checked.in.today') : t('rewards.checkin.now')}
          </button>
        </div>
      </div>
    </div>
  );

  const renderHistory = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">{t('rewards.history.details')}</h3>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('rewards.type')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('rewards.description')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('rewards.points')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('rewards.coins')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('rewards.crystals')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('rewards.time')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {rewardHistory.map((record) => (
                <tr key={record.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      record.type === 'checkin' ? 'bg-green-100 text-green-800' :
                      record.type === 'upload' ? 'bg-blue-100 text-blue-800' :
                      record.type === 'invite' ? 'bg-purple-100 text-purple-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {record.type === 'checkin' ? t('rewards.type.checkin') :
                       record.type === 'upload' ? t('rewards.type.upload') :
                       record.type === 'invite' ? t('rewards.type.invite') : t('rewards.type.levelup')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {record.description}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {record.points > 0 ? `+${record.points}` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {record.coins > 0 ? `+${record.coins}` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {record.crystals > 0 ? `+${record.crystals}` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {record.time}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderInvite = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">{t('rewards.invite.friends')}</h3>
        <p className="text-gray-600 mb-6">{t('rewards.invite.benefits')}</p>
        
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white mb-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-indigo-100">{t('rewards.invite.reward.title')}</p>
              <p className="text-2xl font-bold">{t('rewards.invite.full.reward')}</p>
            </div>
            <UserPlusIcon className="h-8 w-8 text-indigo-200" />
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <p className="text-sm text-gray-600 mb-2">{t('rewards.invite.link')}:</p>
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value="https://ibom.music/invite/demo_user"
              readOnly
              className="flex-1 px-3 py-2 bg-white border border-gray-300 rounded-lg"
            />
            <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
              {t('rewards.copy')}
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-indigo-600">{userRewards.invitedFriends}</p>
            <p className="text-gray-600">{t('rewards.invite.successful')}</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{userRewards.invitedFriends * 100}</p>
            <p className="text-gray-600">{t('rewards.points.earned')}</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderRules = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">{t('rewards.rules')}</h3>
        
        <div className="space-y-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">{t('rewards.rules.points')}</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-600">
              <li>{t('rewards.rules.daily.checkin')}</li>
              <li>{t('rewards.rules.upload.work')}</li>
              <li>{t('rewards.rules.invite.friend')}</li>
              <li>{t('rewards.rules.complete.profile')}</li>
              <li>{t('rewards.rules.participate.activity')}</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">{t('rewards.rules.coins')}</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-600">
              <li>{t('rewards.rules.upload.quality')}</li>
              <li>{t('rewards.rules.invite.verify')}</li>
              <li>{t('rewards.rules.consecutive.checkin')}</li>
              <li>{t('rewards.rules.level.upgrade.coins')}</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">{t('rewards.rules.crystals')}</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-600">
              <li>{t('rewards.rules.invite.register')}</li>
              <li>{t('rewards.rules.level.upgrade.crystals')}</li>
              <li>{t('rewards.rules.special.activity')}</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">{t('rewards.rules.notes')}</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-600">
              <li>{t('rewards.rules.expiry')}</li>
              <li>{t('rewards.rules.violation')}</li>
              <li>{t('rewards.rules.final.interpretation')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'overview':
        return renderOverview();
      case 'earn':
        return renderEarnMethods();
      case 'checkin':
        return renderCheckin();
      case 'invite':
        return renderInvite();
      case 'history':
        return renderHistory();
      case 'rules':
        return renderRules();
      default:
        return renderOverview();
    }
  };

  return (
    <div className="space-y-6">
      {/* {t('rewards.top.tabs')} */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-wrap gap-4">
          {sections.map((section) => {
            const Icon = section.icon;
            const isActive = activeSection === section.id;
            
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  isActive
                    ? 'bg-indigo-50 text-indigo-700'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Icon className={`h-5 w-5 ${isActive ? 'text-indigo-500' : 'text-gray-400'}`} />
                <span className="font-medium">{section.name}</span>
              </button>
            );
          })}
        </div>
      </div>
      
      {/* {t('rewards.content.area')} */}
      {renderContent()}
    </div>
  );
};

export default MyRewards;