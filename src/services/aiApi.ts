import axios from 'axios';

const AI_API_BASE_URL = process.env.REACT_APP_AI_API_URL || 'http://localhost:8084/api/v1/ai';

const aiApiClient = axios.create({
  baseURL: AI_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30秒超时
});

// 响应拦截器
aiApiClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    console.error('AI API error:', error);
    throw error;
  }
);

// AI服务相关接口
export interface LyricsGenerateRequest {
  theme: string;
  style: string;
  mood: string;
  provider?: 'deepseek' | 'qwen' | 'chatgpt';
}

export interface LyricsGenerateResponse {
  success: boolean;
  content: string;
  provider: string;
}

export interface CompositionGenerateRequest {
  style: string;
  tempo: string;
  mood?: string;
  provider?: 'deepseek' | 'qwen' | 'chatgpt';
}

export interface CompositionGenerateResponse {
  success: boolean;
  suggestion: string;
  structure: string;
}

export interface VideoDescriptionRequest {
  theme: string;
  style?: string;
  provider?: 'deepseek' | 'qwen' | 'chatgpt';
}

export interface VideoDescriptionResponse {
  success: boolean;
  descriptions: string[];
}

export interface TutorFeedbackRequest {
  performance: string;
  audioData?: string;
  provider?: 'deepseek' | 'qwen' | 'chatgpt';
}

export interface TutorFeedbackResponse {
  success: boolean;
  feedback: string;
  overallScore: number;
  problemAreas: string[];
  suggestions: string[];
}

export interface AIServiceStatus {
  status: string;
  services: {
    deepseek: boolean;
    qwen: boolean;
    chatgpt: boolean;
  };
  timestamp: number;
}

export class AIApiService {
  // 歌词助手
  static async generateLyrics(request: LyricsGenerateRequest): Promise<LyricsGenerateResponse> {
    return await aiApiClient.post('/lyrics/generate', request);
  }

  // 智能编曲
  static async generateComposition(request: CompositionGenerateRequest): Promise<CompositionGenerateResponse> {
    return await aiApiClient.post('/composition/generate', request);
  }

  // 视频大师 - 图像描述生成
  static async generateImageDescription(request: VideoDescriptionRequest): Promise<VideoDescriptionResponse> {
    return await aiApiClient.post('/video/image/description', request);
  }

  // 音乐导师 - 反馈生成
  static async generateTutorFeedback(request: TutorFeedbackRequest): Promise<TutorFeedbackResponse> {
    return await aiApiClient.post('/tutor/feedback', request);
  }

  // 获取AI服务状态
  static async getServiceStatus(): Promise<AIServiceStatus> {
    return await aiApiClient.get('/status');
  }

  // 通用AI文本生成（用于扩展功能）
  static async generateText(prompt: string, provider?: string): Promise<{ success: boolean; content: string }> {
    return await aiApiClient.post('/generate', { prompt, provider });
  }
}

export default AIApiService;