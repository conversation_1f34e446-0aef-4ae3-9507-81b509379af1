import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlayIcon, HeartIcon, MagnifyingGlassIcon, AdjustmentsHorizontalIcon, ShareIcon, ChatBubbleLeftIcon, EyeIcon, ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { ApiService, Work } from '../../services/api';
import { useWorks } from '../../contexts/WorksContext';
import { useLanguage } from '../../contexts/LanguageContext';

interface CategoryItem {
  key: string;
  label: string;
  labelEn: string;
  children?: CategoryItem[];
}

const WorksPage: React.FC = () => {
  const [works, setWorks] = useState<Work[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('full-version-top');
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['full-version', 'demo']);
  const navigate = useNavigate();
  const { publishedWorks } = useWorks();
  const { t, language } = useLanguage();

  const categories: CategoryItem[] = useMemo(() => [
    {
      key: 'full-version',
      label: '完整版本',
      labelEn: 'Full Version',
      children: [
        { key: 'full-version-top', label: '热门', labelEn: 'Top' },
        { key: 'full-version-hot', label: '最新', labelEn: 'Hot' },
        { key: 'full-version-style', label: '风格', labelEn: 'Style' },
        { key: 'full-version-creator', label: '创作者', labelEn: 'Creator' }
      ]
    },
    {
      key: 'demo',
      label: 'Demo',
      labelEn: 'Demo',
      children: [
        { key: 'demo-top', label: '热门', labelEn: 'Top' },
        { key: 'demo-hot', label: '最新', labelEn: 'Hot' },
        { key: 'demo-style', label: '风格', labelEn: 'Style' },
        { key: 'demo-creator', label: '创作者', labelEn: 'Creator' }
      ]
    }
  ], []);

  const mockFullVersionWorks = useMemo(() => [
    {
      id: 1,
      title: language === 'zh' ? '夏日微风' : 'Summer Breeze',
      description: language === 'zh' ? '轻柔的夏日旋律，带来清凉的感受' : 'A gentle summer melody that brings a refreshing feeling',
      coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 2400,
      collaborators: [language === 'zh' ? '陈小薇' : 'Chen Xiaowei', language === 'zh' ? '马可' : 'Marco'],
      likes: 856,
      comments: 142,
      duration: '04:31',
      genre: language === 'zh' ? '流行' : 'Pop',
      artist: language === 'zh' ? '陈小薇' : 'Chen Xiaowei',
      artistAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'full-version' as any,
      price: 299,
      currency: 'HK$'
    },
    {
      id: 2,
      title: language === 'zh' ? '城市之光' : 'City Lights',
      description: language === 'zh' ? '都市夜晚的电子音乐' : 'Electronic music of urban nights',
      coverUrl: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 1800,
      collaborators: [language === 'zh' ? '王小明' : 'Wang Xiaoming', language === 'zh' ? '李诗雨' : 'Li Shiyu'],
      likes: 623,
      comments: 98,
      duration: '03:45',
      genre: language === 'zh' ? '电子' : 'Electronic',
      artist: language === 'zh' ? '王小明' : 'Wang Xiaoming',
      artistAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'full-version' as any,
      price: 199,
      currency: 'HK$'
    },
    {
      id: 5,
      title: language === 'zh' ? '月夜思君' : 'Moonlit Thoughts',
      description: language === 'zh' ? '古风与现代的完美融合，诉说思念之情' : 'Perfect fusion of ancient and modern, expressing longing feelings',
      coverUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 3200,
      collaborators: [language === 'zh' ? '李雅馨' : 'Li Yaxin', language === 'zh' ? '王志远' : 'Wang Zhiyuan'],
      likes: 1245,
      comments: 210,
      duration: '05:12',
      genre: language === 'zh' ? '古风' : 'Traditional',
      artist: language === 'zh' ? '李雅馨' : 'Li Yaxin',
      artistAvatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'full-version' as any,
      price: 399,
      currency: 'HK$'
    },
    {
      id: 6,
      title: language === 'zh' ? '摇滚青春' : 'Rock Youth',
      description: language === 'zh' ? '激情澎湃的摇滚乐，唤醒内心的力量' : 'Passionate rock music that awakens inner strength',
      coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 2890,
      collaborators: [language === 'zh' ? '张铁龙' : 'Zhang Tielong', language === 'zh' ? '刘强' : 'Liu Qiang'],
      likes: 980,
      comments: 156,
      duration: '04:08',
      genre: language === 'zh' ? '摇滚' : 'Rock',
      artist: language === 'zh' ? '张铁龙' : 'Zhang Tielong',
      artistAvatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'full-version' as any,
      price: 249,
      currency: 'HK$'
    },
    {
      id: 7,
      title: language === 'zh' ? '爵士夜色' : 'Jazz Nights',
      description: language === 'zh' ? '慵懒的爵士音调，营造浪漫氛围' : 'Lazy jazz tones creating romantic atmosphere',
      coverUrl: 'https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 1567,
      collaborators: [language === 'zh' ? '林美玲' : 'Lin Meiling', language === 'zh' ? '黄小华' : 'Huang Xiaohua'],
      likes: 725,
      comments: 89,
      duration: '06:21',
      genre: language === 'zh' ? '爵士' : 'Jazz',
      artist: language === 'zh' ? '林美玲' : 'Lin Meiling',
      artistAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'full-version' as any,
      price: 349,
      currency: 'HK$'
    },
    {
      id: 8,
      title: language === 'zh' ? '雨后彩虹' : 'Rainbow After Rain',
      description: language === 'zh' ? '温暖治愈的旋律，给人希望与力量' : 'Warm healing melody giving hope and strength',
      coverUrl: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 2156,
      collaborators: [language === 'zh' ? '苏晓雨' : 'Su Xiaoyu', language === 'zh' ? '陈阳光' : 'Chen Yangguang'],
      likes: 894,
      comments: 123,
      duration: '04:47',
      genre: language === 'zh' ? '治愈' : 'Healing',
      artist: language === 'zh' ? '苏晓雨' : 'Su Xiaoyu',
      artistAvatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'full-version' as any,
      price: 279,
      currency: 'HK$'
    },
    {
      id: 9,
      title: language === 'zh' ? '电音狂潮' : 'Electronic Wave',
      description: language === 'zh' ? '未来感十足的电子音乐，引领潮流' : 'Futuristic electronic music leading the trend',
      coverUrl: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 3456,
      collaborators: [language === 'zh' ? 'DJ小K' : 'DJ Xiao K', language === 'zh' ? '王电音' : 'Wang Dianyin'],
      likes: 1367,
      comments: 234,
      duration: '05:43',
      genre: language === 'zh' ? '电子' : 'Electronic',
      artist: language === 'zh' ? 'DJ小K' : 'DJ Xiao K',
      artistAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'full-version' as any,
      price: 329,
      currency: 'HK$'
    },
    {
      id: 10,
      title: language === 'zh' ? '乡愁' : 'Nostalgia',
      description: language === 'zh' ? '深情的民谣，勾起对故乡的思念' : 'Soulful folk song evoking memories of hometown',
      coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 1934,
      collaborators: [language === 'zh' ? '老王' : 'Lao Wang', language === 'zh' ? '小李' : 'Xiao Li'],
      likes: 756,
      comments: 98,
      duration: '04:25',
      genre: language === 'zh' ? '民谣' : 'Folk',
      artist: language === 'zh' ? '老王' : 'Lao Wang',
      artistAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'full-version' as any,
      price: 219,
      currency: 'HK$'
    }
  ], [language]);

  const mockDemoWorks = useMemo(() => [
    {
      id: 3,
      title: language === 'zh' ? '山海之间 (Demo)' : 'Between Mountains and Sea (Demo)',
      description: language === 'zh' ? '民谣风格的Demo版本' : 'Folk style demo version',
      coverUrl: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 1200,
      collaborators: [language === 'zh' ? '张三和' : 'Zhang Sanhe'],
      likes: 450,
      comments: 89,
      duration: '02:30',
      genre: language === 'zh' ? '民谣' : 'Folk',
      artist: language === 'zh' ? '张三和' : 'Zhang Sanhe',
      artistAvatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'demo' as any,
      price: 0,
      currency: ''
    },
    {
      id: 4,
      title: language === 'zh' ? '星空漫步 (Demo)' : 'Starry Walk (Demo)',
      description: language === 'zh' ? '轻音乐Demo片段' : 'Light music demo snippet',
      coverUrl: 'https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 956,
      collaborators: [language === 'zh' ? '赵六' : 'Zhao Liu'],
      likes: 328,
      comments: 67,
      duration: '01:45',
      genre: language === 'zh' ? '轻音乐' : 'Light Music',
      artist: language === 'zh' ? '赵六' : 'Zhao Liu',
      artistAvatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'demo' as any,
      price: 0,
      currency: ''
    },
    {
      id: 11,
      title: language === 'zh' ? '黎明序曲 (Demo)' : 'Dawn Prelude (Demo)',
      description: language === 'zh' ? '古典音乐的现代演绎试听片段' : 'Modern interpretation of classical music preview',
      coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 1678,
      collaborators: [language === 'zh' ? '莫扎特小弟' : 'Mozart Jr'],
      likes: 567,
      comments: 112,
      duration: '03:15',
      genre: language === 'zh' ? '古典' : 'Classical',
      artist: language === 'zh' ? '莫扎特小弟' : 'Mozart Jr',
      artistAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'demo' as any,
      price: 0,
      currency: ''
    },
    {
      id: 12,
      title: language === 'zh' ? '街头说唱 (Demo)' : 'Street Rap (Demo)',
      description: language === 'zh' ? '原创说唱Demo，展现街头文化' : 'Original rap demo showcasing street culture',
      coverUrl: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 2134,
      collaborators: [language === 'zh' ? 'MC小白' : 'MC Xiao Bai', language === 'zh' ? 'Beat王' : 'Beat Wang'],
      likes: 789,
      comments: 145,
      duration: '02:48',
      genre: language === 'zh' ? '说唱' : 'Rap',
      artist: language === 'zh' ? 'MC小白' : 'MC Xiao Bai',
      artistAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'demo' as any,
      price: 0,
      currency: ''
    },
    {
      id: 13,
      title: language === 'zh' ? '午后时光 (Demo)' : 'Afternoon Time (Demo)',
      description: language === 'zh' ? '慵懒的午后时光，轻快的节拍' : 'Lazy afternoon with light beats',
      coverUrl: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 892,
      collaborators: [language === 'zh' ? '小清新' : 'Xiao Qingxin'],
      likes: 234,
      comments: 45,
      duration: '01:52',
      genre: language === 'zh' ? '清新' : 'Fresh',
      artist: language === 'zh' ? '小清新' : 'Xiao Qingxin',
      artistAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'demo' as any,
      price: 0,
      currency: ''
    },
    {
      id: 14,
      title: language === 'zh' ? '机械之心 (Demo)' : 'Mechanical Heart (Demo)',
      description: language === 'zh' ? '工业风电子乐Demo，机械感十足' : 'Industrial electronic demo with mechanical feel',
      coverUrl: 'https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 1445,
      collaborators: [language === 'zh' ? '机器人' : 'Robot', language === 'zh' ? '工业党' : 'Industrial Party'],
      likes: 478,
      comments: 89,
      duration: '02:34',
      genre: language === 'zh' ? '工业' : 'Industrial',
      artist: language === 'zh' ? '机器人' : 'Robot',
      artistAvatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'demo' as any,
      price: 0,
      currency: ''
    },
    {
      id: 15,
      title: language === 'zh' ? '海浪声声 (Demo)' : 'Ocean Waves (Demo)',
      description: language === 'zh' ? '大自然声音与音乐的融合' : 'Fusion of natural sounds and music',
      coverUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      playCount: 1123,
      collaborators: [language === 'zh' ? '自然音' : 'Natural Sound'],
      likes: 356,
      comments: 67,
      duration: '03:21',
      genre: language === 'zh' ? '自然' : 'Nature',
      artist: language === 'zh' ? '自然音' : 'Natural Sound',
      artistAvatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      type: 'demo' as any,
      price: 0,
      currency: ''
    }
  ], [language]);

  useEffect(() => {
    loadWorks();
  }, [publishedWorks, language]);

  const loadWorks = async () => {
    setLoading(true);
    try {
      const response = await ApiService.getWorks(searchTerm);
      const allWorks = [...publishedWorks, ...response];
      setWorks(allWorks);
    } catch (error) {
      console.error('Failed to load works:', error);
      const allWorks = [...publishedWorks, ...mockFullVersionWorks, ...mockDemoWorks];
      setWorks(allWorks);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    setLoading(true);
    await loadWorks();
  };

  const toggleCategory = (categoryKey: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryKey)
        ? prev.filter(key => key !== categoryKey)
        : [...prev, categoryKey]
    );
  };

  const getCategoryLabel = (category: CategoryItem) => {
    return language === 'zh' ? category.label : category.labelEn;
  };

  const getFilteredWorks = () => {
    let filteredWorks = works;

    // Filter by search term
    if (searchTerm) {
      filteredWorks = filteredWorks.filter(work => 
        work.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        work.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (work.artist && work.artist.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory.startsWith('full-version')) {
      filteredWorks = [...mockFullVersionWorks];
    } else if (selectedCategory.startsWith('demo')) {
      filteredWorks = [...mockDemoWorks];
    }

    // Apply subcategory filtering
    if (selectedCategory.includes('top')) {
      filteredWorks = filteredWorks.sort((a, b) => (b.playCount || 0) - (a.playCount || 0));
    } else if (selectedCategory.includes('hot')) {
      filteredWorks = filteredWorks.sort((a, b) => (b.likes || 0) - (a.likes || 0));
    } else if (selectedCategory.includes('style')) {
      filteredWorks = filteredWorks.sort((a, b) => (a.genre || '').localeCompare(b.genre || ''));
    } else if (selectedCategory.includes('creator')) {
      filteredWorks = filteredWorks.sort((a, b) => (a.artist || '').localeCompare(b.artist || ''));
    }

    return filteredWorks;
  };

  const filteredWorks = getFilteredWorks();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            {language === 'zh' ? '音乐作品' : 'Music Works'}
          </h1>
          <p className="text-lg text-gray-600">
            {language === 'zh' ? '发现和购买优质的音乐作品' : 'Discover and purchase quality music works'}
          </p>
        </div>

        <div className="flex gap-8">
          {/* Left Sidebar - Search and Categories */}
          <div className="w-80 flex-shrink-0">
            {/* Search */}
            <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder={language === 'zh' ? '搜索音乐作品...' : 'Search music works...'}
                  className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>

            {/* Categories */}
            <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">
                {language === 'zh' ? '分类' : 'Categories'}
              </h3>
              <div className="space-y-2">
                {categories.map(category => (
                  <div key={category.key}>
                    {/* Main Category */}
                    <button
                      onClick={() => toggleCategory(category.key)}
                      className="w-full flex items-center justify-between px-4 py-3 rounded-lg text-sm font-medium transition-colors bg-gray-50 text-gray-700 hover:bg-gray-100"
                    >
                      <span>{getCategoryLabel(category)}</span>
                      {expandedCategories.includes(category.key) ? (
                        <ChevronDownIcon className="h-4 w-4" />
                      ) : (
                        <ChevronRightIcon className="h-4 w-4" />
                      )}
                    </button>
                    
                    {/* Subcategories */}
                    {expandedCategories.includes(category.key) && category.children && (
                      <div className="ml-4 mt-2 space-y-1">
                        {category.children.map(child => (
                          <button
                            key={child.key}
                            onClick={() => setSelectedCategory(child.key)}
                            className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                              selectedCategory === child.key
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            {getCategoryLabel(child)}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Settings */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <button className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <AdjustmentsHorizontalIcon className="h-5 w-5" />
                <span>{language === 'zh' ? '高级筛选' : 'Advanced Filter'}</span>
              </button>
            </div>
          </div>

          {/* Main Content - Works Grid */}
          <div className="flex-1">
            {/* Category Title */}
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                {(() => {
                  const category = categories.find(cat => 
                    cat.children?.some(child => child.key === selectedCategory)
                  );
                  const subcategory = category?.children?.find(child => child.key === selectedCategory);
                  if (category && subcategory) {
                    return `${getCategoryLabel(category)} - ${getCategoryLabel(subcategory)}`;
                  }
                  return language === 'zh' ? '所有作品' : 'All Works';
                })()}
              </h2>
              <p className="text-gray-600">
                {language === 'zh' ? `找到 ${filteredWorks.length} 个作品` : `Found ${filteredWorks.length} works`}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredWorks.map(work => (
                <div 
                  key={work.id} 
                  className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                  onClick={() => navigate(`/works/${work.id}`)}
                >
                  {/* Cover Image */}
                  <div className="relative h-64 bg-gradient-to-br from-purple-400 to-pink-400 overflow-hidden">
                    {work.coverUrl ? (
                      <img 
                        src={work.coverUrl} 
                        alt={work.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <PlayIcon className="h-20 w-20 text-white" />
                      </div>
                    )}
                    
                    {/* Play Button Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                      <button className="opacity-0 group-hover:opacity-100 transform scale-75 group-hover:scale-100 transition-all duration-300">
                        <div className="bg-white rounded-full p-4 shadow-lg">
                          <PlayIcon className="h-8 w-8 text-blue-600" />
                        </div>
                      </button>
                    </div>

                    {/* Duration */}
                    <div className="absolute bottom-4 right-4 bg-black bg-opacity-60 text-white px-3 py-1 rounded-full text-sm">
                      {work.duration}
                    </div>

                    {/* Genre Tag */}
                    <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {work.genre}
                    </div>

                    {/* Type Badge */}
                    <div className="absolute top-4 right-4">
                      {(work as any).type === 'demo' ? (
                        <span className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                          Demo
                        </span>
                      ) : (
                        <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                          {language === 'zh' ? '完整版' : 'Full'}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    {/* Title and Artist */}
                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-gray-900 mb-2 truncate">{work.title}</h3>
                      <div className="flex items-center space-x-2">
                        <img 
                          src={work.artistAvatar}
                          alt={work.artist}
                          className="w-6 h-6 rounded-full object-cover"
                        />
                        <span className="text-gray-600 text-sm">{work.artist}</span>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
                      {work.description}
                    </p>

                    {/* Price */}
                    {(work as any).type === 'full-version' && (work as any).price && (
                      <div className="mb-4">
                        <span className="text-lg font-bold text-blue-600">
                          {(work as any).currency} {(work as any).price}
                        </span>
                      </div>
                    )}

                    {/* Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <EyeIcon className="h-4 w-4" />
                          <span>{((work.playCount || 0) / 1000).toFixed(1)}k</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <HeartIcon className="h-4 w-4" />
                          <span>{work.likes || 0}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <ChatBubbleLeftIcon className="h-4 w-4" />
                          <span>{work.comments || 0}</span>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      {(work as any).type === 'demo' ? (
                        <button 
                          className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors font-medium flex items-center justify-center gap-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            alert(language === 'zh' ? `试听 ${work.title}` : `Preview ${work.title}`);
                          }}
                        >
                          <PlayIcon className="h-5 w-5" />
                          {language === 'zh' ? '试听' : 'Preview'}
                        </button>
                      ) : (
                        <>
                          <button 
                            className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center justify-center gap-2"
                            onClick={(e) => {
                              e.stopPropagation();
                              alert(language === 'zh' ? `购买 ${work.title}` : `Buy ${work.title}`);
                            }}
                          >
                            {language === 'zh' ? '购买' : 'Buy'}
                          </button>
                          <button 
                            className="px-4 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                            onClick={(e) => {
                              e.stopPropagation();
                              alert(language === 'zh' ? `试听 ${work.title}` : `Preview ${work.title}`);
                            }}
                          >
                            <PlayIcon className="h-5 w-5" />
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              
              {/* Empty State */}
              {filteredWorks.length === 0 && (
                <div className="col-span-full text-center py-16">
                  <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
                    <PlayIcon className="h-full w-full" />
                  </div>
                  <h3 className="text-xl font-medium text-gray-900 mb-2">
                    {language === 'zh' ? '没有找到作品' : 'No works found'}
                  </h3>
                  <p className="text-gray-500">
                    {language === 'zh' ? '尝试调整搜索条件或分类' : 'Try adjusting search terms or categories'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorksPage;