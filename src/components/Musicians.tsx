import React, { useState, useEffect, useMemo } from 'react';
import { UserIcon, PlayIcon, HeartIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import ApiService from '../services/api';

interface Musician {
  id: number;
  name: string;
  avatar?: string;
  genre: string;
  followers: number;
  works: number;
  rating: number;
  location?: string;
  description?: string;
  verified?: boolean;
  tags?: string[];
}

const Musicians: React.FC = () => {
  const [musicians, setMusicians] = useState<Musician[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const { t, language } = useLanguage();
  const navigate = useNavigate();

  const categories = useMemo(() => [
    { value: 'all', label: language === 'en' ? 'All' : '全部' },
    { value: 'singers', label: language === 'en' ? 'Singers' : '歌手' },
    { value: 'lyrists', label: language === 'en' ? 'Lyrists' : '作词人' },
    { value: 'composers', label: language === 'en' ? 'Composers' : '作曲家' },
    { value: 'producers', label: language === 'en' ? 'Producers' : '制作人' },
    { value: 'sound-engineers', label: language === 'en' ? 'Sound Engineers' : '音响工程师' },
    { value: 'video-producers', label: language === 'en' ? 'Video Producers' : '视频制作人' },
    { value: 'beat-boxers', label: language === 'en' ? 'Beat Boxers' : '节拍盒子' },
    { value: 'rappers', label: language === 'en' ? 'Rappers' : '说唱歌手' }
  ], [language]);

  const mockMusicians = useMemo(() => [
    // Singers
    { 
      id: 1, 
      name: 'Emma Chen', 
      genre: 'singers', 
      followers: 22800, 
      works: 18, 
      rating: 4.2,
      location: 'Beijing',
      description: 'Pop vocalist with classical training',
      verified: true,
      tags: ['Pop', 'Classical', 'Creative'],
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    { 
      id: 2, 
      name: 'Jay Wang', 
      genre: 'singers', 
      followers: 45900, 
      works: 15, 
      rating: 4.7,
      location: 'Guangzhou',
      description: 'Independent singer-songwriter',
      verified: true,
      tags: ['Pop', 'Independent', 'Creative'],
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    
    // Composers
    { 
      id: 3, 
      name: 'Alex Liu', 
      genre: 'composers', 
      followers: 25400, 
      works: 25, 
      rating: 4.4,
      location: 'Shanghai',
      description: 'Electronic music composer and arranger',
      verified: true,
      tags: ['Electronic', 'Production', 'Arrangement'],
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    { 
      id: 4, 
      name: 'Sarah Kim', 
      genre: 'composers', 
      followers: 18700, 
      works: 31, 
      rating: 4.6,
      location: 'Seoul',
      description: 'Film score and orchestral composer',
      verified: true,
      tags: ['Orchestral', 'Film Score', 'Classical'],
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    
    // Lyrists
    { 
      id: 5, 
      name: 'Maya Zhang', 
      genre: 'lyrists', 
      followers: 18500, 
      works: 42, 
      rating: 4.9,
      location: 'Chengdu',
      description: 'Award-winning lyricist specializing in emotional storytelling',
      verified: true,
      tags: ['Lyrics', 'Literature', 'Emotional'],
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    { 
      id: 6, 
      name: 'David Chen', 
      genre: 'lyrists', 
      followers: 14200, 
      works: 38, 
      rating: 4.5,
      location: 'Taipei',
      description: 'Bilingual lyricist for pop and R&B',
      verified: false,
      tags: ['Pop Lyrics', 'R&B', 'Bilingual'],
      avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    
    // Producers
    { 
      id: 7, 
      name: 'Mike Johnson', 
      genre: 'producers', 
      followers: 35600, 
      works: 67, 
      rating: 4.8,
      location: 'Los Angeles',
      description: 'Grammy-nominated hip-hop and pop producer',
      verified: true,
      tags: ['Hip-Hop', 'Pop Production', 'Mixing'],
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    { 
      id: 8, 
      name: 'Lisa Park', 
      genre: 'producers', 
      followers: 28900, 
      works: 45, 
      rating: 4.6,
      location: 'Tokyo',
      description: 'Electronic and EDM producer',
      verified: true,
      tags: ['EDM', 'Electronic', 'Synthesizer'],
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    
    // Sound Engineers
    { 
      id: 9, 
      name: 'Tom Wilson', 
      genre: 'sound-engineers', 
      followers: 12400, 
      works: 89, 
      rating: 4.7,
      location: 'Nashville',
      description: 'Professional mixing and mastering engineer',
      verified: true,
      tags: ['Mixing', 'Mastering', 'Studio Engineering'],
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    
    // Video Producers
    { 
      id: 10, 
      name: 'Anna Rodriguez', 
      genre: 'video-producers', 
      followers: 16800, 
      works: 23, 
      rating: 4.5,
      location: 'Barcelona',
      description: 'Music video director and producer',
      verified: false,
      tags: ['Music Videos', 'Cinematography', 'Editing'],
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    
    // Beat Boxers
    { 
      id: 11, 
      name: 'Kevin Brown', 
      genre: 'beat-boxers', 
      followers: 8900, 
      works: 12, 
      rating: 4.3,
      location: 'New York',
      description: 'World championship beat boxer',
      verified: false,
      tags: ['Beatboxing', 'Vocal Percussion', 'Hip-Hop'],
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    
    // Rappers
    { 
      id: 12, 
      name: 'MC Flow', 
      genre: 'rappers', 
      followers: 42300, 
      works: 28, 
      rating: 4.6,
      location: 'Atlanta',
      description: 'Underground rap artist and freestyle champion',
      verified: true,
      tags: ['Rap', 'Freestyle', 'Underground'],
      avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    { 
      id: 13, 
      name: 'Ruby Li', 
      genre: 'rappers', 
      followers: 31700, 
      works: 19, 
      rating: 4.4,
      location: 'Hong Kong',
      description: 'Bilingual female rapper and songwriter',
      verified: true,
      tags: ['Rap', 'Bilingual', 'Songwriter'],
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    }
  ], []);

  useEffect(() => {
    const fetchMusicians = async () => {
      setLoading(true);
      try {
        const data = await ApiService.getMusicians();
        setMusicians(data);
      } catch (error) {
        console.error('Failed to fetch musicians:', error);
        // Enhanced mock data for demonstration - 减少数据量提高加载速度
        setMusicians(mockMusicians);
      } finally {
        setLoading(false);
      }
    };

    fetchMusicians();
  }, []); // Only run once on mount

  // Update musicians data when language changes
  useEffect(() => {
    // If we already have data and it's likely mock data, update it with new translations
    if (musicians.length > 0 && musicians[0].id <= 6) { // Our mock data has IDs 1-6
      setMusicians(mockMusicians);
    }
  }, [mockMusicians, musicians]);

  const filteredMusicians = musicians.filter(musician => {
    const matchesSearch = musician.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         musician.genre.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (musician.description && musician.description.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || musician.genre === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {language === 'en' ? 'Loading creators...' : '正在加载创作者...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            {language === 'en' ? 'Creators' : '创作者'}
          </h1>
          <p className="text-lg text-gray-600">
            {language === 'en' ? 'Discover talented creators for your music projects' : '发现有才华的创作者参与您的音乐项目'}
          </p>
        </div>

        <div className="flex gap-8">
          {/* Left Sidebar - Search and Categories */}
          <div className="w-80 flex-shrink-0">
            {/* Search */}
            <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder={language === 'en' ? 'Search creators...' : '搜索创作者...'}
                  className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Categories */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">
                {language === 'en' ? 'Categories' : '分类'}
              </h3>
              <div className="space-y-2">
                {categories.map(category => (
                  <button
                    key={category.value}
                    onClick={() => setSelectedCategory(category.value)}
                    className={`w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category.value
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {category.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content - Musicians Grid */}
          <div className="flex-1">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMusicians.map((musician) => (
            <div 
              key={musician.id} 
              className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer flex flex-col"
              onClick={() => navigate(`/musicians/${musician.id}`)}
            >
              <div className="relative h-64 bg-gradient-to-br from-purple-400 to-pink-400 overflow-hidden">
                {musician.avatar ? (
                  <img 
                    src={musician.avatar} 
                    alt={musician.name} 
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300" 
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <UserIcon className="h-20 w-20 text-white" />
                  </div>
                )}
                
                {/* Tags */}
                <div className="absolute top-4 left-4 flex flex-wrap gap-2">
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    musician.verified ? 'bg-blue-600 text-white' : 'bg-black bg-opacity-60 text-white'
                  }`}>
                    {musician.genre}
                  </div>
                  {musician.verified && (
                    <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      {t('musicians.verified')}
                    </div>
                  )}
                </div>

                {/* Rating */}
                <div className="absolute top-4 right-4 bg-black bg-opacity-60 backdrop-blur-sm rounded-full px-3 py-1 flex items-center space-x-1">
                  <StarIconSolid className="h-4 w-4 text-yellow-400" />
                  <span className="text-white text-sm font-medium">{musician.rating}</span>
                </div>
              </div>

              <div className="p-6 flex flex-col flex-grow">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-1">{musician.name}</h3>
                    {musician.location && (
                      <p className="text-sm text-gray-500">{musician.location}</p>
                    )}
                  </div>
                </div>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
                  {musician.description}
                </p>

                {/* Tags */}
                {musician.tags && (
                  <div className="flex flex-wrap gap-2 mb-4">
                    {musician.tags.slice(0, 3).map((tag, index) => (
                      <span 
                        key={index}
                        className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full font-medium"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}

                {/* Stats */}
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <HeartIcon className="h-4 w-4" />
                      <span>{(musician.followers / 1000).toFixed(1)}k {t('musicians.followers')}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <PlayIcon className="h-4 w-4" />
                      <span>{musician.works} {t('musicians.works')}</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 mt-auto">
                  <button 
                    className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-full hover:bg-blue-700 transition-colors font-medium"
                    onClick={(e) => {
                      e.stopPropagation();
                      alert(`${language === 'en' ? 'Followed' : '已关注'} ${musician.name}！`);
                    }}
                  >
                    {language === 'en' ? 'Follow' : '关注'}
                  </button>
                  <button 
                    className="px-4 py-3 border border-gray-200 text-gray-600 rounded-full hover:bg-gray-50 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate('/chat');
                    }}
                  >
                    {language === 'en' ? 'Message' : '私信'}
                  </button>
                </div>
              </div>
            </div>
          ))}
          
          {filteredMusicians.length === 0 && (
            <div className="col-span-full text-center py-16">
              <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
                <UserIcon className="h-full w-full" />
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">{t('musicians.no.results')}</h3>
              <p className="text-gray-500">{t('musicians.no.results.desc')}</p>
            </div>
          )}
          </div>
        </div>
      </div>
    </div>
  </div>
  );
};

export default Musicians;