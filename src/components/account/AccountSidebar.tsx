import React from 'react';
import { 
  UserIcon, 
  HomeIcon, 
  ChatBubbleLeftIcon, 
  SparklesIcon, 
  GiftIcon, 
  CreditCardIcon,
  CheckBadgeIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

interface AccountSidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const AccountSidebar: React.FC<AccountSidebarProps> = ({ activeTab, onTabChange }) => {
  const { t } = useLanguage();

  // 添加滚动条样式
  const scrollbarStyle = `
    .account-sidebar-scroll::-webkit-scrollbar {
      width: 6px;
    }
    .account-sidebar-scroll::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;
    }
    .account-sidebar-scroll::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;
    }
    .account-sidebar-scroll::-webkit-scrollbar-thumb:hover {
      background: #94a3b8;
    }
  `;
  
  const menuItems = [
    {
      id: 'homepage',
      name: t('account.homepage'),
      icon: HomeIcon,
      description: t('account.homepage.desc')
    },
    {
      id: 'info',
      name: t('account.info'),
      icon: UserIcon,
      description: t('account.info.desc')
    },
    {
      id: 'profile',
      name: t('account.profile'),
      icon: UserIcon,
      description: t('account.profile.desc')
    },
    {
      id: 'messages',
      name: t('account.messages'),
      icon: ChatBubbleLeftIcon,
      description: t('account.messages.desc')
    },
    {
      id: 'creative',
      name: t('account.creative'),
      icon: SparklesIcon,
      description: t('account.creative.desc')
    },
    {
      id: 'rewards',
      name: t('account.rewards'),
      icon: GiftIcon,
      description: t('account.rewards.desc')
    },
    {
      id: 'wallet',
      name: t('account.wallet'),
      icon: CreditCardIcon,
      description: t('account.wallet.desc')
    },
    {
      id: 'certification',
      name: t('account.certification'),
      icon: CheckBadgeIcon,
      description: t('account.certification.desc')
    }
  ];

  return (
    <>
      <style>{scrollbarStyle}</style>
      <div className="fixed left-0 top-16 w-64 bg-white shadow-lg border-r border-gray-200 z-40 overflow-hidden" style={{ height: 'calc(100vh - 4rem)' }}>
        <div className="account-sidebar-scroll h-full overflow-y-auto" style={{ 
          scrollbarWidth: 'thin', 
          scrollbarColor: '#9CA3AF #F3F4F6' 
        }}>
          <div className="p-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('account.title')}</h2>
            
            <nav className="space-y-2 pb-8">
            {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => onTabChange(item.id)}
                className={`w-full flex items-center px-3 py-3 rounded-lg text-left transition-colors ${
                  isActive
                    ? 'bg-indigo-50 text-indigo-700 border-l-4 border-indigo-500'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600'
                }`}
              >
                <Icon className={`h-5 w-5 mr-3 ${isActive ? 'text-indigo-500' : 'text-gray-400'}`} />
                <div className="flex-1">
                  <div className="font-medium">{item.name}</div>
                  <div className="text-xs text-gray-500">{item.description}</div>
                </div>
              </button>
            );
            })}
          </nav>
          </div>
        </div>
      </div>
    </>
  );
};

export default AccountSidebar;