import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, UserRole, USER_ROLE_PERMISSIONS } from '../types/user';

interface UserContextType {
  user: User | null;
  setUser: (user: User | null) => void;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  hasRole: (role: UserRole) => boolean;
  hasPermission: (permission: keyof import('../types/user').UserRolePermissions) => boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const { default: ApiService } = await import('../services/api');
      const response = await ApiService.login({ email, password });
      
      setUser(response.user);
      localStorage.setItem('user', JSON.stringify(response.user));
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      const { default: ApiService } = await import('../services/api');
      await ApiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      localStorage.removeItem('user');
    }
  };

  const hasRole = (role: UserRole) => {
    return user?.role === role;
  };

  const hasPermission = (permission: keyof import('../types/user').UserRolePermissions) => {
    if (!user) return false;
    return USER_ROLE_PERMISSIONS[user.role][permission];
  };

  const isAuthenticated = !!user;

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
        login,
        logout,
        isAuthenticated,
        hasRole,
        hasPermission,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};