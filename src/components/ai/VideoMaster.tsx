import React, { useState } from 'react';
import { VideoCameraIcon, PhotoIcon, ArrowPathIcon, PlayIcon } from '@heroicons/react/24/outline';

const VideoMaster: React.FC = () => {
  const [lyricsInput, setLyricsInput] = useState('');
  const [selectedModel, setSelectedModel] = useState('standard');
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [isGeneratingMV, setIsGeneratingMV] = useState(false);
  const [generatedMV, setGeneratedMV] = useState<string | null>(null);

  const aiModels = [
    { id: 'standard', name: '标准模型', description: '适合大多数场景的通用模型' },
    { id: 'artistic', name: '艺术模型', description: '擅长生成艺术风格的图像' },
    { id: 'realistic', name: '写实模型', description: '生成逼真的人物和场景' },
    { id: 'anime', name: '动漫模型', description: '专门生成动漫风格的图像' },
  ];

  const handleGenerateImage = () => {
    if (!lyricsInput.trim()) return;
    
    setIsGeneratingImage(true);
    // Simulate image generation
    setTimeout(() => {
      const newImages = [
        'https://picsum.photos/400/300?random=1',
        'https://picsum.photos/400/300?random=2',
        'https://picsum.photos/400/300?random=3',
        'https://picsum.photos/400/300?random=4',
      ];
      setGeneratedImages(newImages);
      setIsGeneratingImage(false);
    }, 3000);
  };

  const handleRegenerateImage = () => {
    setIsGeneratingImage(true);
    setTimeout(() => {
      const newImages = [
        'https://picsum.photos/400/300?random=5',
        'https://picsum.photos/400/300?random=6',
        'https://picsum.photos/400/300?random=7',
        'https://picsum.photos/400/300?random=8',
      ];
      setGeneratedImages(newImages);
      setIsGeneratingImage(false);
    }, 3000);
  };

  const handleGenerateMV = () => {
    if (generatedImages.length === 0) return;
    
    setIsGeneratingMV(true);
    setTimeout(() => {
      setGeneratedMV('https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4');
      setIsGeneratingMV(false);
    }, 5000);
  };

  return (
    <div className="space-y-6">
      {/* Top Section: Lyrics Input and Model Selection */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center gap-3 mb-6">
          <VideoCameraIcon className="w-8 h-8 text-purple-600" />
          <h2 className="text-2xl font-bold text-gray-900">影像大师</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Lyrics Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              歌词输入
            </label>
            <textarea
              value={lyricsInput}
              onChange={(e) => setLyricsInput(e.target.value)}
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              placeholder="输入歌词内容，AI将根据歌词生成相应的视觉内容..."
            />
          </div>

          {/* Model Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              AI模型选择
            </label>
            <div className="space-y-3">
              {aiModels.map((model) => (
                <div key={model.id} className="flex items-start">
                  <input
                    type="radio"
                    id={model.id}
                    name="aiModel"
                    value={model.id}
                    checked={selectedModel === model.id}
                    onChange={(e) => setSelectedModel(e.target.value)}
                    className="mt-1 mr-3 text-purple-600 focus:ring-purple-500"
                  />
                  <div className="flex-1">
                    <label htmlFor={model.id} className="text-sm font-medium text-gray-900 cursor-pointer">
                      {model.name}
                    </label>
                    <p className="text-xs text-gray-500 mt-1">{model.description}</p>
                  </div>
                </div>
              ))}
            </div>
            
            <button
              onClick={handleGenerateImage}
              disabled={!lyricsInput.trim() || isGeneratingImage}
              className="w-full mt-4 flex items-center justify-center gap-2 px-4 py-3 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <PhotoIcon className="w-5 h-5" />
              {isGeneratingImage ? '生成中...' : '生成图片'}
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Section: Generated Content */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900">AI生成内容</h3>
          <div className="flex gap-3">
            <button
              onClick={handleRegenerateImage}
              disabled={generatedImages.length === 0 || isGeneratingImage}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ArrowPathIcon className="w-4 h-4" />
              重新生成图片
            </button>
            <button
              onClick={handleGenerateMV}
              disabled={generatedImages.length === 0 || isGeneratingMV}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <VideoCameraIcon className="w-4 h-4" />
              {isGeneratingMV ? '生成MV中...' : '生成MV'}
            </button>
          </div>
        </div>

        {/* Image Display Grid */}
        <div className="mb-6">
          <h4 className="text-lg font-medium text-gray-800 mb-3">生成的图片</h4>
          {isGeneratingImage ? (
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="aspect-video bg-gray-200 rounded-lg animate-pulse flex items-center justify-center">
                  <PhotoIcon className="w-12 h-12 text-gray-400" />
                </div>
              ))}
            </div>
          ) : generatedImages.length > 0 ? (
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              {generatedImages.map((image, index) => (
                <div key={index} className="aspect-video bg-gray-100 rounded-lg overflow-hidden group cursor-pointer hover:shadow-lg transition-shadow">
                  <img
                    src={image}
                    alt={`Generated image ${index + 1}`}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="aspect-video bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
              <div className="text-center">
                <PhotoIcon className="w-16 h-16 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">输入歌词并选择模型来生成图片</p>
              </div>
            </div>
          )}
        </div>

        {/* MV Display */}
        <div>
          <h4 className="text-lg font-medium text-gray-800 mb-3">生成的MV</h4>
          {isGeneratingMV ? (
            <div className="aspect-video bg-gray-200 rounded-lg animate-pulse flex items-center justify-center">
              <div className="text-center">
                <VideoCameraIcon className="w-16 h-16 text-gray-400 mx-auto mb-2 animate-spin" />
                <p className="text-gray-600">正在生成MV...</p>
              </div>
            </div>
          ) : generatedMV ? (
            <div className="aspect-video bg-black rounded-lg overflow-hidden">
              <video
                src={generatedMV}
                controls
                className="w-full h-full"
                poster="https://picsum.photos/800/450?random=9"
              >
                Your browser does not support the video tag.
              </video>
            </div>
          ) : (
            <div className="aspect-video bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
              <div className="text-center">
                <VideoCameraIcon className="w-16 h-16 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">先生成图片，然后点击"生成MV"按钮</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoMaster;