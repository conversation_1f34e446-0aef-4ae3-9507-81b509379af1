import React from 'react';
import { PlayIcon, PauseIcon, StopIcon } from '@heroicons/react/24/solid';

interface TransportProps {
  isPlaying: boolean;
  isRecording: boolean;
  tempo: number;
  currentTime: number;
  onPlay: () => void;
  onStop: () => void;
  onRecord: () => void;
  onTempoChange: (tempo: number) => void;
}

const Transport: React.FC<TransportProps> = ({
  isPlaying,
  isRecording,
  tempo,
  currentTime,
  onPlay,
  onStop,
  onRecord,
  onTempoChange
}) => {
  const formatTime = (seconds: number) => {
    if (!isFinite(seconds) || seconds < 0) {
      return '00:00';
    }
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="transport-container">
      <div className="transport-controls">
        <button
          className={`transport-button ${isRecording ? 'active' : ''}`}
          onClick={onRecord}
          title="Record"
        >
          <div className="w-4 h-4 bg-red-500 rounded-full"></div>
        </button>
        
        <button
          className="transport-button"
          onClick={onStop}
          title="Stop"
        >
          <StopIcon className="w-5 h-5" />
        </button>
        
        <button
          className={`transport-button ${isPlaying ? 'active' : ''}`}
          onClick={onPlay}
          title={isPlaying ? 'Pause' : 'Play'}
        >
          {isPlaying ? (
            <PauseIcon className="w-5 h-5" />
          ) : (
            <PlayIcon className="w-5 h-5" />
          )}
        </button>
      </div>
      
      <div className="transport-info">
        <div className="time-display">
          {formatTime(currentTime)}
        </div>
        
        <div className="tempo-section">
          <label htmlFor="tempo">BPM:</label>
          <input
            id="tempo"
            type="number"
            min="60"
            max="200"
            value={tempo}
            onChange={(e) => onTempoChange(parseInt(e.target.value))}
            className="tempo-input"
          />
        </div>
      </div>
    </div>
  );
};

export default Transport;