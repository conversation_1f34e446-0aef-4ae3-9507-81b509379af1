import React, { useState, useEffect, useMemo } from 'react';
import { dawApiService, Project } from '../../services/dawApi';
import { useUser } from '../../contexts/UserContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { PlusIcon, FolderIcon, ClockIcon, UsersIcon } from '@heroicons/react/24/outline';

interface ProjectManagerProps {
  onProjectSelect: (project: Project) => void;
  onClose: () => void;
}

const ProjectManager: React.FC<ProjectManagerProps> = ({ onProjectSelect, onClose }) => {
  const { user } = useUser();
  const { t } = useLanguage();
  // 使用useMemo创建effectiveUser避免每次渲染重新创建对象
  const effectiveUser = useMemo(() => {
    return user || {
      id: 'demo-user',
      name: 'Demo User',
      email: '<EMAIL>',
      role: 'musician' as const,
      verified: true,
      createdAt: new Date('2024-01-01').toISOString(),
      updatedAt: new Date('2024-01-01').toISOString()
    };
  }, [user]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [templates, setTemplates] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'recent' | 'templates' | 'shared'>('recent');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');

  useEffect(() => {
    initializeAndLoadData();
  }, [effectiveUser]);

  const initializeAndLoadData = async () => {
    // 初始化DAW API服务连接检查
    await dawApiService.initialize();
    
    // 加载项目和模板
    await Promise.all([loadProjects(), loadTemplates()]);
  };

  const loadProjects = async () => {
    if (!effectiveUser) return;
    
    try {
      setLoading(true);
      const userProjects = await dawApiService.getProjects(effectiveUser.id);
      
      // 如果没有项目，创建一些示例项目
      if (userProjects.length === 0) {
        console.log('No projects found, creating demo projects...');
        const demoProjects = [
          {
            _id: `demo_project_${Date.now()}_1`,
            name: t('daw.demo.project1'),
            userId: effectiveUser.id,
            tempo: 120,
            timeSignature: 4,
            tracks: [],
            clips: [],
            collaborators: [],
            visibility: 'private' as const,
            lastModified: new Date(),
          },
          {
            _id: `demo_project_${Date.now()}_2`,
            name: t('daw.demo.project2'),
            userId: effectiveUser.id,
            tempo: 128,
            timeSignature: 4,
            tracks: [],
            clips: [],
            collaborators: [],
            visibility: 'private' as const,
            lastModified: new Date(Date.now() - 86400000), // 1天前
          }
        ];
        
        // 保存示例项目到localStorage
        const existingProjects = JSON.parse(localStorage.getItem('dawProjects') || '[]');
        const allProjects = [...existingProjects, ...demoProjects];
        localStorage.setItem('dawProjects', JSON.stringify(allProjects));
        
        setProjects(demoProjects);
      } else {
        setProjects(userProjects);
      }
    } catch (error) {
      console.error('Failed to load projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      const projectTemplates = await dawApiService.getProjectTemplates();
      setTemplates(projectTemplates);
    } catch (error) {
      console.error('Failed to load templates:', error);
    }
  };

  const createProject = async (name: string, template?: Project) => {
    if (!effectiveUser) return;

    try {
      const projectData: Partial<Project> = {
        name,
        userId: effectiveUser.id,
        tempo: template?.tempo || 120,
        timeSignature: template?.timeSignature || 4,
        tracks: template?.tracks || [],
        clips: template?.clips || [],
        collaborators: [],
        visibility: 'private'
      };

      console.log('创建项目开始，当前项目数量:', projects.length);
      const newProject = await dawApiService.createProject(projectData);
      console.log('项目创建成功:', newProject);
      
      const updatedProjects = [newProject, ...projects];
      console.log('更新后项目数量:', updatedProjects.length);
      setProjects(updatedProjects);
      
      // 验证localStorage中的数据
      const storageProjects = JSON.parse(localStorage.getItem('dawProjects') || '[]');
      console.log('localStorage中的项目数量:', storageProjects.length);
      
      onProjectSelect(newProject);
      setShowCreateModal(false);
      setNewProjectName('');
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const ProjectCard: React.FC<{ project: Project; isTemplate?: boolean }> = ({ project, isTemplate = false }) => (
    <div
      className="project-card bg-gray-800 border border-gray-700 rounded-lg p-4 hover:bg-gray-750 cursor-pointer transition-colors"
      onClick={() => {
        if (isTemplate) {
          createProject(`${t('daw.new.project')} from ${project.name}`, project);
        } else {
          onProjectSelect(project);
        }
      }}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <FolderIcon className="w-5 h-5 text-blue-400" />
          <h3 className="font-medium text-white">{project.name}</h3>
        </div>
        {project.collaborators && project.collaborators.length > 0 && (
          <div className="flex items-center gap-1 text-xs text-gray-400">
            <UsersIcon className="w-3 h-3" />
            {project.collaborators.length}
          </div>
        )}
      </div>
      
      <div className="text-sm text-gray-400 mb-2">
        <div>Tempo: {project.tempo} BPM</div>
        <div>Tracks: {project.tracks?.length || 0}</div>
        {project.lastModified && (
          <div className="flex items-center gap-1 mt-1">
            <ClockIcon className="w-3 h-3" />
            {formatDate(project.lastModified)}
          </div>
        )}
      </div>
      
      <div className="flex items-center justify-between">
        <span className={`text-xs px-2 py-1 rounded ${
          project.visibility === 'public' ? 'bg-green-600' :
          project.visibility === 'collaborative' ? 'bg-blue-600' : 'bg-gray-600'
        }`}>
          {project.visibility}
        </span>
        {isTemplate && (
          <span className="text-xs text-blue-400">{t('daw.use.template')}</span>
        )}
      </div>
    </div>
  );

  const CreateProjectModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 w-96">
        <h3 className="text-lg font-bold text-white mb-4">{t('daw.create.new')}</h3>
        
        <div className="mb-4">
          <label className="block text-sm text-gray-400 mb-2">{t('daw.project.name')}</label>
          <input
            type="text"
            value={newProjectName}
            onChange={(e) => setNewProjectName(e.target.value)}
            className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            placeholder={t('daw.name.placeholder')}
            autoFocus
          />
        </div>
        
        <div className="flex justify-end gap-2">
          <button
            onClick={() => {
              setShowCreateModal(false);
              setNewProjectName('');
            }}
            className="px-4 py-2 text-gray-400 hover:text-white"
          >
            {t('daw.cancel')}
          </button>
          <button
            onClick={() => createProject(newProjectName)}
            disabled={!newProjectName.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {t('daw.create')}
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="daw-container bg-gray-900">
      <div className="daw-header">
        <h1 className="daw-title">{t('daw.project.manager')}</h1>
        <button
          onClick={onClose}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          {t('daw.close')}
        </button>
      </div>
      
      <div className="flex-1 p-6">
        {/* Tabs */}
        <div className="flex gap-4 mb-6 border-b border-gray-700">
          {[
            { id: 'recent', label: t('daw.tabs.recent') },
            { id: 'templates', label: t('daw.tabs.templates') },
            { id: 'shared', label: t('daw.tabs.shared') }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`pb-2 px-1 border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-400 text-blue-400'
                  : 'border-transparent text-gray-400 hover:text-white'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Create New Project Button */}
        {activeTab === 'recent' && (
          <div className="mb-6">
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              <PlusIcon className="w-5 h-5" />
              {t('daw.new.project')}
            </button>
          </div>
        )}

        {/* Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {loading ? (
            <div className="col-span-full text-center text-gray-400 py-8">
              {t('daw.loading.projects')}
            </div>
          ) : (
            <>
              {activeTab === 'recent' && (
                <>
                  {projects.length === 0 ? (
                    <div className="col-span-full text-center text-gray-400 py-8">
                      {t('daw.no.projects')}
                    </div>
                  ) : (
                    projects.map(project => (
                      <ProjectCard key={project._id} project={project} />
                    ))
                  )}
                </>
              )}
              
              {activeTab === 'templates' && (
                <>
                  {templates.map((template, index) => (
                    <ProjectCard key={index} project={template} isTemplate />
                  ))}
                </>
              )}
              
              {activeTab === 'shared' && (
                <div className="col-span-full text-center text-gray-400 py-8">
                  {t('daw.no.shared')}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {showCreateModal && <CreateProjectModal />}
    </div>
  );
};

export default ProjectManager;