import React, { useState } from 'react';
import { 
  UserIcon, 
  CheckBadgeIcon, 
  LinkIcon, 
  ShieldCheckIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

const PersonalProfile: React.FC = () => {
  const [activeSection, setActiveSection] = useState('basic');
  const { t } = useLanguage();

  const sections = [
    { id: 'basic', name: t('profile.basic.info'), icon: UserIcon },
    { id: 'certification', name: t('profile.certification'), icon: CheckBadgeIcon },
    { id: 'binding', name: t('profile.binding'), icon: LinkIcon },
    { id: 'security', name: t('profile.security'), icon: ShieldCheckIcon },
    { id: 'preferences', name: t('profile.preferences'), icon: CogIcon }
  ];

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-6">
        <div className="relative">
          <img 
            src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=120&fit=crop&crop=face" 
            alt={t('myhomepage.avatar.alt')} 
            className="w-30 h-30 rounded-full"
          />
          <button className="absolute bottom-0 right-0 w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white hover:bg-indigo-700">
            <CogIcon className="h-4 w-4" />
          </button>
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold text-gray-900">{t('profile.demo.user')}</h2>
          <p className="text-gray-600">@demo_user</p>
          <div className="flex items-center space-x-4 mt-4">
            <div className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm">
              {t('profile.normal.user')}
            </div>
            <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
              {t('profile.verified')}
            </div>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('profile.real.name')}</label>
          <input 
            type="text" 
            value="张三"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('profile.gender')}</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
            <option>{t('profile.male')}</option>
            <option>{t('profile.female')}</option>
            <option>{t('profile.secret')}</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('profile.birth.date')}</label>
          <input 
            type="date" 
            value="1990-01-01"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('profile.location')}</label>
          <input 
            type="text" 
            value="北京市"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{t('profile.bio')}</label>
        <textarea 
          rows={4}
          value={t('profile.bio.demo')}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{t('profile.music.style')}</label>
        <div className="flex flex-wrap gap-2">
          {['pop', 'classical', 'rock', 'jazz', 'electronic', 'folk', 'hiphop', 'country'].map((style) => (
            <button
              key={style}
              className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm hover:bg-indigo-200"
            >
              {t(`profile.style.${style}`)}
            </button>
          ))}
        </div>
      </div>
      
      <div className="flex justify-end">
        <button className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
          {t('profile.save.changes')}
        </button>
      </div>
    </div>
  );

  const renderCertification = () => (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-semibold text-blue-800">{t('profile.cert.description')}</h4>
        <p className="text-blue-700 mt-1">{t('profile.cert.benefit')}</p>
      </div>
      
      <div className="space-y-4">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">{t('profile.real.name.cert')}</h3>
            <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
              {t('profile.certified')}
            </span>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>{t('profile.real.name')}：张三</p>
            <p>身份证号：110101********1234</p>
            <p>{t('profile.cert.time')}</p>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">{t('profile.musician.cert')}</h3>
            <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
              {t('profile.apply.cert')}
            </button>
          </div>
          <div className="text-sm text-gray-600">
            <p>{t('profile.musician.cert.desc')}</p>
            <p className="mt-2">{t('profile.musician.cert.requires')}</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>{t('profile.musician.works')}</li>
              <li>{t('profile.musician.id')}</li>
              <li>{t('profile.musician.certificates')}</li>
            </ul>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">{t('profile.company.cert')}</h3>
            <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
              {t('profile.apply.cert')}
            </button>
          </div>
          <div className="text-sm text-gray-600">
            <p>{t('profile.company.cert.desc')}</p>
            <p className="mt-2">{t('profile.company.requires')}</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>{t('profile.business.license')}</li>
              <li>{t('profile.company.intro')}</li>
              <li>{t('profile.legal.person.id')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderBinding = () => (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 className="font-semibold text-yellow-800">{t('profile.binding.tip.title')}</h4>
        <p className="text-yellow-700 mt-1">{t('profile.binding.tip.desc')}</p>
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">微</span>
            </div>
            <div>
              <h4 className="font-semibold">{t('profile.wechat')}</h4>
              <p className="text-gray-600 text-sm">{t('profile.wechat.bound')}</p>
            </div>
          </div>
          <button className="px-4 py-2 text-red-600 border border-red-600 rounded-lg hover:bg-red-50">
            {t('profile.unbind')}
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">Q</span>
            </div>
            <div>
              <h4 className="font-semibold">{t('profile.qq')}</h4>
              <p className="text-gray-600 text-sm">{t('profile.not.bound')}</p>
            </div>
          </div>
          <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
            {t('profile.bind')}
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">G</span>
            </div>
            <div>
              <h4 className="font-semibold">{t('profile.github')}</h4>
              <p className="text-gray-600 text-sm">{t('profile.not.bound')}</p>
            </div>
          </div>
          <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
            {t('profile.bind')}
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">微</span>
            </div>
            <div>
              <h4 className="font-semibold">{t('profile.weibo')}</h4>
              <p className="text-gray-600 text-sm">{t('profile.not.bound')}</p>
            </div>
          </div>
          <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
            {t('profile.bind')}
          </button>
        </div>
      </div>
    </div>
  );

  const renderSecurity = () => (
    <div className="space-y-6">
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h4 className="font-semibold text-red-800">{t('profile.security.tip.title')}</h4>
        <p className="text-red-700 mt-1">{t('profile.security.tip.desc')}</p>
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('profile.login.password')}</h4>
            <p className="text-gray-600 text-sm">{t('profile.password.modified')}</p>
          </div>
          <button className="px-4 py-2 text-indigo-600 border border-indigo-600 rounded-lg hover:bg-indigo-50">
            {t('profile.change.password')}
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('profile.phone.number')}</h4>
            <p className="text-gray-600 text-sm">138****8888</p>
          </div>
          <button className="px-4 py-2 text-indigo-600 border border-indigo-600 rounded-lg hover:bg-indigo-50">
            {t('profile.change.phone')}
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('profile.email.address')}</h4>
            <p className="text-gray-600 text-sm"><EMAIL></p>
          </div>
          <button className="px-4 py-2 text-indigo-600 border border-indigo-600 rounded-lg hover:bg-indigo-50">
            {t('profile.change.email')}
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 className="font-semibold">{t('profile.two.factor')}</h4>
            <p className="text-gray-600 text-sm">{t('profile.two.factor.disabled')}</p>
          </div>
          <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
            {t('profile.enable')}
          </button>
        </div>
      </div>
    </div>
  );

  const renderPreferences = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">{t('profile.interface.settings')}</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">{t('profile.dark.mode')}</span>
              <input type="checkbox" className="w-5 h-5 text-indigo-600" />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">{t('profile.font.size')}</span>
              <select className="px-3 py-2 border border-gray-300 rounded-lg">
                <option>{t('profile.font.small')}</option>
                <option selected>{t('profile.font.medium')}</option>
                <option>{t('profile.font.large')}</option>
              </select>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">{t('profile.language')}</span>
              <select className="px-3 py-2 border border-gray-300 rounded-lg">
                <option selected>{t('profile.chinese')}</option>
                <option>{t('profile.english')}</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">{t('profile.music.playback')}</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">{t('profile.auto.play')}</span>
              <input type="checkbox" className="w-5 h-5 text-indigo-600" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">{t('profile.audio.quality')}</span>
              <select className="px-3 py-2 border border-gray-300 rounded-lg">
                <option>{t('profile.quality.standard')}</option>
                <option selected>{t('profile.quality.high')}</option>
                <option>{t('profile.quality.lossless')}</option>
              </select>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">{t('profile.volume')}</span>
              <input type="range" className="w-32" defaultValue="70" />
            </div>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">{t('profile.privacy.settings')}</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">{t('profile.profile.visibility')}</span>
              <select className="px-3 py-2 border border-gray-300 rounded-lg">
                <option selected>{t('profile.visibility.everyone')}</option>
                <option>{t('profile.visibility.friends')}</option>
                <option>{t('profile.visibility.self')}</option>
              </select>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">{t('profile.allow.private.messages')}</span>
              <input type="checkbox" className="w-5 h-5 text-indigo-600" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">{t('profile.show.online.status')}</span>
              <input type="checkbox" className="w-5 h-5 text-indigo-600" defaultChecked />
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end">
        <button className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
          {t('profile.save.settings')}
        </button>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'basic':
        return renderBasicInfo();
      case 'certification':
        return renderCertification();
      case 'binding':
        return renderBinding();
      case 'security':
        return renderSecurity();
      case 'preferences':
        return renderPreferences();
      default:
        return renderBasicInfo();
    }
  };

  return (
    <div className="space-y-6">
      {/* 顶部标签 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex space-x-8">
          {sections.map((section) => {
            const Icon = section.icon;
            const isActive = activeSection === section.id;
            
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  isActive
                    ? 'bg-indigo-50 text-indigo-700'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Icon className={`h-5 w-5 ${isActive ? 'text-indigo-500' : 'text-gray-400'}`} />
                <span className="font-medium">{section.name}</span>
              </button>
            );
          })}
        </div>
      </div>
      
      {/* 内容区域 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {renderContent()}
      </div>
    </div>
  );
};

export default PersonalProfile;