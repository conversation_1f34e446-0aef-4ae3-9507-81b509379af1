import React, { useState, useEffect, useMemo } from 'react';
import { 
  PlusIcon, 
  EyeIcon, 
  ChatBubbleLeftIcon, 
  HeartIcon,
  UserGroupIcon,
  DocumentTextIcon,
  DocumentCheckIcon,
  ClockIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ShareIcon,
  MusicalNoteIcon,
  ChatBubbleLeftRightIcon,
  FolderIcon,
  UserIcon,
  StarIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ArrowDownTrayIcon,
  PaperClipIcon,
  PhotoIcon,
  PlayIcon,
  PauseIcon,
  SpeakerWaveIcon,
  RocketLaunchIcon,
  XMarkIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { useUser } from '../../contexts/UserContext';
import { useWorks } from '../../contexts/WorksContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useNavigate, useLocation } from 'react-router-dom';

interface CollabSpace {
  id: number;
  name: string;
  description: string;
  coverImage: string;
  privacy: 'public' | 'participants' | 'searchable';
  memberCount: number;
  memberAvatars: string[];
  creator: string;
  isCreator: boolean;
  hasSignedContract: boolean;
  recentActivity: string;
  createdTime: string;
  role?: string;
  selectedRoles?: string[];
  roleAssignments?: any;
  revenueShares?: {[key: string]: number};
  invitedMembers?: any[];
}

interface Contract {
  id: number;
  spaceName: string;
  type: 'signed' | 'pending';
  signedDate?: string;
  parties: string[];
  status: string;
}

const AllSpaces: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedSubcategory, setSelectedSubcategory] = useState('all');
  const [expandedCategories, setExpandedCategories] = useState<{ [key: string]: boolean }>({
    recruiting: true,
    inProgress: false,
    completed: false
  });
  const [sortBy, setSortBy] = useState('hot');
  const [mySpacesTab, setMySpacesTab] = useState<'created' | 'joined' | 'signed-contracts' | 'pending-contracts' | 'my-contracts' | 'created-daw' | 'joined-daw' | 'crowdfunding' | 'expanded' | null>(null);
  const [selectedSpace, setSelectedSpace] = useState<CollabSpace | null>(null);
  const [spaceDetailTab, setSpaceDetailTab] = useState<'discussion' | 'activity' | 'media' | 'members' | 'history'>('discussion');
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showDiscussionModal, setShowDiscussionModal] = useState(false);
  const [showRevenueShareModal, setShowRevenueShareModal] = useState(false);
  const [selectedSpaceForApplication, setSelectedSpaceForApplication] = useState<any>(null);
  const [userProposedShares, setUserProposedShares] = useState<{[key: string]: number}>({});
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [applicationMessage, setApplicationMessage] = useState('');
  const [showCrowdfundingDetail, setShowCrowdfundingDetail] = useState(false);
  const [showCrowdfundingManage, setShowCrowdfundingManage] = useState(false);
  const [selectedCrowdfundingProject, setSelectedCrowdfundingProject] = useState<any>(null);
  
  // 申请加入对话框状态
  const [showJoinDialog, setShowJoinDialog] = useState(false);
  const [selectedJoinSpace, setSelectedJoinSpace] = useState<any>(null);
  const [selectedJoinRoles, setSelectedJoinRoles] = useState<string[]>([]);
  const [customShares, setCustomShares] = useState<{ [key: string]: number }>({});
  const [joinMessage, setJoinMessage] = useState('');
  
  const [createdSpaces, setCreatedSpaces] = useState<CollabSpace[]>([]);
  const [isPublishing, setIsPublishing] = useState(false);
  const [isCreatingNFT, setIsCreatingNFT] = useState(false);
  const { isAuthenticated } = useUser();
  const { addPublishedWork, updateWorkNFTByCollabSpaceId } = useWorks();
  const { t, language } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  // Filter categories and subcategories
  const filterCategories = [
    {
      key: 'recruiting',
      label: language === 'zh' ? '招募中' : 'Recruiting',
      subcategories: [
        { key: 'lyrics', label: language === 'zh' ? '找作词' : 'Looking for Lyricist' },
        { key: 'composition', label: language === 'zh' ? '找作曲' : 'Looking for Composer' },
        { key: 'vocalist', label: language === 'zh' ? '找歌手' : 'Looking for Vocalist' },
        { key: 'mixing', label: language === 'zh' ? '找混音' : 'Looking for Mixing' },
        { key: 'production', label: language === 'zh' ? '找制作发行' : 'Looking for Production' }
      ]
    },
    {
      key: 'inProgress',
      label: language === 'zh' ? '进行中' : 'In Progress',
      subcategories: [
        { key: 'lyrics', label: language === 'zh' ? '找作词' : 'Looking for Lyricist' },
        { key: 'composition', label: language === 'zh' ? '找作曲' : 'Looking for Composer' },
        { key: 'vocalist', label: language === 'zh' ? '找歌手' : 'Looking for Vocalist' },
        { key: 'mixing', label: language === 'zh' ? '找混音' : 'Looking for Mixing' },
        { key: 'production', label: language === 'zh' ? '找制作发行' : 'Looking for Production' }
      ]
    },
    {
      key: 'completed',
      label: language === 'zh' ? '已完成' : 'Completed',
      subcategories: [
        { key: 'lyrics', label: language === 'zh' ? '找作词' : 'Looking for Lyricist' },
        { key: 'composition', label: language === 'zh' ? '找作曲' : 'Looking for Composer' },
        { key: 'vocalist', label: language === 'zh' ? '找歌手' : 'Looking for Vocalist' },
        { key: 'mixing', label: language === 'zh' ? '找混音' : 'Looking for Mixing' },
        { key: 'production', label: language === 'zh' ? '找制作发行' : 'Looking for Production' }
      ]
    }
  ];

  const categories = [
    { key: 'all', label: t('collab.categories.all'), icon: '🎵' },
    { key: 'lyrics', label: t('collab.categories.lyrics'), icon: '✍️' },
    { key: 'composition', label: t('collab.categories.composition'), icon: '🎼' },
    { key: 'singer', label: t('collab.categories.singer'), icon: '🎤' },
    { key: 'mixing', label: t('collab.categories.mixing'), icon: '🎚️' },
    { key: 'production', label: t('collab.categories.production'), icon: '🎧' }
  ];

  // 根据选中空间的角色生成可用角色（空缺角色）
  const getAvailableRoles = (space: any) => {
    if (!space) return [];
    
    // 定义所有可能的角色（与创建空间时的选项保持一致）
    const allPossibleRoles = ['作词', '作曲', '编曲', '演唱', '混音', '制作', '其他'];
    
    let selectedRoles: string[] = [];
    
    // If this is a created space, use the original space data for roles and revenue shares
    if ((space as any).originalSpaceData) {
      const originalData = (space as any).originalSpaceData;
      selectedRoles = originalData.selectedRoles || [];
    } else {
      // For existing spaces, use space.roles or default roles
      selectedRoles = space.roles || ['作词', '作曲', '编曲', '演唱', '混音', '制作'];
    }
    
    // 返回空缺角色（未被选择的角色，即真正的空缺）
    // 创建空间时已勾选的角色表示已有安排，未勾选的角色才是空缺
    const vacantRoles = allPossibleRoles
      .filter((role: string) => {
        // 只返回未被选择的角色（真正的空缺角色）
        return !selectedRoles.includes(role);
      })
      .map((role: string) => ({
        id: role.toLowerCase().replace(/\s+/g, '_'),
        name: role,
        defaultShare: 10 // 为空缺角色分配默认收益分成
      }));
    
    return vacantRoles;
  };

  // 角色类型定义
  interface RoleType {
    id: string;
    name: string;
    defaultShare: number;
  }

  const toggleCategory = (categoryKey: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryKey]: !prev[categoryKey]
    }));
  };

  const handleCreateSpace = () => {
    if (!isAuthenticated) {
      navigate('/login', { state: { message: t('collab.login.required') } });
      return;
    }
    navigate('/collaboration/create');
  };

  // 删除协创空间
  const handleDeleteSpace = (spaceId: number, spaceName: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止卡片点击事件
    
    const confirmMessage = language === 'zh' 
      ? `确定要删除协创空间"${spaceName}"吗？此操作不可恢复。` 
      : `Are you sure you want to delete collaboration space "${spaceName}"? This action cannot be undone.`;
    
    if (window.confirm(confirmMessage)) {
      // 从 localStorage 中删除
      const savedSpaces = JSON.parse(localStorage.getItem('myCreatedSpaces') || '[]');
      const updatedSpaces = savedSpaces.filter((space: any) => space.id !== spaceId);
      localStorage.setItem('myCreatedSpaces', JSON.stringify(updatedSpaces));
      
      // 更新 state 中的创建空间列表
      setCreatedSpaces(prevSpaces => prevSpaces.filter(space => space.id !== spaceId));
      
      const successMessage = language === 'zh' 
        ? '协创空间已成功删除' 
        : 'Collaboration space deleted successfully';
      alert(successMessage);
    }
  };


  // 处理新创建的协创空间
  useEffect(() => {
    // 从 localStorage 加载我创建的协创空间
    const savedSpaces = JSON.parse(localStorage.getItem('myCreatedSpaces') || '[]');
    const convertedSpaces: CollabSpace[] = savedSpaces.map((space: any) => ({
      id: space.id,
      name: space.spaceName,
      description: space.description,
      coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=300&fit=crop',
      privacy: space.privacy,
      memberCount: 1 + (space.invitedMembers?.length || 0),
      memberAvatars: [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
      ],
      creator: space.creator,
      isCreator: true,
      hasSignedContract: false,
      recentActivity: t('collab.activity.created'),
      createdTime: new Date(space.createdAt).toISOString().split('T')[0],
      // 保留角色相关数据
      selectedRoles: space.selectedRoles || [],
      roleAssignments: space.roleAssignments || {},
      revenueShares: space.revenueShares || {},
      invitedMembers: space.invitedMembers || []
    }));
    
    setCreatedSpaces(convertedSpaces);
    
    // 检查是否有新创建的空间数据传递过来
    if (location.state?.showNewSpace && location.state?.spaceData) {
      const newSpaceData = location.state.spaceData;
      const newSpace: CollabSpace = {
        id: newSpaceData.id,
        name: newSpaceData.spaceName,
        description: newSpaceData.description,
        coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=300&fit=crop',
        privacy: newSpaceData.privacy,
        memberCount: 1 + (newSpaceData.invitedMembers?.length || 0),
        memberAvatars: [
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
        ],
        creator: newSpaceData.creator,
        isCreator: true,
        hasSignedContract: false,
        recentActivity: t('collab.activity.created'),
        createdTime: new Date().toISOString().split('T')[0]
      };
      
      // 显示新创建的空间详情，并设置为显示成员tab
      setSelectedSpace(newSpace);
      setMySpacesTab('created');
      setSpaceDetailTab('members');
      
      // 清理 location state 避免重复处理
      navigate('/collaboration', { replace: true });
    }
  }, [location.state, navigate, t]);

  // 移除自动展开逻辑，保持默认显示全部协创空间

  const joinedSpaces: CollabSpace[] = useMemo(() => [
    {
      id: 3,
      name: t('collab.space.electronic.title'),
      description: t('collab.space.electronic.desc'),
      coverImage: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=600&h=300&fit=crop',
      privacy: 'public',
      memberCount: 6,
      memberAvatars: [
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
      ],
      creator: t('collab.creator.dj.alex'),
      isCreator: false,
      hasSignedContract: true,
      recentActivity: `3${t('collab.time.hours.ago')}${t('collab.activity.discussion')}`,
      createdTime: "2024-01-08",
      role: t('collab.role.arrangement')
    },
    {
      id: 4,
      name: t('collab.space.ancient.title'),
      description: t('collab.space.ancient.desc'),
      coverImage: 'https://images.unsplash.com/photo-1507838153414-b4b713384a76?w=600&h=300&fit=crop',
      privacy: 'participants',
      memberCount: 5,
      memberAvatars: [
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face'
      ],
      creator: t('collab.creator.ancient'),
      isCreator: false,
      hasSignedContract: false,
      recentActivity: `1${t('collab.time.days.ago')}${t('collab.activity.participated')}`,
      createdTime: "2024-01-05",
      role: t('collab.role.lyrics')
    }
  ], [t]);

  const contracts: Contract[] = useMemo(() => [
    {
      id: 1,
      spaceName: t('collab.space.electronic.title'),
      type: 'signed',
      signedDate: "2024-01-20",
      parties: [t('collab.creator.dj.alex'), t('collab.creator.wang'), t('collab.member.arranger.li'), t('collab.member.mixer.zhang')],
      status: t('collab.contract.status.active')
    },
    {
      id: 2,
      spaceName: t('collab.space.folk.title'),
      type: 'signed',
      signedDate: "2024-01-18",
      parties: [t('collab.creator.wang'), t('collab.member.guitarist.chen'), t('collab.member.singer.liu')],
      status: t('collab.contract.status.active')
    },
    {
      id: 3,
      spaceName: t('collab.space.ancient.title'),
      type: 'pending',
      parties: [t('collab.creator.ancient'), t('collab.creator.wang'), t('collab.member.lyricist.zhao')],
      status: t('collab.contract.status.pending')
    }
  ], [t]);

  const myCreatedContracts = [
    {
      id: 1,
      contractTitle: t('collab.contract.title.dreams'),
      spaceName: t('collab.space.pop.title'),
      createdDate: "2024-01-25",
      status: t('collab.contract.status.deployed'),
      blockchainHash: "0x1234567890abcdef...",
      parties: [t('collab.creator.wang'), t('collab.member.lyricist.li'), t('collab.member.singer.zhang'), t('collab.member.mixer.chen')],
      totalRevenue: t('collab.contract.revenue.composition')
    },
    {
      id: 2,
      contractTitle: t('collab.contract.title.folk'),
      spaceName: t('collab.space.folk.title'),
      createdDate: "2024-01-20",
      status: t('collab.contract.status.waiting'),
      blockchainHash: "",
      parties: [t('collab.creator.wang'), t('collab.member.guitarist.chen'), t('collab.member.singer.liu')],
      totalRevenue: t('collab.contract.revenue.folk')
    }
  ];

  // 渲染函数
  const handleSpaceClick = (space: CollabSpace) => {
    setSelectedSpace(space);
    setSpaceDetailTab('discussion');
  };

  const handleInviteMember = () => {
    setShowInviteModal(true);
  };

  const handleShareSpace = () => {
    setShowShareModal(true);
  };

  const handleSignContract = () => {
    navigate('/collaboration/contract');
  };

  const handleOpenDAW = (projectId?: number) => {
    if (projectId) {
      navigate(`/music-studio?projectId=${projectId}`);
    } else {
      navigate('/music-studio');
    }
  };

  const handleCopyrightContract = () => {
    navigate('/collaboration/copyright-contract');
  };

  // 空间成员数据
  const spaceMembers = [
    {
      id: 1,
      name: t('collab.member.producer.wang'),
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      role: t('collab.creator.label'),
      specialties: [t('collab.specialty.composition'), t('collab.specialty.arrangement')],
      joinDate: '2024-01-15',
      isCreator: true,
      contributions: 15,
      lastActive: t('collab.time.2hours.ago')
    },
    {
      id: 2,
      name: t('collab.member.lyricist.li.full'),
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
      role: t('collab.role.lyricist'),
      specialties: [t('collab.specialty.lyrics'), t('collab.specialty.composition')],
      joinDate: '2024-01-16',
      isCreator: false,
      contributions: 8,
      lastActive: t('collab.time.1day.ago')
    },
    {
      id: 3,
      name: t('collab.member.singer.zhang.full'),
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
      role: t('collab.role.vocalist'),
      specialties: [t('collab.specialty.vocals'), t('collab.specialty.harmony')],
      joinDate: '2024-01-17',
      isCreator: false,
      contributions: 12,
      lastActive: t('collab.time.5hours.ago')
    },
    {
      id: 4,
      name: t('collab.member.mixer.chen.full'),
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      role: t('collab.role.mixer'),
      specialties: [t('collab.specialty.mixing'), t('collab.specialty.mastering')],
      joinDate: '2024-01-18',
      isCreator: false,
      contributions: 6,
      lastActive: t('collab.time.3hours.ago')
    }
  ];

  // 历史版本数据
  const versionHistory = [
    {
      id: 1,
      version: 'v3.2',
      title: t('collab.version.title.main.melody'),
      timestamp: '2024-01-25 14:30',
      author: t('collab.member.producer.wang'),
      authorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      changes: [
        t('collab.change.main.recording'),
        t('collab.change.drums.bass'),
        t('collab.change.initial.mixing'),
        t('collab.change.beat.sync.fix')
      ],
      files: [
        { name: t('collab.file.main.melody') + '_v3.2.wav', size: '25.6MB' },
        { name: t('collab.file.drums') + '_v3.2.wav', size: '18.2MB' }
      ]
    },
    {
      id: 2,
      version: 'v3.1',
      title: t('collab.version.title.lyrics.vocals'),
      timestamp: '2024-01-23 16:45',
      author: t('collab.member.lyricist.li.full'),
      authorAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
      changes: [
        t('collab.change.lyrics.complete'),
        t('collab.change.demo.recording'),
        t('collab.change.lyrics.rhythm'),
        t('collab.change.harmony.arrangement')
      ],
      files: [
        { name: t('collab.file.lyrics.final') + '.txt', size: '2.1KB' },
        { name: t('collab.file.demo.vocal') + '_v3.1.mp3', size: '8.9MB' }
      ]
    },
    {
      id: 3,
      version: 'v3.0',
      title: t('collab.version.title.arrangement.framework'),
      timestamp: '2024-01-20 10:15',
      author: t('collab.member.producer.wang'),
      authorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      changes: [
        t('collab.change.song.structure'),
        t('collab.change.chord.progression'),
        t('collab.change.melody.framework'),
        t('collab.change.production.timeline')
      ],
      files: [
        { name: t('collab.file.arrangement.sketch') + '_v3.0.mid', size: '1.2MB' },
        { name: t('collab.file.chord.chart') + '_v3.0.pdf', size: '856KB' }
      ]
    },
    {
      id: 4,
      version: 'v2.5',
      title: t('collab.version.title.concept.style'),
      timestamp: '2024-01-18 19:20',
      author: t('collab.author.team.discussion'),
      authorAvatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=40&h=40&fit=crop&crop=face',
      changes: [
        t('collab.change.pop.rock.style'),
        t('collab.change.theme.direction'),
        t('collab.change.division.plan'),
        t('collab.change.project.milestones')
      ],
      files: [
        { name: t('collab.file.project.plan') + '.pdf', size: '2.3MB' },
        { name: t('collab.file.style.reference') + '.mp3', size: '15.7MB' }
      ]
    }
  ];

  // 讨论数据
  const discussionData = [
    {
      id: 1,
      author: t('allspaces.author.producer.wang'),
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      timestamp: `2${t('allspaces.time.hours.ago')}`,
      content: t('allspaces.discussion.hello'),
      replies: [
        {
          id: 11,
          author: t('allspaces.author.lyricist.li'),
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
          timestamp: `1${t('allspaces.time.hours.ago')}`,
          content: t('allspaces.discussion.demo.good')
        },
        {
          id: 12,
          author: t('allspaces.author.singer.zhang'),
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
          timestamp: `30${t('allspaces.time.minutes.ago')}`,
          content: t('allspaces.discussion.bridge.adjust')
        }
      ],
      likes: 5,
      isLiked: false
    },
    {
      id: 2,
      author: t('allspaces.author.mixer.chen'),
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      timestamp: `1${t('allspaces.time.days.ago')}`,
      content: t('allspaces.discussion.mixing'),
      replies: [
        {
          id: 21,
          author: t('allspaces.author.producer.wang'),
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
          timestamp: `23${t('allspaces.time.hours.ago')}`,
          content: t('allspaces.discussion.mixing.reply')
        }
      ],
      likes: 3,
      isLiked: true
    },
    {
      id: 3,
      author: t('allspaces.author.singer.zhang'),
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
      timestamp: `2${t('allspaces.time.days.ago')}`,
      content: t('allspaces.discussion.recording'),
      replies: [],
      likes: 8,
      isLiked: false
    }
  ];

  // 活动数据
  const activityData = [
    {
      id: 1,
      type: 'file_upload',
      author: t('allspaces.author.producer.wang'),
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      timestamp: `2${t('allspaces.time.hours.ago')}`,
      content: t('allspaces.activity.file.upload'),
      details: t('allspaces.activity.file.size')
    },
    {
      id: 2,
      type: 'comment',
      author: t('allspaces.author.lyricist.li'),
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
      timestamp: `3${t('allspaces.time.hours.ago')}`,
      content: t('allspaces.activity.comment.post'),
      details: t('allspaces.activity.comment.detail')
    },
    {
      id: 3,
      type: 'member_join',
      author: t('allspaces.author.mixer.chen'),
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      timestamp: `1${t('allspaces.time.days.ago')}`,
      content: t('allspaces.activity.member.join'),
      details: t('allspaces.activity.role.mixer')
    },
    {
      id: 4,
      type: 'version_update',
      author: t('allspaces.author.producer.wang'),
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      timestamp: `1${t('allspaces.time.days.ago')}`,
      content: t('allspaces.activity.version.create'),
      details: t('allspaces.activity.version.detail')
    },
    {
      id: 5,
      type: 'milestone',
      author: t('allspaces.system'),
      avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=40&h=40&fit=crop&crop=face',
      timestamp: '2天前',
      content: '项目达成里程碑：编曲完成',
      details: '项目进度：65%'
    }
  ];

  // 影音和文件数据 - 使用国际化
  const mediaData = useMemo(() => ({
    audio: [
      {
        id: 1,
        name: t('allspaces.media.file.name.melody'),
        size: '25.6MB',
        duration: '3:45',
        uploadedBy: t('allspaces.media.file.uploaded.by.producer'),
        uploadTime: `2${t('allspaces.media.time.hours.ago')}`,
        type: 'audio',
        waveform: true
      },
      {
        id: 2,
        name: t('allspaces.media.file.name.vocal'),
        size: '8.9MB',
        duration: '3:42',
        uploadedBy: t('allspaces.media.file.uploaded.by.singer'),
        uploadTime: `1${t('allspaces.media.time.day.ago')}`,
        type: 'audio',
        waveform: true
      },
      {
        id: 3,
        name: t('allspaces.media.file.name.arrangement'),
        size: '1.2MB',
        duration: '3:48',
        uploadedBy: t('allspaces.media.file.uploaded.by.producer'),
        uploadTime: `3${t('allspaces.media.time.days.ago')}`,
        type: 'midi',
        waveform: false
      }
    ],
    documents: [
      {
        id: 4,
        name: t('allspaces.media.file.name.lyrics'),
        size: '2.1KB',
        uploadedBy: t('allspaces.media.file.uploaded.by.lyricist'),
        uploadTime: `1${t('allspaces.media.time.day.ago')}`,
        type: 'document'
      },
      {
        id: 5,
        name: t('allspaces.media.file.name.chords'),
        size: '856KB',
        uploadedBy: t('allspaces.media.file.uploaded.by.producer'),
        uploadTime: `3${t('allspaces.media.time.days.ago')}`,
        type: 'document'
      },
      {
        id: 6,
        name: t('allspaces.media.file.name.project.plan'),
        size: '2.3MB',
        uploadedBy: t('allspaces.media.file.uploaded.by.producer'),
        uploadTime: `5${t('allspaces.media.time.days.ago')}`,
        type: 'document'
      }
    ],
    images: [
      {
        id: 20,
        name: t('allspaces.media.file.name.album.cover'),
        size: '4.2MB',
        uploadedBy: t('allspaces.media.file.uploaded.by.producer'),
        uploadTime: `2${t('allspaces.media.time.days.ago')}`,
        type: 'image',
        thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=200&h=200&fit=crop'
      },
      {
        id: 22,
        name: t('allspaces.media.file.name.sheet.music'),
        size: '1.8MB',
        uploadedBy: t('allspaces.media.file.uploaded.by.lyricist'),
        uploadTime: `4${t('allspaces.media.time.days.ago')}`,
        type: 'image',
        thumbnail: 'https://images.unsplash.com/photo-1507838153414-b4b713384a76?w=200&h=200&fit=crop'
      }
    ]
  }), [t]);

  const handleApplyToJoin = (spaceId: number) => {
    // Find the space data
    const space = allSpacesWithCreated.find(s => s.id === spaceId);
    if (!space) {
      alert('找不到该协作空间');
      return;
    }
    
    // Set up the modal with space data
    setSelectedSpaceForApplication(space);
    
    // Get available vacant roles from space's original data (roles created when space was created)
    let vacantRoles = space.roles || [];
    let initialShares: {[key: string]: number} = {};
    
    // If this is a created space, use the original space data for roles and revenue shares
    if ((space as any).originalSpaceData) {
      vacantRoles = (space as any).originalSpaceData.selectedRoles || [];
      initialShares = (space as any).originalSpaceData.revenueShares || {};
    } else {
      // For existing spaces, use mock data or default roles
      vacantRoles = space.roles || ['作词', '作曲', '编曲', '演唱', '混音', '制作'];
      const defaultSharePerRole = Math.floor(100 / vacantRoles.length);
      
      vacantRoles.forEach((role: string, index: number) => {
        // Distribute shares evenly, with remainder going to first roles
        const share = defaultSharePerRole + (index < (100 % vacantRoles.length) ? 1 : 0);
        initialShares[role] = share;
      });
    }
    
    setUserProposedShares(initialShares);
    setSelectedRoles([]);
    setApplicationMessage('');
    setShowRevenueShareModal(true);
  };

  // 所有协创空间数据
  const allSpacesData = useMemo(() => [
    {
      id: 1,
      name: t('allspaces.data.pop.title'),
      description: t('allspaces.data.pop.desc'),
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=300&fit=crop',
      status: t('allspaces.status.recruiting'),
      members: 4,
      category: 'lyrics',
      creator: t('allspaces.creator.producer.wang'),
      creatorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [t('allspaces.role.lyrics'), t('allspaces.role.vocals')],
      viewCount: 1250,
      likeCount: 89,
      shareCount: 23,
      createdTime: `2${t('allspaces.time.ago.hours')}`
    },
    {
      id: 2,
      name: t('allspaces.data.folk.title'),
      description: t('allspaces.data.folk.desc'),
      image: 'https://images.unsplash.com/photo-1520166012956-add9ba0835cb?w=600&h=300&fit=crop',
      status: t('allspaces.status.ongoing'),
      members: 3,
      category: 'composition',
      creator: t('allspaces.creator.folk.lover'),
      creatorAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [t('allspaces.role.composition'), t('allspaces.role.guitar')],
      viewCount: 876,
      likeCount: 54,
      shareCount: 12,
      createdTime: `5${t('allspaces.time.ago.hours')}`
    },
    {
      id: 3,
      name: t('allspaces.data.electronic.title'),
      description: t('allspaces.data.electronic.desc'),
      image: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=600&h=300&fit=crop',
      status: t('allspaces.status.recruiting'),
      members: 6,
      category: 'mixing',
      creator: t('allspaces.creator.electronic.producer'),
      creatorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [t('allspaces.role.mixing'), t('allspaces.role.production')],
      viewCount: 2340,
      likeCount: 156,
      shareCount: 45,
      createdTime: `1${t('allspaces.time.ago.days')}`
    },
    {
      id: 4,
      name: t('allspaces.data.ancient.title'),
      description: t('allspaces.data.ancient.desc'),
      image: 'https://images.unsplash.com/photo-1507838153414-b4b713384a76?w=600&h=300&fit=crop',
      status: t('allspaces.status.completed'),
      members: 5,
      category: 'lyrics',
      creator: t('allspaces.creator.ancient'),
      creatorAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [t('allspaces.role.lyrics'), t('allspaces.role.arrangement')],
      viewCount: 987,
      likeCount: 72,
      shareCount: 18,
      createdTime: `3${t('allspaces.time.ago.days')}`
    },
    {
      id: 5,
      name: t('allspaces.data.rap.title'),
      description: t('allspaces.data.rap.desc'),
      image: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?w=600&h=300&fit=crop',
      status: t('allspaces.status.recruiting'),
      members: 8,
      category: 'singer',
      creator: t('allspaces.creator.rap.leader'),
      creatorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [t('allspaces.role.rap'), t('allspaces.role.beat.production')],
      viewCount: 1567,
      likeCount: 98,
      shareCount: 34,
      createdTime: `6${t('allspaces.time.ago.hours')}`
    },
    {
      id: 6,
      name: t('allspaces.data.piano.title'),
      description: t('allspaces.data.piano.desc'),
      image: 'https://images.unsplash.com/photo-1520523839897-bd0b52f945a0?w=600&h=300&fit=crop',
      status: t('allspaces.status.ongoing'),
      members: 4,
      category: 'composition',
      creator: t('allspaces.creator.piano.poet'),
      creatorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [t('allspaces.role.piano'), t('allspaces.role.arrangement')],
      viewCount: 2156,
      likeCount: 134,
      shareCount: 67,
      createdTime: `12${t('allspaces.time.ago.hours')}`
    },
    {
      id: 17,
      name: t('allspaces.data.rock.title'),
      description: t('allspaces.data.rock.desc'),
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=300&fit=crop',
      status: t('allspaces.status.recruiting'),
      members: 5,
      category: 'singer',
      creator: t('allspaces.creator.rock.veteran'),
      creatorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [t('allspaces.role.lead.vocals'), t('allspaces.role.guitar'), t('allspaces.role.bass')],
      viewCount: 1890,
      likeCount: 112,
      shareCount: 28,
      createdTime: `8${t('allspaces.time.ago.hours')}`
    },
    {
      id: 21,
      name: t('allspaces.data.jazz.title'),
      description: t('allspaces.data.jazz.desc'),
      image: 'https://images.unsplash.com/photo-1511192336575-5a79af67a629?w=600&h=300&fit=crop',
      status: t('allspaces.status.completed'),
      members: 6,
      category: 'production',
      creator: t('allspaces.creator.jazz.master'),
      creatorAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [t('allspaces.role.jazz.piano'), t('allspaces.role.saxophone')],
      viewCount: 1456,
      likeCount: 87,
      shareCount: 32,
      createdTime: `2${t('allspaces.time.ago.days')}`
    },
    // 为子分类添加更多测试数据
    {
      id: 18,
      name: language === 'zh' ? '摇滚乐队寻找主唱' : 'Rock Band Looking for Vocalist',
      description: language === 'zh' ? '我们是一支成立两年的摇滚乐队，正在寻找有实力的主唱加入，希望能够一起创作和演出' : 'We are a two-year-old rock band looking for a talented vocalist to join us for creation and performance',
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=300&fit=crop',
      status: language === 'zh' ? '招募中' : 'Recruiting',
      members: 3,
      category: 'vocalist',
      creator: language === 'zh' ? '摇滚吉他手' : 'Rock Guitarist',
      creatorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [language === 'zh' ? '主唱' : 'Vocalist', language === 'zh' ? '和声' : 'Backing Vocals'],
      viewCount: 890,
      likeCount: 45,
      shareCount: 15,
      createdTime: `1${language === 'zh' ? '天前' : ' day ago'}`
    },
    {
      id: 23,
      name: language === 'zh' ? '寻找专业混音师' : 'Looking for Professional Mixing Engineer',
      description: language === 'zh' ? '我们有一首完成的歌曲，需要专业的混音师来完善音质，欢迎有经验的朋友加入' : 'We have a completed song that needs professional mixing to perfect the sound quality. Experienced friends are welcome',
      image: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=600&h=300&fit=crop',
      status: language === 'zh' ? '进行中' : 'In Progress',
      members: 2,
      category: 'mixing',
      creator: language === 'zh' ? '独立制作人' : 'Independent Producer',
      creatorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [language === 'zh' ? '混音' : 'Mixing', language === 'zh' ? '母带处理' : 'Mastering'],
      viewCount: 654,
      likeCount: 32,
      shareCount: 8,
      createdTime: `3${language === 'zh' ? '天前' : ' days ago'}`
    },
    {
      id: 9,
      name: language === 'zh' ? '完成的爵士专辑项目' : 'Completed Jazz Album Project',
      description: language === 'zh' ? '这是我们完成的爵士专辑项目，历时6个月，收录了10首原创作品' : 'This is our completed jazz album project that took 6 months and includes 10 original compositions',
      image: 'https://images.unsplash.com/photo-1511192336575-5a79af67a629?w=600&h=300&fit=crop',
      status: language === 'zh' ? '已完成' : 'Completed',
      members: 7,
      category: 'composition',
      creator: language === 'zh' ? '爵士钢琴家' : 'Jazz Pianist',
      creatorAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
      memberAvatars: [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
      ],
      roles: [language === 'zh' ? '作曲' : 'Composition', language === 'zh' ? '编曲' : 'Arrangement'],
      viewCount: 2100,
      likeCount: 156,
      shareCount: 78,
      createdTime: `1${language === 'zh' ? '周前' : ' week ago'}`
    }
  ], [t, language]);

  // 合并所有空间数据（包括创建的空间）
  const allSpacesWithCreated = useMemo(() => {
    // 将创建的空间转换为allSpacesData格式
    const createdSpacesData = createdSpaces.map(space => ({
      id: space.id + 1000, // 避免ID冲突
      name: space.name,
      description: space.description,
      image: space.coverImage,
      status: t('allspaces.status.recruiting'),
      members: space.memberCount,
      category: 'all', // 创建的空间属于所有类别
      creator: space.creator,
      creatorAvatar: space.memberAvatars[0] || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      memberAvatars: space.memberAvatars,
      roles: space.selectedRoles || [], // 使用创建时设置的角色
      viewCount: 0, // 新创建的空间浏览量为0
      likeCount: 0, // 新创建的空间点赞量为0
      shareCount: 0, // 新创建的空间分享量为0
      createdTime: space.createdTime || t('allspaces.time.just.now'),
      privacy: space.privacy,
      isCreated: true, // 标记为创建的空间
      // 保留完整的创建空间数据，包括角色分配和收益分成
      originalSpaceData: space
    }));
    
    return [...allSpacesData, ...createdSpacesData];
  }, [allSpacesData, createdSpaces, t]);

  // 根据选择的类别筛选空间
  const filteredSpaces = allSpacesWithCreated.filter(space => {
    if (selectedCategory === 'all') return true;
    
    // 如果选择了子分类，按子分类筛选
    if (selectedSubcategory !== 'all') {
      return space.category === selectedSubcategory;
    }
    
    // 如果只选择了主分类，需要根据主分类显示相应的内容
    // 这里我们可以为每个主分类定义对应的状态筛选
    if (selectedCategory === 'recruiting') {
      return space.status === '招募中' || space.status === t('allspaces.status.recruiting');
    } else if (selectedCategory === 'inProgress') {
      return space.status === '进行中' || space.status === t('allspaces.status.ongoing');
    } else if (selectedCategory === 'completed') {
      return space.status === '已完成' || space.status === t('allspaces.status.completed');
    }
    
    return space.category === selectedCategory;
  });

  // 根据排序方式排序
  const allSpaces = [...filteredSpaces].sort((a, b) => {
    if (sortBy === 'hot') {
      return b.members - a.members; // 按成员数排序作为热门度
    } else if (sortBy === 'new') {
      return b.id - a.id; // 按ID排序作为时间排序
    } else if (sortBy === 'members') {
      return b.members - a.members;
    }
    return 0;
  });

  const renderMySpacesList = (spaces: CollabSpace[]) => (
    <div className="space-y-4">
      {spaces.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <UserGroupIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {language === 'zh' ? '还没有创建协创空间' : 'No collaboration spaces created yet'}
          </h3>
          <p className="text-gray-500 mb-4">
            {language === 'zh' ? '开始创建您的第一个协创空间吧' : 'Start by creating your first collaboration space'}
          </p>
          <button
            onClick={handleCreateSpace}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            {language === 'zh' ? '创建协创空间' : 'Create Space'}
          </button>
        </div>
      ) : (
        spaces.map((space) => (
        <div 
          key={space.id} 
          className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
          onClick={() => handleSpaceClick(space)}
        >
          <div className="flex">
            <div className="w-32 h-24 flex-shrink-0">
              <img
                src={space.coverImage}
                alt={space.name}
                className="w-full h-full object-cover rounded-l-lg"
              />
            </div>
            
            <div className="flex-1 p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                  {space.name}
                </h3>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    space.privacy === 'public' ? 'bg-green-100 text-green-800' :
                    space.privacy === 'searchable' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {space.privacy === 'public' ? t('collab.privacy.public') : 
                     space.privacy === 'searchable' ? t('collab.privacy.searchable') : t('collab.privacy.private')}
                  </span>
                  {space.hasSignedContract && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                      {t('collab.contract.signed')}
                    </span>
                  )}
                  {/* 只有在"我创建的协创空间"页面显示删除按钮 */}
                  {mySpacesTab === 'created' && (
                    <button
                      onClick={(e) => handleDeleteSpace(space.id, space.name, e)}
                      className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                      title={language === 'zh' ? '删除协创空间' : 'Delete collaboration space'}
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
              
              <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                {space.description}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <UserGroupIcon className="w-4 h-4" />
                    <span>{space.memberCount} {t('collab.members.count')}</span>
                  </div>
                  {space.role && (
                    <div className="flex items-center gap-1">
                      <UserIcon className="w-4 h-4" />
                      <span>{t('collab.my.role')}：{space.role}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-1">
                    <ClockIcon className="w-4 h-4" />
                    <span>{space.recentActivity}</span>
                  </div>
                </div>
                
                <div className="flex -space-x-2">
                  {space.memberAvatars.slice(0, 3).map((avatar, index) => (
                    <img
                      key={index}
                      src={avatar}
                      alt=""
                      className="w-6 h-6 rounded-full border-2 border-white"
                    />
                  ))}
                  {space.memberCount > 3 && (
                    <div className="w-6 h-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                      <span className="text-xs text-gray-600">+{space.memberCount - 3}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        ))
      )}
    </div>
  );

  const renderContractList = (filterType?: 'signed' | 'pending') => {
    const filteredContracts = filterType 
      ? contracts.filter(c => c.type === filterType)
      : contracts;
    
    return (
      <div className="space-y-4">
        {filteredContracts.map((contract) => (
          <div key={contract.id} className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex justify-between items-start mb-3">
              <h3 className="text-lg font-semibold text-gray-900">
                {contract.spaceName}
              </h3>
              <div className="flex items-center gap-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  contract.type === 'signed' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {contract.type === 'signed' ? '已签署' : '待签署'}
                </span>
                <button 
                  onClick={() => navigate('/collaboration/contract-detail', { state: { contract } })}
                  className="px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  查看详情
                </button>
              </div>
            </div>
            
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <UserGroupIcon className="w-4 h-4" />
                <span>参与方：{contract.parties.join('、')}</span>
              </div>
              {contract.signedDate && (
                <div className="flex items-center gap-2">
                  <DocumentCheckIcon className="w-4 h-4" />
                  <span>签署时间：{contract.signedDate}</span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <ClockIcon className="w-4 h-4" />
                <span>状态：{contract.status}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderMyCreatedContracts = () => (
    <div className="space-y-4">
      {myCreatedContracts.map((contract) => (
        <div key={contract.id} className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                {contract.contractTitle}
              </h3>
              <p className="text-sm text-gray-600">
                {t('allspaces.contract.source.space')}{contract.spaceName}
              </p>
            </div>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              contract.status === t('allspaces.contract.status.deployed') 
                ? 'bg-green-100 text-green-800' 
                : contract.status === t('allspaces.contract.status.pending')
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-gray-100 text-gray-800'
            }`}>
              {contract.status}
            </span>
          </div>
          
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <CalendarIcon className="w-4 h-4" />
              <span>{t('allspaces.contract.created.time')}{contract.createdDate}</span>
            </div>
            
            <div className="flex items-center gap-2">
              <UserGroupIcon className="w-4 h-4" />
              <span>参与方：{contract.parties.join('、')}</span>
            </div>
            
            <div className="flex items-start gap-2">
              <CurrencyDollarIcon className="w-4 h-4 mt-0.5" />
              <span>分成比例：{contract.totalRevenue}</span>
            </div>
            
            {contract.blockchainHash && (
              <div className="flex items-center gap-2">
                <DocumentTextIcon className="w-4 h-4" />
                <span>区块链哈希：{contract.blockchainHash}</span>
              </div>
            )}
          </div>

          <div className="mt-4 flex gap-2">
            <button 
              onClick={() => navigate('/collaboration/contract-detail', { state: { contract } })}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              查看详情
            </button>
            {contract.status === '等待签署' && (
              <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm">
                发送签署邀请
              </button>
            )}
            <button 
              onClick={() => navigate('/collaboration/copyright-contract')}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
            >
              编辑合约
            </button>
          </div>
        </div>
      ))}

      <div className="text-center py-8">
        <button
          onClick={() => navigate('/collaboration/copyright-contract')}
          className="flex items-center gap-2 mx-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon className="w-5 h-5" />
          创建新的版权分成合约
        </button>
      </div>
    </div>
  );

  // DAW项目数据 - 使用国际化
  const getDawProjectsData = useMemo(() => ({
    created: [
      {
        id: 1,
        name: t('allspaces.daw.project.edm'),
        description: t('allspaces.daw.project.edm.desc'),
        coverImage: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=600&h=300&fit=crop',
        status: t('allspaces.daw.status.in.progress'),
        collaborators: 3,
        progress: 65,
        createdTime: "2024-01-20"
      },
      {
        id: 2,
        name: t('allspaces.daw.project.folk'),
        description: t('allspaces.daw.project.folk.desc'),
        coverImage: 'https://images.unsplash.com/photo-1520166012956-add9ba0835cb?w=600&h=300&fit=crop',
        status: t('allspaces.daw.status.recruiting'),
        collaborators: 2,
        progress: 30,
        createdTime: "2024-01-18"
      },
      {
        id: 3,
        name: t('allspaces.daw.project.traditional'),
        description: t('allspaces.daw.project.traditional.desc'),
        coverImage: 'https://images.unsplash.com/photo-1507838153414-b4b713384a76?w=600&h=300&fit=crop',
        status: t('allspaces.daw.status.completed'),
        collaborators: 4,
        progress: 100,
        createdTime: "2024-01-10"
      }
    ],
    joined: [
      {
        id: 4,
        name: t('allspaces.daw.project.rock'),
        description: t('allspaces.daw.project.rock.desc'),
        coverImage: 'https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=600&h=300&fit=crop',
        status: t('allspaces.daw.status.in.progress'),
        collaborators: 5,
        progress: 80,
        createdTime: "2024-01-22"
      },
      {
        id: 5,
        name: t('allspaces.daw.project.hiphop'),
        description: t('allspaces.daw.project.hiphop.desc'),
        coverImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=300&fit=crop',
        status: t('allspaces.daw.status.recruiting'),
        collaborators: 2,
        progress: 45,
        createdTime: "2024-01-25"
      },
      {
        id: 6,
        name: t('allspaces.daw.project.piano'),
        description: t('allspaces.daw.project.piano.desc'),
        coverImage: 'https://images.unsplash.com/photo-1520523839897-bd0b52f945a0?w=600&h=300&fit=crop',
        status: t('allspaces.daw.status.completed'),
        collaborators: 3,
        progress: 100,
        createdTime: "2024-01-15"
      },
      {
        id: 19,
        name: t('allspaces.daw.project.jazz'),
        description: t('allspaces.daw.project.jazz.desc'),
        coverImage: 'https://images.unsplash.com/photo-1511192336575-5a79af67a629?w=600&h=300&fit=crop',
        status: t('allspaces.daw.status.in.progress'),
        collaborators: 4,
        progress: 70,
        createdTime: "2024-01-12"
      }
    ]
  }), [t]);

  const renderDAWProjects = (type: 'created' | 'joined') => {
    const dawProjects = getDawProjectsData[type];

    return (
      <div className="space-y-4">
        {dawProjects.map((project) => (
          <div key={project.id} className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex">
              <div className="w-32 h-24 flex-shrink-0">
                <img
                  src={project.coverImage}
                  alt={project.name}
                  className="w-full h-full object-cover rounded-lg"
                />
              </div>
              
              <div className="flex-1 ml-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {project.name}
                  </h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    project.status === '招募中' ? 'bg-green-100 text-green-800' :
                    project.status === '进行中' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {project.status}
                  </span>
                </div>
                
                <p className="text-sm text-gray-600 mb-3">{project.description}</p>
                
                <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <UserGroupIcon className="w-4 h-4" />
                      <span>{project.collaborators} 协作者</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <ClockIcon className="w-4 h-4" />
                      <span>{project.createdTime}</span>
                    </div>
                  </div>
                </div>
                
                {/* 进度条 */}
                <div className="mb-2">
                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                    <span>项目进度</span>
                    <span>{project.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <button 
                    onClick={() => handleOpenDAW(project.id)}
                    className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
                  >
                    打开DAW
                  </button>
                  {type === 'created' && (
                    <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200 transition-colors">
                      项目设置
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderCrowdfundingContent = () => {
    // Mock crowdfunding data for the user
    const myCrowdfundingProjects = [
      {
        id: 1,
        title: language === 'zh' ? '《梦想的声音》专辑制作' : '"Sounds of Dreams" Album Production',
        description: language === 'zh' ? '打造原创音乐专辑，融合流行与电子元素' : 'Creating original music album combining pop and electronic elements',
        targetAmount: 50000,
        currentAmount: 32500,
        currency: 'CNY',
        progress: 65,
        deadline: '2024-12-31',
        backers: 156,
        status: 'active' as const,
        coverImage: '/api/placeholder/300/200'
      },
      {
        id: 2,
        title: language === 'zh' ? '音乐工作室设备升级' : 'Music Studio Equipment Upgrade',
        description: language === 'zh' ? '购买专业录音设备，提升音乐制作品质' : 'Purchasing professional recording equipment to enhance music production quality',
        targetAmount: 30000,
        currentAmount: 30000,
        currency: 'CNY',
        progress: 100,
        deadline: '2024-10-15',
        backers: 89,
        status: 'completed' as const,
        coverImage: '/api/placeholder/300/200'
      }
    ];

    return (
      <div className="space-y-6">
        {myCrowdfundingProjects.map((project) => (
          <div key={project.id} className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex gap-4">
              <img
                src={project.coverImage}
                alt={project.title}
                className="w-32 h-24 object-cover rounded-lg flex-shrink-0"
              />
              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">{project.title}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    project.status === 'active' ? 'bg-green-100 text-green-800' :
                    project.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {project.status === 'active' ? (language === 'zh' ? '进行中' : 'Active') :
                     project.status === 'completed' ? (language === 'zh' ? '已完成' : 'Completed') :
                     (language === 'zh' ? '草稿' : 'Draft')}
                  </span>
                </div>
                
                <p className="text-gray-600 text-sm mb-3">{project.description}</p>
                
                <div className="flex items-center gap-6 text-sm text-gray-500 mb-3">
                  <div className="flex items-center gap-1">
                    <CurrencyDollarIcon className="w-4 h-4" />
                    <span>{project.currentAmount.toLocaleString()} / {project.targetAmount.toLocaleString()} {project.currency}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <UserGroupIcon className="w-4 h-4" />
                    <span>{project.backers} {language === 'zh' ? '支持者' : 'backers'}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <CalendarIcon className="w-4 h-4" />
                    <span>{language === 'zh' ? '截止到' : 'Until'} {project.deadline}</span>
                  </div>
                </div>
                
                <div className="mb-3">
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">{language === 'zh' ? '进度' : 'Progress'}</span>
                    <span className="font-medium">{project.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${project.progress}%` }}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <button 
                    onClick={() => {
                      setSelectedCrowdfundingProject(project);
                      setShowCrowdfundingDetail(true);
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                  >
                    {language === 'zh' ? '查看详情' : 'View Details'}
                  </button>
                  <button 
                    onClick={() => {
                      setSelectedCrowdfundingProject(project);
                      setShowCrowdfundingManage(true);
                    }}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                  >
                    {language === 'zh' ? '管理项目' : 'Manage Project'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {myCrowdfundingProjects.length === 0 && (
          <div className="text-center py-12">
            <RocketLaunchIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {language === 'zh' ? '还没有众筹项目' : 'No crowdfunding projects yet'}
            </h3>
            <p className="text-gray-500 mb-4">
              {language === 'zh' ? '创建您的第一个众筹项目，获得支持者的资助' : 'Create your first crowdfunding project to get support from backers'}
            </p>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              {language === 'zh' ? '创建众筹项目' : 'Create Project'}
            </button>
          </div>
        )}
      </div>
    );
  };

  // 判断是否是新创建的空间（没有历史数据）
  const isNewSpace = (space: CollabSpace) => {
    const savedSpaces = JSON.parse(localStorage.getItem('myCreatedSpaces') || '[]');
    return savedSpaces.some((savedSpace: any) => savedSpace.id === space.id);
  };

  const renderSpaceDetail = () => {
    if (!selectedSpace) return null;
    
    const isNew = isNewSpace(selectedSpace);

    return (
      <div className="bg-white rounded-lg shadow-sm border">
        {/* 空间封面和基本信息 */}
        <div className="relative h-48 overflow-hidden rounded-t-lg">
          <img
            src={selectedSpace.coverImage}
            alt={selectedSpace.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          <div className="absolute bottom-4 left-4 text-white">
            <h2 className="text-2xl font-bold mb-1">{selectedSpace.name}</h2>
            <p className="text-sm opacity-90">{selectedSpace.description}</p>
          </div>
        </div>

        {/* 空间信息栏 */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-6 text-sm text-gray-600">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                selectedSpace.privacy === 'public' ? 'bg-green-100 text-green-800' :
                selectedSpace.privacy === 'searchable' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {selectedSpace.privacy === 'public' ? '公开' : 
                 selectedSpace.privacy === 'searchable' ? '可搜索' : '私密'}
              </span>
              {!selectedSpace.isCreator && (
                <span>发起人：{selectedSpace.creator}</span>
              )}
              <div className="flex items-center gap-1">
                <UserGroupIcon className="w-4 h-4" />
                <span>{selectedSpace.memberCount} 成员</span>
              </div>
              {/* 成员头像 */}
              <div className="flex -space-x-1">
                {selectedSpace.memberAvatars.map((avatar, index) => (
                  <img
                    key={index}
                    src={avatar}
                    alt=""
                    className="w-6 h-6 rounded-full border-2 border-white"
                  />
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              {selectedSpace.isCreator && (
                <button
                  onClick={handleInviteMember}
                  className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  <PlusIcon className="w-4 h-4" />
                  邀请
                </button>
              )}
              <button
                onClick={handleShareSpace}
                className="flex items-center gap-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
              >
                <ShareIcon className="w-4 h-4" />
                分享
              </button>
            </div>
          </div>
        </div>

        {/* Tab导航和操作按钮 */}
        <div className="px-6 py-4 border-b">
          <div className="flex justify-between items-center">
            <nav className="flex space-x-6">
              {[
                { key: 'discussion', label: t('allspaces.tab.discussion'), icon: ChatBubbleLeftRightIcon },
                { key: 'activity', label: t('allspaces.tab.activity'), icon: ClockIcon },
                { key: 'media', label: t('allspaces.tab.media'), icon: FolderIcon },
                { key: 'members', label: t('allspaces.tab.members'), icon: UserGroupIcon },
                { key: 'history', label: t('allspaces.tab.history'), icon: DocumentTextIcon }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSpaceDetailTab(tab.key as any)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                    spaceDetailTab === tab.key
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span className="text-sm">{tab.label}</span>
                </button>
              ))}
            </nav>

            <div className="flex gap-2">
              <button 
                onClick={handleSignContract}
                className="flex items-center gap-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
              >
                <DocumentCheckIcon className="w-4 h-4" />
                {t('allspaces.button.sign.contract')}
              </button>
              <button 
                onClick={() => handleOpenDAW()}
                className="flex items-center gap-1 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
              >
                <MusicalNoteIcon className="w-4 h-4" />
                DAW
              </button>
              {selectedSpace.isCreator && (
                <button 
                  onClick={handleCopyrightContract}
                  className="flex items-center gap-1 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm"
                >
                  <DocumentTextIcon className="w-4 h-4" />
                  版权分成合约
                </button>
              )}
            </div>
          </div>
          
          {/* 第二行操作按钮 - 申请众筹、发布和NFT */}
          {selectedSpace.isCreator && (
            <div className="flex justify-end gap-2 mt-4">
              <button
                onClick={() => {
                  // 检查是否有创作的作品
                  const hasWorks = selectedSpace && selectedSpace.memberCount > 2; // Mock condition
                  if (hasWorks) {
                    navigate('/crowdfunding/apply');
                  } else {
                    alert(language === 'zh' ? '需要先创作作品才能申请众筹' : 'Need to create works before applying for crowdfunding');
                  }
                }}
                disabled={!selectedSpace || selectedSpace.memberCount <= 2}
                className="flex items-center gap-1 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RocketLaunchIcon className="w-4 h-4" />
                {language === 'zh' ? '申请众筹' : 'Apply for Crowdfunding'}
              </button>
              
              <button
                onClick={async () => {
                  setIsPublishing(true);
                  try {
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    
                    // 创建新的作品对象
                    const workId = `collab_${selectedSpace.id}_${Date.now()}`;
                    const newWork = {
                      id: workId, // 使用协创空间ID和时间戳组合
                      title: selectedSpace.name,
                      description: selectedSpace.description,
                      coverUrl: selectedSpace.coverImage,
                      playCount: 0,
                      collaborators: selectedSpace.memberAvatars.length > 0 ? ['协创成员'] : [],
                      likes: 0,
                      comments: 0,
                      duration: '03:45',
                      genre: '协创作品',
                      artist: selectedSpace.creator,
                      artistAvatar: selectedSpace.memberAvatars[0] || '',
                      isFromCollaboration: true,
                      collaborationSpaceId: selectedSpace.id
                    };
                    
                    // 添加到已发布作品列表
                    addPublishedWork(newWork);
                    
                    alert(`协创作品《${selectedSpace.name}》已成功发布到作品页面！\n\n现在您可以在作品页面查看该作品。`);
                  } catch (error) {
                    alert('发布失败，请稍后重试');
                  } finally {
                    setIsPublishing(false);
                  }
                }}
                disabled={isPublishing}
                className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <PlusIcon className="w-4 h-4" />
                {isPublishing ? t('allspaces.button.publishing') : t('allspaces.button.publish.work')}
              </button>
              
              <button
                onClick={async () => {
                  setIsCreatingNFT(true);
                  try {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    const nftId = `nft_${Date.now()}`;
                    const tokenId = `token_${selectedSpace.id}_${Date.now()}`;
                    
                    // 查找对应的已发布作品并添加NFT信息
                    const newNFT = {
                      id: nftId,
                      tokenId: tokenId,
                      price: 0.05, // ETH
                      currency: 'ETH',
                      isForSale: true,
                      createdAt: new Date().toISOString()
                    };
                    
                    // 更新对应作品的NFT信息
                    updateWorkNFTByCollabSpaceId(selectedSpace.id, newNFT);
                    
                    alert(`NFT创建成功！\n\n作品：${selectedSpace.name}\nNFT ID: ${nftId}\nToken ID: ${tokenId}\n价格: 0.05 ETH\n协创空间ID: ${selectedSpace.id}\n\n该NFT已在作品详情页显示，支持购买功能。请先发布作品，然后在作品详情页查看NFT信息。`);
                  } catch (error) {
                    alert('NFT创建失败，请稍后重试');
                  } finally {
                    setIsCreatingNFT(false);
                  }
                }}
                disabled={isCreatingNFT}
                className="flex items-center gap-1 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ShareIcon className="w-4 h-4" />
                {isCreatingNFT ? '创建中...' : 'NFT'}
              </button>
            </div>
          )}
        </div>

        {/* Tab内容 */}
        <div className="p-6">
          {spaceDetailTab === 'discussion' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">{t('allspaces.discussion.title')}</h3>
                <button 
                  onClick={() => setShowDiscussionModal(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  {t('allspaces.discussion.new')}
                </button>
              </div>
              
              {isNew ? (
                <div className="text-center py-12">
                  <ChatBubbleLeftRightIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">{t('allspaces.discussion.empty.title')}</h4>
                  <p className="text-gray-500 mb-6">{t('allspaces.discussion.empty.desc')}</p>
                  <button 
                    onClick={() => setShowDiscussionModal(true)}
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {t('allspaces.discussion.new')}
                  </button>
                </div>
              ) : (
                <>
                <div className="space-y-6">
                  {discussionData.map((discussion) => (
                    <div key={discussion.id} className="bg-white border rounded-lg p-4">
                    <div className="flex gap-4">
                      <img
                        src={discussion.avatar}
                        alt={discussion.author}
                        className="w-10 h-10 rounded-full flex-shrink-0"
                      />
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium text-gray-900">{discussion.author}</span>
                          <span className="text-sm text-gray-500">{discussion.timestamp}</span>
                        </div>
                        
                        <p className="text-gray-700 mb-3">{discussion.content}</p>
                        
                        <div className="flex items-center gap-4 mb-3">
                          <button 
                            className={`flex items-center gap-1 text-sm transition-colors ${
                              discussion.isLiked ? 'text-red-600' : 'text-gray-500 hover:text-red-600'
                            }`}
                          >
                            <HeartIcon className={`w-4 h-4 ${discussion.isLiked ? 'fill-current' : ''}`} />
                            {discussion.likes}
                          </button>
                          <button className="flex items-center gap-1 text-sm text-gray-500 hover:text-blue-600 transition-colors">
                            <ChatBubbleLeftIcon className="w-4 h-4" />
                            回复
                          </button>
                        </div>
                        
                        {discussion.replies.length > 0 && (
                          <div className="border-l-2 border-gray-200 pl-4 space-y-3">
                            {discussion.replies.map((reply) => (
                              <div key={reply.id} className="flex gap-3">
                                <img
                                  src={reply.avatar}
                                  alt={reply.author}
                                  className="w-8 h-8 rounded-full flex-shrink-0"
                                />
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-1">
                                    <span className="font-medium text-gray-900 text-sm">{reply.author}</span>
                                    <span className="text-xs text-gray-500">{reply.timestamp}</span>
                                  </div>
                                  <p className="text-gray-700 text-sm">{reply.content}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 pt-6 border-t">
                <div className="flex gap-3">
                  <img
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face"
                    alt="当前用户"
                    className="w-10 h-10 rounded-full flex-shrink-0"
                  />
                  <div className="flex-1">
                    <textarea
                      placeholder={t('allspaces.discussion.placeholder')}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      rows={3}
                    ></textarea>
                    <div className="flex justify-between items-center mt-2">
                      <div className="flex items-center gap-2">
                        <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                          <PaperClipIcon className="w-4 h-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                          <PhotoIcon className="w-4 h-4" />
                        </button>
                      </div>
                      <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        发布
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              </>
              )}
            </div>
          )}
          {spaceDetailTab === 'activity' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">{t('allspaces.activity.title')}</h3>
                <span className="text-sm text-gray-500">{t('allspaces.activity.recent')}</span>
              </div>
              
              {isNew ? (
                <div className="text-center py-12">
                  <ClockIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">{t('allspaces.activity.empty.title')}</h4>
                  <p className="text-gray-500">{t('allspaces.activity.empty.desc')}</p>
                </div>
              ) : (
                <>
                  <div className="space-y-4">
                    {activityData.map((activity, index) => (
                      <div key={activity.id} className="relative">
                    {index < activityData.length - 1 && (
                      <div className="absolute left-6 top-12 w-0.5 h-16 bg-gray-200"></div>
                    )}
                    
                    <div className="flex gap-4">
                      <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                        {activity.type === 'file_upload' && <ArrowDownTrayIcon className="w-5 h-5 text-blue-600" />}
                        {activity.type === 'comment' && <ChatBubbleLeftIcon className="w-5 h-5 text-green-600" />}
                        {activity.type === 'member_join' && <UserIcon className="w-5 h-5 text-purple-600" />}
                        {activity.type === 'version_update' && <DocumentTextIcon className="w-5 h-5 text-orange-600" />}
                        {activity.type === 'milestone' && <StarIcon className="w-5 h-5 text-yellow-600" />}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          {activity.author !== '系统' && (
                            <img
                              src={activity.avatar}
                              alt={activity.author}
                              className="w-6 h-6 rounded-full"
                            />
                          )}
                          <span className="font-medium text-gray-900 text-sm">{activity.author}</span>
                          <span className="text-xs text-gray-500">{activity.timestamp}</span>
                        </div>
                        
                        <p className="text-gray-700 text-sm mb-1">{activity.content}</p>
                        {activity.details && (
                          <p className="text-xs text-gray-500">{activity.details}</p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-8 text-center">
                <button className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                  {t('allspaces.activity.view.more')}
                </button>
              </div>
              </>
              )}
            </div>
          )}
          {spaceDetailTab === 'media' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">{t('allspaces.tab.media')}</h3>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                  上传文件
                </button>
              </div>
              
              {isNew ? (
                <div className="text-center py-12">
                  <FolderIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">还没有上传文件</h4>
                  <p className="text-gray-500 mb-6">开始上传音频文件、视频和其他协作素材</p>
                  <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    上传第一个文件
                  </button>
                </div>
              ) : (
                <>
                  {/* 音频文件 */}
                  <div className="mb-8">
                    <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center gap-2">
                      <SpeakerWaveIcon className="w-5 h-5" />
                      音频文件 ({mediaData.audio.length})
                    </h4>
                    <div className="grid gap-4">
                      {mediaData.audio.map((file) => (
                    <div key={file.id} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 flex-1">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <MusicalNoteIcon className="w-6 h-6 text-blue-600" />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <h5 className="font-medium text-gray-900 truncate">{file.name}</h5>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <span>{file.size}</span>
                              <span>{file.duration}</span>
                              <span>上传者：{file.uploadedBy}</span>
                              <span>{file.uploadTime}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <button className="p-2 text-gray-400 hover:text-blue-600 rounded-lg transition-colors">
                            <PlayIcon className="w-5 h-5" />
                          </button>
                          <button className="p-2 text-gray-400 hover:text-blue-600 rounded-lg transition-colors">
                            <ArrowDownTrayIcon className="w-5 h-5" />
                          </button>
                        </div>
                      </div>
                      
                      {file.waveform && (
                        <div className="mt-3 h-16 bg-white rounded border flex items-center justify-center">
                          <div className="text-xs text-gray-400">音频波形预览</div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              
              {/* 文档文件 */}
              <div className="mb-8">
                <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center gap-2">
                  <DocumentTextIcon className="w-5 h-5" />
                  文档文件 ({mediaData.documents.length})
                </h4>
                <div className="grid gap-3">
                  {mediaData.documents.map((file) => (
                    <div key={file.id} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                          <DocumentTextIcon className="w-5 h-5 text-red-600" />
                        </div>
                        <div>
                          <h5 className="font-medium text-gray-900">{file.name}</h5>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span>{file.size}</span>
                            <span>上传者：{file.uploadedBy}</span>
                            <span>{file.uploadTime}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <button className="px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded transition-colors">
                          预览
                        </button>
                        <button className="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded transition-colors">
                          下载
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* 图片文件 */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center gap-2">
                  <PhotoIcon className="w-5 h-5" />
                  图片文件 ({mediaData.images.length})
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {mediaData.images.map((file) => (
                    <div key={file.id} className="bg-gray-50 rounded-lg p-3">
                      <div className="aspect-square bg-gray-200 rounded-lg mb-3 overflow-hidden">
                        <img
                          src={file.thumbnail}
                          alt={file.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <h5 className="font-medium text-gray-900 text-sm truncate">{file.name}</h5>
                      <div className="text-xs text-gray-500 mt-1">
                        <div>{file.size}</div>
                        <div>{file.uploadTime}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
                </>
              )}
            </div>
          )}
          {spaceDetailTab === 'members' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">{t('allspaces.members.title')}</h3>
                <span className="text-sm text-gray-500">{spaceMembers.length} {t('allspaces.members.count')}</span>
              </div>
              
              <div className="grid gap-4">
                {spaceMembers.map((member) => (
                  <div key={member.id} className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="relative">
                          <img
                            src={member.avatar}
                            alt={member.name}
                            className="w-12 h-12 rounded-full"
                          />
                          {member.isCreator && (
                            <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                              <StarIcon className="w-3 h-3 text-white" />
                            </div>
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-gray-900">{member.name}</h4>
                            {member.isCreator && (
                              <span className="px-2 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded-full font-medium">
                                发起人
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <span className="font-medium">角色：</span>
                              <span className="px-2 py-0.5 bg-blue-100 text-blue-800 rounded text-xs">
                                {member.role}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className="font-medium">擅长：</span>
                              <span>{member.specialties.join('、')}</span>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-4 text-xs text-gray-500 mt-2">
                            <span>加入时间：{member.joinDate}</span>
                            <span>贡献：{member.contributions} 次</span>
                            <span>最后活跃：{member.lastActive}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <button className="px-3 py-1.5 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                          查看资料
                        </button>
                        {selectedSpace?.isCreator && !member.isCreator && (
                          <button className="px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                            移除
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {selectedSpace?.isCreator && (
                <div className="mt-6 pt-6 border-t">
                  <button
                    onClick={handleInviteMember}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <PlusIcon className="w-4 h-4" />
                    {t('allspaces.members.invite')}
                  </button>
                </div>
              )}
            </div>
          )}
          {spaceDetailTab === 'history' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">{t('allspaces.tab.history')}</h3>
                <span className="text-sm text-gray-500">{isNew ? '0' : versionHistory.length} {t('allspaces.history.count')}</span>
              </div>
              
              {isNew ? (
                <div className="text-center py-12">
                  <DocumentTextIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">还没有版本记录</h4>
                  <p className="text-gray-500">当作品有更新时，版本历史会显示在这里</p>
                </div>
              ) : (
                <>
                <div className="space-y-6">
                  {versionHistory.map((version, index) => (
                  <div key={version.id} className="relative">
                    {/* 时间轴线条 */}
                    {index < versionHistory.length - 1 && (
                      <div className="absolute left-6 top-16 w-0.5 h-full bg-gray-200"></div>
                    )}
                    
                    <div className="flex gap-4">
                      {/* 时间轴点 */}
                      <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center relative">
                        <img
                          src={version.authorAvatar}
                          alt={version.author}
                          className="w-10 h-10 rounded-full"
                        />
                        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">{version.version.slice(-1)}</span>
                        </div>
                      </div>
                      
                      {/* 版本内容 */}
                      <div className="flex-1 bg-white border rounded-lg p-4 shadow-sm">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-semibold text-gray-900">{version.version}</h4>
                              {index === 0 && (
                                <span className="px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">
                                  最新
                                </span>
                              )}
                            </div>
                            <p className="text-gray-700 mb-2">{version.title}</p>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <span>{version.author}</span>
                              <span>{version.timestamp}</span>
                            </div>
                          </div>
                        </div>
                        
                        {/* 更改内容 */}
                        <div className="mb-4">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">主要更改：</h5>
                          <ul className="space-y-1">
                            {version.changes.map((change, changeIndex) => (
                              <li key={changeIndex} className="text-sm text-gray-600 flex items-start gap-2">
                                <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                                {change}
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        {/* 相关文件 */}
                        <div>
                          <h5 className="text-sm font-medium text-gray-700 mb-2">相关文件：</h5>
                          <div className="space-y-2">
                            {version.files.map((file, fileIndex) => (
                              <div key={fileIndex} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                                <div className="flex items-center gap-3">
                                  <FolderIcon className="w-5 h-5 text-gray-400" />
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                                    <p className="text-xs text-gray-500">{file.size}</p>
                                  </div>
                                </div>
                                <button className="flex items-center gap-1 px-2 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded transition-colors">
                                  <ArrowDownTrayIcon className="w-4 h-4" />
                                  下载
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-8 text-center">
                <button className="flex items-center gap-2 mx-auto px-4 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                  <ChevronDownIcon className="w-4 h-4" />
                  加载更多历史版本
                </button>
              </div>
              </>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* 左侧筛选区域 */}
      <div className="w-64 bg-white shadow-sm border-r flex flex-col h-screen">
        <div className="flex-1 overflow-y-auto p-6">
          <h3 className="font-semibold text-gray-900 mb-4">
            {language === 'zh' ? '分类' : 'Categories'}
          </h3>
          
          {/* All Option */}
          <div className="mb-4">
            <button
              onClick={() => {
                setSelectedCategory('all');
                setSelectedSubcategory('all');
                setMySpacesTab(null);
              }}
              className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                selectedCategory === 'all' 
                  ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              {language === 'zh' ? '全部' : 'All'}
            </button>
          </div>

          {/* Filter Categories */}
          <div className="space-y-2 mb-6">
            {filterCategories.map((category) => (
              <div key={category.key}>
                <button
                  onClick={() => {
                    toggleCategory(category.key);
                    setSelectedCategory(category.key);
                    setSelectedSubcategory('all');
                    setMySpacesTab(null);
                  }}
                  className={`w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors ${
                    selectedCategory === category.key 
                      ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <span className="font-medium">{category.label}</span>
                  {expandedCategories[category.key] ? (
                    <ChevronDownIcon className="h-4 w-4" />
                  ) : (
                    <ChevronRightIcon className="h-4 w-4" />
                  )}
                </button>
                
                {expandedCategories[category.key] && (
                  <div className="ml-4 mt-2 space-y-1">
                    {category.subcategories.map((subcategory) => (
                      <button
                        key={subcategory.key}
                        onClick={() => {
                          setSelectedCategory(category.key);
                          setSelectedSubcategory(subcategory.key);
                          setMySpacesTab(null);
                        }}
                        className={`w-full text-left px-3 py-2 text-sm rounded-lg transition-colors ${
                          selectedCategory === category.key && selectedSubcategory === subcategory.key
                            ? 'bg-blue-100 text-blue-800'
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                      >
                        {subcategory.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="mb-6">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            >
              <option value="hot">{t('allspaces.sort.hot')}</option>
              <option value="new">{t('allspaces.sort.new')}</option>
              <option value="members">{t('allspaces.sort.members')}</option>
            </select>
          </div>

        </div>
      </div>

      {/* 中间内容区域 */}
      <div className="flex-1 p-6">
        {selectedSpace ? (
          <div>
            <div className="mb-4">
              <button
                onClick={() => setSelectedSpace(null)}
                className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="w-4 h-4">←</div>
                返回列表
              </button>
            </div>
            {renderSpaceDetail()}
          </div>
        ) : mySpacesTab && mySpacesTab !== 'expanded' ? (
          <div>
            <div className="mb-6">
              <div className="flex items-center gap-4 mb-4">
                <button
                  onClick={() => setMySpacesTab(null)}
                  className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <div className="w-4 h-4">←</div>
                  {language === 'zh' ? '返回列表' : 'Back to List'}
                </button>
              </div>
              
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {mySpacesTab === 'created' ? t('collab.created.spaces') :
                 mySpacesTab === 'joined' ? t('collab.joined.spaces') : 
                 mySpacesTab === 'created-daw' ? t('collab.created.daw') : 
                 mySpacesTab === 'joined-daw' ? t('collab.joined.daw') :
                 mySpacesTab === 'signed-contracts' ? t('collab.signed.contracts') : 
                 mySpacesTab === 'pending-contracts' ? t('collab.pending.contracts') : 
                 mySpacesTab === 'crowdfunding' ? (language === 'zh' ? '我的众筹' : 'My Crowdfunding') :
                 t('collab.copyright.contracts')}
              </h1>
              <p className="text-gray-600">
                {mySpacesTab === 'created' ? t('collab.manage.created') :
                 mySpacesTab === 'joined' ? t('collab.view.joined') : 
                 mySpacesTab === 'created-daw' ? t('collab.manage.daw.created') : 
                 mySpacesTab === 'joined-daw' ? t('collab.view.daw.joined') :
                 mySpacesTab === 'signed-contracts' ? t('collab.view.signed.contracts') : 
                 mySpacesTab === 'pending-contracts' ? t('collab.view.pending.contracts') : 
                 mySpacesTab === 'crowdfunding' ? (language === 'zh' ? '管理您的众筹项目和查看支持者' : 'Manage your crowdfunding projects and view supporters') :
                 t('collab.manage.copyright.contracts')}
              </p>
            </div>

            {mySpacesTab === 'created' && renderMySpacesList(createdSpaces)}
            {mySpacesTab === 'joined' && renderMySpacesList(joinedSpaces)}
            {mySpacesTab === 'created-daw' && renderDAWProjects('created')}
            {mySpacesTab === 'joined-daw' && renderDAWProjects('joined')}
            {mySpacesTab === 'signed-contracts' && renderContractList('signed')}
            {mySpacesTab === 'pending-contracts' && renderContractList('pending')}
            {mySpacesTab === 'my-contracts' && renderMyCreatedContracts()}
            {mySpacesTab === 'crowdfunding' && renderCrowdfundingContent()}
          </div>
        ) : (
          <div>
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{t('collab.all.spaces')}</h1>
              <p className="text-gray-600">{t('collab.subtitle')}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {allSpaces.map((space) => (
                <div key={space.id} className="bg-white rounded-lg shadow-sm border overflow-hidden hover:shadow-md transition-shadow flex flex-col">
                  {/* 背景图片 */}
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={space.image}
                      alt={space.name}
                      className="w-full h-full object-cover"
                    />
                    {/* 状态标签 */}
                    <div className="absolute top-3 right-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        space.status === t('allspaces.status.recruiting') ? 'bg-green-100 text-green-800' :
                        space.status === t('allspaces.status.ongoing') ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {space.status}
                      </span>
                    </div>
                    {/* 创建者头像 */}
                    <div className="absolute bottom-3 left-3">
                      <img
                        src={space.creatorAvatar}
                        alt={space.creator}
                        className="w-8 h-8 rounded-full border-2 border-white"
                      />
                    </div>
                  </div>
                  
                  {/* 卡片内容 */}
                  <div className="p-4 flex flex-col flex-grow">
                    <div className="mb-3">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">{space.name}</h3>
                      <p className="text-gray-600 text-sm line-clamp-2">{space.description}</p>
                    </div>
                    
                    {/* 需要的角色 */}
                    <div className="mb-3">
                      <div className="flex flex-wrap gap-1">
                        {space.roles.map((role, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full"
                          >
                            {role}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    {/* 成员头像 */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        <div className="flex -space-x-2">
                          {space.memberAvatars.slice(0, 3).map((avatar, index) => (
                            <img
                              key={index}
                              src={avatar}
                              alt={`Member ${index + 1}`}
                              className="w-6 h-6 rounded-full border-2 border-white"
                            />
                          ))}
                          {space.members > 3 && (
                            <div className="w-6 h-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                              <span className="text-xs text-gray-600">+{space.members - 3}</span>
                            </div>
                          )}
                        </div>
                        <span className="ml-2 text-sm text-gray-500">{space.members} {language === 'zh' ? '成员' : 'members'}</span>
                      </div>
                      <span className="text-xs text-gray-500">{space.createdTime}</span>
                    </div>
                    
                    {/* 统计数据 */}
                    <div className="flex items-center justify-between mb-4 text-sm text-gray-500">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <EyeIcon className="w-4 h-4" />
                          <span>{space.viewCount}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <HeartIcon className="w-4 h-4" />
                          <span>{space.likeCount}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <ShareIcon className="w-4 h-4" />
                          <span>{space.shareCount}</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* 申请加入按钮 */}
                    <div className="mt-auto">
                      <button 
                      onClick={() => {
                        setSelectedJoinSpace(space);
                        setShowJoinDialog(true);
                        setSelectedJoinRoles([]);
                        setCustomShares({});
                        setJoinMessage('');
                      }}
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                      >
                        {language === 'zh' ? '申请加入' : 'Apply to Join'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 右侧菜单栏 */}
      <div className="w-64 bg-white shadow-sm border-l flex flex-col h-screen">
        <div className="flex-1 overflow-y-auto p-6">
          <h3 className="font-semibold text-gray-900 mb-4">
            {language === 'zh' ? '我的协创' : 'My Collaboration'}
          </h3>
          
          <div className="space-y-2">
            <button
              onClick={() => {
                setMySpacesTab('created');
                setSelectedSpace(null);
              }}
              className={`w-full text-left px-3 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                mySpacesTab === 'created' 
                  ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <UserGroupIcon className="h-4 w-4" />
              <span className="text-sm">{t('collab.created.spaces')} ({createdSpaces.length})</span>
            </button>
            
            <button
              onClick={() => {
                setMySpacesTab('joined');
                setSelectedSpace(null);
              }}
              className={`w-full text-left px-3 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                mySpacesTab === 'joined' 
                  ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <UserGroupIcon className="h-4 w-4" />
              <span className="text-sm">{t('collab.joined.spaces')} (3)</span>
            </button>
            
            <button
              onClick={() => {
                setMySpacesTab('signed-contracts');
                setSelectedSpace(null);
              }}
              className={`w-full text-left px-3 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                mySpacesTab === 'signed-contracts' 
                  ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <DocumentCheckIcon className="h-4 w-4" />
              <span className="text-sm">{language === 'zh' ? '已签协议' : 'Signed Contracts'} (2)</span>
            </button>
            
            <button
              onClick={() => {
                setMySpacesTab('pending-contracts');
                setSelectedSpace(null);
              }}
              className={`w-full text-left px-3 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                mySpacesTab === 'pending-contracts' 
                  ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <DocumentTextIcon className="h-4 w-4" />
              <span className="text-sm">{t('collab.pending.contracts')} (1)</span>
            </button>
            
            <button
              onClick={() => {
                setMySpacesTab('my-contracts');
                setSelectedSpace(null);
              }}
              className={`w-full text-left px-3 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                mySpacesTab === 'my-contracts' 
                  ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <DocumentCheckIcon className="h-4 w-4" />
              <span className="text-sm">{t('collab.copyright.contracts')} (2)</span>
            </button>
            
            <button
              onClick={() => {
                setMySpacesTab('crowdfunding');
                setSelectedSpace(null);
              }}
              className={`w-full text-left px-3 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                mySpacesTab === 'crowdfunding' 
                  ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <RocketLaunchIcon className="h-4 w-4" />
              <span className="text-sm">{language === 'zh' ? '我的众筹' : 'My Crowdfunding'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* 申请加入对话框 */}
      {showJoinDialog && selectedJoinSpace && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] flex flex-col">
            {/* Header - 固定在顶部 */}
            <div className="flex items-center justify-between p-6 border-b flex-shrink-0">
              <h3 className="text-xl font-bold text-gray-900">
                {language === 'zh' ? '申请加入' : 'Apply to Join'}
              </h3>
              <button 
                onClick={() => setShowJoinDialog(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            
            {/* 可滚动内容区域 */}
            <div className="flex-1 overflow-y-auto p-6">
              {/* Space Info */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-2">{selectedJoinSpace.name}</h4>
                <p className="text-sm text-gray-600">{selectedJoinSpace.description}</p>
              </div>
              
              {/* Available Roles */}
              <div className="mb-6">
                <h5 className="font-medium text-gray-900 mb-3">
                  {language === 'zh' ? '空缺角色' : 'Available Roles'}
                  <span className="text-sm text-gray-500 ml-2">
                    (共 {getAvailableRoles(selectedJoinSpace).length} 个空缺)
                  </span>
                </h5>
                <p className="text-sm text-gray-600 mb-4">
                  {language === 'zh' ? 
                    '以下是该协创空间当前需要招募的角色，请选择您希望申请的角色：' : 
                    'The following roles are currently being recruited for this collaboration space. Please select the roles you wish to apply for:'}
                </p>
                <div className="space-y-3">
                  {getAvailableRoles(selectedJoinSpace).map((role: RoleType) => (
                    <div key={role.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={role.id}
                          checked={selectedJoinRoles.includes(role.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedJoinRoles([...selectedJoinRoles, role.id]);
                              setCustomShares({ ...customShares, [role.id]: role.defaultShare });
                            } else {
                              setSelectedJoinRoles(selectedJoinRoles.filter(r => r !== role.id));
                              const newShares = { ...customShares };
                              delete newShares[role.id];
                              setCustomShares(newShares);
                            }
                          }}
                          className="mr-3 h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
                        />
                        <label htmlFor={role.id} className="font-medium text-gray-700">
                          {role.name} <span className="text-sm text-green-600">(招募中)</span>
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={customShares[role.id] || role.defaultShare}
                          onChange={(e) => setCustomShares({ ...customShares, [role.id]: parseInt(e.target.value) || 0 })}
                          disabled={!selectedJoinRoles.includes(role.id)}
                          className="w-16 px-2 py-1 text-sm border rounded focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                        />
                        <span className="text-sm text-gray-500">%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Message Area */}
              <div className="mb-6">
                <label className="block font-medium text-gray-900 mb-2">
                  {language === 'zh' ? '留言（可选）' : 'Message (Optional)'}
                </label>
                <textarea
                  value={joinMessage}
                  onChange={(e) => setJoinMessage(e.target.value)}
                  placeholder={language === 'zh' ? '告诉他们你为什么想加入...' : 'Tell them why you want to join...'}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>
            </div>
            
            {/* Action Buttons - 固定在底部 */}
            <div className="flex space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
              <button
                onClick={() => setShowJoinDialog(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                {language === 'zh' ? '取消' : 'Cancel'}
              </button>
              <button
                onClick={() => {
                  // Handle join application
                  console.log('Apply to join:', {
                    space: selectedJoinSpace,
                    roles: selectedJoinRoles,
                    shares: customShares,
                    message: joinMessage
                  });
                  alert(language === 'zh' ? '申请已提交！创建者将审核您的申请，通过后您可在我的协作空间中查看' : 'Application submitted! The creator will review and you will see it in your applied collaboration spaces after approval');
                  setShowJoinDialog(false);
                }}
                disabled={selectedJoinRoles.length === 0}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                {language === 'zh' ? '提交申请' : 'Submit Application'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 众筹项目详情模态框 */}
      {showCrowdfundingDetail && selectedCrowdfundingProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">
                  {language === 'zh' ? '众筹项目详情' : 'Crowdfunding Project Details'}
                </h2>
                <button 
                  onClick={() => {
                    setShowCrowdfundingDetail(false);
                    setSelectedCrowdfundingProject(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              {/* Project Info */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Content */}
                <div className="lg:col-span-2">
                  <div className="mb-6">
                    <img 
                      src={selectedCrowdfundingProject.image} 
                      alt={selectedCrowdfundingProject.title}
                      className="w-full h-64 object-cover rounded-lg mb-4"
                    />
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      {selectedCrowdfundingProject.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {selectedCrowdfundingProject.description}
                    </p>
                  </div>

                  {/* Project Updates */}
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">
                      {language === 'zh' ? '项目更新' : 'Project Updates'}
                    </h4>
                    <div className="space-y-4">
                      <div className="border-l-4 border-blue-500 pl-4">
                        <div className="flex justify-between items-start mb-2">
                          <h5 className="font-medium text-gray-900">
                            {language === 'zh' ? '录音进度更新' : 'Recording Progress Update'}
                          </h5>
                          <span className="text-sm text-gray-500">2024-01-15</span>
                        </div>
                        <p className="text-sm text-gray-600">
                          {language === 'zh' ? '主歌部分录制完成，预计下周开始副歌录制。感谢所有支持者的耐心等待！' : 'Verse recording completed, chorus recording planned for next week. Thank you for your patience!'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Sidebar */}
                <div className="lg:col-span-1">
                  {/* Funding Progress */}
                  <div className="bg-gray-50 rounded-lg p-6 mb-6">
                    <div className="text-center mb-4">
                      <div className="text-3xl font-bold text-gray-900 mb-1">
                        {selectedCrowdfundingProject.progress}%
                      </div>
                      <div className="text-sm text-gray-600">
                        {language === 'zh' ? '筹资进度' : 'Funding Progress'}
                      </div>
                    </div>
                    
                    <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                      <div
                        className="bg-blue-600 h-3 rounded-full"
                        style={{ width: `${selectedCrowdfundingProject.progress}%` }}
                      />
                    </div>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">{language === 'zh' ? '已筹集' : 'Raised'}</span>
                        <span className="font-medium">
                          {selectedCrowdfundingProject.currentAmount.toLocaleString()} {selectedCrowdfundingProject.currency}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">{language === 'zh' ? '待筹集' : 'Remaining'}</span>
                        <span className="font-medium text-orange-600">
                          {(selectedCrowdfundingProject.targetAmount - selectedCrowdfundingProject.currentAmount).toLocaleString()} {selectedCrowdfundingProject.currency}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">{language === 'zh' ? '目标金额' : 'Goal'}</span>
                        <span className="font-medium">
                          {selectedCrowdfundingProject.targetAmount.toLocaleString()} {selectedCrowdfundingProject.currency}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 支持者列表 */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">
                      {language === 'zh' ? '支持者' : 'Backers'} ({selectedCrowdfundingProject.backers})
                    </h4>
                    <div className="max-h-64 overflow-y-auto space-y-3">
                      {/* Mock支持者数据 */}
                      {[
                        { name: 'Alex Chen', amount: 200, avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face', time: '2 hours ago' },
                        { name: 'Emma Liu', amount: 500, avatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?w=40&h=40&fit=crop&crop=face', time: '5 hours ago' },
                        { name: 'David Wang', amount: 100, avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face', time: '1 day ago' },
                        { name: 'Sarah Kim', amount: 300, avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face', time: '2 days ago' },
                        { name: 'Mike Johnson', amount: 150, avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=40&h=40&fit=crop&crop=face', time: '3 days ago' },
                        { name: 'Lisa Park', amount: 250, avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=40&h=40&fit=crop&crop=face', time: '4 days ago' },
                        { name: 'Tom Wilson', amount: 400, avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face', time: '5 days ago' },
                        { name: 'Anna Rodriguez', amount: 180, avatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?w=40&h=40&fit=crop&crop=face', time: '6 days ago' }
                      ].map((backer, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                          <div className="flex items-center space-x-3">
                            <img 
                              src={backer.avatar} 
                              alt={backer.name}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                            <div>
                              <div className="font-medium text-gray-900 text-sm">{backer.name}</div>
                              <div className="text-xs text-gray-500">{backer.time}</div>
                            </div>
                          </div>
                          <div className="text-sm font-medium text-blue-600">
                            ¥{backer.amount}
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 pt-3 border-t border-gray-200">
                      <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        {language === 'zh' ? '查看所有支持者' : 'View All Backers'} →
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 管理众筹项目模态框 */}
      {showCrowdfundingManage && selectedCrowdfundingProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">
                  {language === 'zh' ? '管理众筹项目' : 'Manage Crowdfunding Project'}
                </h2>
                <button 
                  onClick={() => {
                    setShowCrowdfundingManage(false);
                    setSelectedCrowdfundingProject(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              {/* Management Content */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Management Area */}
                <div className="lg:col-span-2">
                  {/* Project Status */}
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      {language === 'zh' ? '项目状态' : 'Project Status'}
                    </h3>
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                        <span className="font-medium text-green-800">
                          {language === 'zh' ? '进行中' : 'Active'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Update Project */}
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      {language === 'zh' ? '发布更新' : 'Post Update'}
                    </h3>
                    <div className="space-y-4">
                      <input
                        type="text"
                        placeholder={language === 'zh' ? '更新标题' : 'Update title'}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <textarea
                        rows={4}
                        placeholder={language === 'zh' ? '更新内容详情...' : 'Update details...'}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        {language === 'zh' ? '发布更新' : 'Post Update'}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Sidebar */}
                <div className="lg:col-span-1">
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">
                      {language === 'zh' ? '快速操作' : 'Quick Actions'}
                    </h4>
                    <div className="space-y-3">
                      <button className="w-full text-left px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        {language === 'zh' ? '编辑项目信息' : 'Edit Project Info'}
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        {language === 'zh' ? '管理回报层级' : 'Manage Rewards'}
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm bg-red-50 border border-red-200 text-red-700 rounded-lg hover:bg-red-100 transition-colors">
                        {language === 'zh' ? '暂停项目' : 'Pause Project'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AllSpaces;
