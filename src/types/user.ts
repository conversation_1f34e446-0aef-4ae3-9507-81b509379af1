export type UserRole = 'user' | 'streamer' | 'musician' | 'producer' | 'investor' | 'publisher';

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: UserRole;
  verified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserRolePermissions {
  canStream: boolean;
  canCreateMusic: boolean;
  canProduceMusic: boolean;
  canInvest: boolean;
  canPublish: boolean;
  canManageUsers: boolean;
}

export const USER_ROLE_LABELS: Record<UserRole, string> = {
  user: '普通用户',
  streamer: '主播',
  musician: '音乐人',
  producer: '制作人',
  investor: '投资人',
  publisher: '发行商'
};

export const USER_ROLE_PERMISSIONS: Record<UserRole, UserRolePermissions> = {
  user: {
    canStream: false,
    canCreateMusic: false,
    canProduceMusic: false,
    canInvest: false,
    canPublish: false,
    canManageUsers: false
  },
  streamer: {
    canStream: true,
    canCreateMusic: false,
    canProduceMusic: false,
    canInvest: false,
    canPublish: false,
    canManageUsers: false
  },
  musician: {
    canStream: true,
    canCreateMusic: true,
    canProduceMusic: false,
    canInvest: false,
    canPublish: false,
    canManageUsers: false
  },
  producer: {
    canStream: true,
    canCreateMusic: true,
    canProduceMusic: true,
    canInvest: false,
    canPublish: false,
    canManageUsers: false
  },
  investor: {
    canStream: false,
    canCreateMusic: false,
    canProduceMusic: false,
    canInvest: true,
    canPublish: false,
    canManageUsers: false
  },
  publisher: {
    canStream: false,
    canCreateMusic: false,
    canProduceMusic: false,
    canInvest: false,
    canPublish: true,
    canManageUsers: false
  }
};