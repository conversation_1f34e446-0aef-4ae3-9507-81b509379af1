import React, { useState } from 'react';
import * as Tone from 'tone';
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline';

interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  instrument?: any;
  effects: any[];
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
}

interface EffectsPanelProps {
  track: Track;
  onUpdateTrack: (trackId: string, updates: Partial<Track>) => void;
}

interface EffectDefinition {
  name: string;
  type: string;
  create: () => any;
}

const availableEffects: EffectDefinition[] = [
  {
    name: 'Reverb',
    type: 'reverb',
    create: () => new Tone.Reverb(0.8)
  },
  {
    name: 'Delay',
    type: 'delay',
    create: () => new Tone.PingPongDelay("8n", 0.3)
  },
  {
    name: 'Chorus',
    type: 'chorus',
    create: () => new Tone.Chorus(4, 2.5, 0.5)
  },
  {
    name: 'Distortion',
    type: 'distortion',
    create: () => new Tone.Distortion(0.4)
  },
  {
    name: 'Filter',
    type: 'filter',
    create: () => new Tone.Filter(1000, "lowpass")
  },
  {
    name: 'Compressor',
    type: 'compressor',
    create: () => new Tone.Compressor(-24, 12)
  },
  {
    name: 'EQ3',
    type: 'eq3',
    create: () => new Tone.EQ3()
  },
  {
    name: 'Phaser',
    type: 'phaser',
    create: () => new Tone.Phaser()
  }
];

const EffectsPanel: React.FC<EffectsPanelProps> = ({ track, onUpdateTrack }) => {
  const [showEffectMenu, setShowEffectMenu] = useState(false);

  const addEffect = (effectDef: EffectDefinition) => {
    const effect = effectDef.create();
    const newEffects = [...track.effects, effect];
    
    // Reconnect the audio chain
    if (track.instrument) {
      track.instrument.disconnect();
      
      if (newEffects.length > 0) {
        // Connect instrument to first effect
        track.instrument.connect(newEffects[0]);
        
        // Connect effects in chain
        for (let i = 0; i < newEffects.length - 1; i++) {
          newEffects[i].connect(newEffects[i + 1]);
        }
        
        // Connect last effect to destination
        newEffects[newEffects.length - 1].toDestination();
      } else {
        // Connect instrument directly to destination
        track.instrument.toDestination();
      }
    }
    
    onUpdateTrack(track.id, { effects: newEffects });
    setShowEffectMenu(false);
  };

  const removeEffect = (effectIndex: number) => {
    const effectToRemove = track.effects[effectIndex];
    const newEffects = track.effects.filter((_, index) => index !== effectIndex);
    
    // Dispose of the removed effect
    effectToRemove.dispose();
    
    // Reconnect the audio chain
    if (track.instrument) {
      track.instrument.disconnect();
      
      if (newEffects.length > 0) {
        // Connect instrument to first effect
        track.instrument.connect(newEffects[0]);
        
        // Connect effects in chain
        for (let i = 0; i < newEffects.length - 1; i++) {
          newEffects[i].connect(newEffects[i + 1]);
        }
        
        // Connect last effect to destination
        newEffects[newEffects.length - 1].toDestination();
      } else {
        // Connect instrument directly to destination
        track.instrument.toDestination();
      }
    }
    
    onUpdateTrack(track.id, { effects: newEffects });
  };

  const getEffectName = (effect: any): string => {
    // Get effect name from constructor
    const constructorName = effect.constructor.name;
    return constructorName.replace('Tone', '');
  };

  const EffectControls: React.FC<{ effect: any; index: number }> = ({ effect, index }) => {
    const effectName = getEffectName(effect);
    
    const renderEffectParams = () => {
      const params = [];
      
      // Common parameters based on effect type
      const effectName = getEffectName(effect);
      
      if (effectName === 'Reverb' && effect.decay) {
        params.push(
          <div key="decay" className="flex items-center justify-between text-xs mb-1">
            <span>Decay:</span>
            <input
              type="range"
              min="0.1"
              max="5"
              step="0.1"
              defaultValue="1.5"
              onChange={(e) => {
                effect.decay.value = parseFloat(e.target.value);
              }}
              className="w-16"
            />
          </div>
        );
      } else if (effectName === 'PingPongDelay' && effect.feedback) {
        params.push(
          <div key="feedback" className="flex items-center justify-between text-xs mb-1">
            <span>Feedback:</span>
            <input
              type="range"
              min="0"
              max="0.8"
              step="0.1"
              defaultValue="0.3"
              onChange={(e) => {
                effect.feedback.value = parseFloat(e.target.value);
              }}
              className="w-16"
            />
          </div>
        );
      } else if (effectName === 'Filter' && effect.frequency) {
        params.push(
          <div key="frequency" className="flex items-center justify-between text-xs mb-1">
            <span>Frequency:</span>
            <input
              type="range"
              min="100"
              max="10000"
              step="100"
              defaultValue="1000"
              onChange={(e) => {
                effect.frequency.value = parseFloat(e.target.value);
              }}
              className="w-16"
            />
          </div>
        );
      } else if (effectName === 'Distortion' && effect.distortion !== undefined) {
        params.push(
          <div key="distortion" className="flex items-center justify-between text-xs mb-1">
            <span>Drive:</span>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              defaultValue="0.4"
              onChange={(e) => {
                effect.distortion = parseFloat(e.target.value);
              }}
              className="w-16"
            />
          </div>
        );
      }
      
      return params;
    };

    return (
      <div className="effect-slot">
        <div className="effect-info">
          <div className="effect-name font-medium">{effectName}</div>
          <div className="effect-params mt-2">
            {renderEffectParams()}
          </div>
        </div>
        <button
          className="effect-remove"
          onClick={() => removeEffect(index)}
          title="Remove effect"
        >
          <TrashIcon className="w-3 h-3" />
        </button>
      </div>
    );
  };

  return (
    <div className="effects-panel">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-bold text-blue-400">Effects</h3>
        <div className="relative">
          <button
            className="flex items-center px-2 py-1 bg-gray-600 text-white rounded text-xs hover:bg-gray-700"
            onClick={() => setShowEffectMenu(!showEffectMenu)}
          >
            <PlusIcon className="w-3 h-3 mr-1" />
            Add
          </button>
          
          {showEffectMenu && (
            <div className="absolute right-0 top-8 w-40 bg-gray-800 border border-gray-600 rounded shadow-lg z-50">
              {availableEffects.map((effectDef) => (
                <button
                  key={effectDef.type}
                  className="w-full text-left px-3 py-2 text-xs text-white hover:bg-gray-700"
                  onClick={() => addEffect(effectDef)}
                >
                  {effectDef.name}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
      
      <div className="effects-chain">
        {track.effects.length === 0 ? (
          <div className="text-xs text-gray-500 text-center py-4">
            No effects applied
          </div>
        ) : (
          track.effects.map((effect, index) => (
            <EffectControls key={index} effect={effect} index={index} />
          ))
        )}
      </div>
      
      <div className="mt-4 pt-3 border-t border-gray-600">
        <h4 className="text-xs font-medium mb-2 text-gray-400">Presets</h4>
        <div className="space-y-1">
          <button className="w-full text-left px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600">
            Clean
          </button>
          <button className="w-full text-left px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600">
            Ambient
          </button>
          <button className="w-full text-left px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600">
            Rock Lead
          </button>
          <button className="w-full text-left px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600">
            Bass Boost
          </button>
        </div>
      </div>
    </div>
  );
};

export default EffectsPanel;