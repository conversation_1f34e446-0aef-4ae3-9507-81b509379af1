import React, { useState, useMemo } from 'react';
import { 
  MusicalNoteIcon, 
  UserGroupIcon, 
  QueueListIcon, 
  HeartIcon,
  PlusIcon,
  EyeIcon,
  PlayIcon,
  ShareIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

const CreativeCenter: React.FC = () => {
  const [activeSection, setActiveSection] = useState('works');
  const { t } = useLanguage();

  const sections = [
    { id: 'works', name: t('creative.my.works'), icon: MusicalNoteIcon },
    { id: 'collaborations', name: t('creative.my.collaborations'), icon: UserGroupIcon },
    { id: 'playlists', name: t('creative.my.playlists'), icon: QueueListIcon },
    { id: 'favorites', name: t('creative.my.favorites'), icon: HeartIcon }
  ];

  const myWorks = useMemo(() => [
    {
      id: 1,
      title: t('creative.work.nocturne'),
      cover: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=200&h=200&fit=crop',
      duration: '3:45',
      status: 'published',
      plays: 1234,
      likes: 89,
      comments: 23,
      createdAt: '2024-01-15',
      genre: t('creative.genre.classical')
    },
    {
      id: 2,
      title: t('creative.work.spring.story'),
      cover: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=200&h=200&fit=crop',
      duration: '4:12',
      status: 'published',
      plays: 2456,
      likes: 156,
      comments: 45,
      createdAt: '2024-01-10',
      genre: t('creative.genre.folk')
    },
    {
      id: 3,
      title: t('creative.work.untitled'),
      cover: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=200&h=200&fit=crop',
      duration: '2:30',
      status: 'draft',
      plays: 0,
      likes: 0,
      comments: 0,
      createdAt: '2024-01-20',
      genre: t('creative.genre.electronic')
    }
  ], [t]);

  const collaborations = useMemo(() => [
    {
      id: 1,
      title: t('creative.collab.dream.concerto'),
      members: [t('creative.role.producer'), t('creative.role.pianist'), t('creative.role.violinist')],
      myRole: t('creative.role.arranger'),
      status: 'in_progress',
      progress: 75,
      deadline: '2024-02-15'
    },
    {
      id: 2,
      title: t('creative.collab.youth.memories'),
      members: [t('creative.role.singer'), t('creative.role.guitarist')],
      myRole: t('creative.role.lyricist'),
      status: 'completed',
      progress: 100,
      deadline: '2024-01-01'
    }
  ], [t]);

  const playlists = useMemo(() => [
    {
      id: 1,
      name: t('creative.playlist.my.originals'),
      cover: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=200&h=200&fit=crop',
      trackCount: 12,
      totalDuration: '45:23',
      isPublic: true,
      createdAt: '2024-01-01'
    },
    {
      id: 2,
      name: t('creative.playlist.favorite.classical'),
      cover: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=200&h=200&fit=crop',
      trackCount: 25,
      totalDuration: '87:45',
      isPublic: false,
      createdAt: '2024-01-05'
    },
    {
      id: 3,
      name: t('creative.playlist.relaxing.time'),
      cover: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=200&h=200&fit=crop',
      trackCount: 18,
      totalDuration: '62:12',
      isPublic: true,
      createdAt: '2024-01-12'
    }
  ], [t]);

  const favorites = useMemo(() => [
    {
      id: 1,
      title: t('creative.favorite.moonlight.sonata'),
      artist: t('creative.artist.beethoven'),
      cover: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=200&h=200&fit=crop',
      duration: '15:30',
      addedAt: '2024-01-18'
    },
    {
      id: 2,
      title: t('creative.favorite.jasmine.flower'),
      artist: t('creative.artist.folk.music'),
      cover: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=200&h=200&fit=crop',
      duration: '3:45',
      addedAt: '2024-01-16'
    }
  ], [t]);

  const renderWorks = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{t('creative.my.works')}</h3>
        <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center">
          <PlusIcon className="h-4 w-4 mr-2" />
          {t('creative.upload.new.work')}
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {myWorks.map((work) => (
          <div key={work.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="relative">
              <img
                src={work.cover}
                alt={work.title}
                className="w-full h-48 object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                <button className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                  <PlayIcon className="h-6 w-6 text-indigo-600" />
                </button>
              </div>
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                {work.duration}
              </div>
            </div>
            
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-semibold text-gray-900 truncate">{work.title}</h4>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  work.status === 'published' 
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {work.status === 'published' ? t('creative.published') : t('creative.draft')}
                </span>
              </div>
              
              <div className="flex items-center space-x-2 text-xs text-gray-600 mb-3">
                <span>{work.genre}</span>
                <span>•</span>
                <span>{work.createdAt}</span>
              </div>
              
              <div className="flex items-center justify-between text-sm text-gray-600">
                <div className="flex items-center space-x-4">
                  <span className="flex items-center">
                    <EyeIcon className="h-4 w-4 mr-1" />
                    {work.plays}
                  </span>
                  <span className="flex items-center">
                    <HeartIcon className="h-4 w-4 mr-1" />
                    {work.likes}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="text-indigo-600 hover:text-indigo-700">
                    {t('creative.edit')}
                  </button>
                  <button className="text-gray-600 hover:text-gray-700">
                    <ShareIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderCollaborations = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{t('creative.my.collaborations')}</h3>
        <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center">
          <PlusIcon className="h-4 w-4 mr-2" />
          {t('creative.join.collaboration')}
        </button>
      </div>
      
      <div className="space-y-4">
        {collaborations.map((collab) => (
          <div key={collab.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-gray-900">{collab.title}</h4>
              <span className={`px-3 py-1 rounded-full text-sm ${
                collab.status === 'completed'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {collab.status === 'completed' ? t('creative.completed') : t('creative.in.progress')}
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-600 mb-2">{t('creative.collaboration.members')}</p>
                <div className="flex flex-wrap gap-2">
                  {collab.members.map((member, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                      {member}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-600 mb-2">{t('creative.my.role')}</p>
                <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-sm">
                  {collab.myRole}
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex-1 mr-4">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm text-gray-600">{t('creative.progress')}</span>
                  <span className="text-sm text-gray-600">{collab.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-indigo-600 h-2 rounded-full"
                    style={{ width: `${collab.progress}%` }}
                  ></div>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                {t('creative.deadline')}: {collab.deadline}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderPlaylists = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{t('creative.my.playlists')}</h3>
        <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center">
          <PlusIcon className="h-4 w-4 mr-2" />
          {t('creative.create.playlist')}
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {playlists.map((playlist) => (
          <div key={playlist.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <img
              src={playlist.cover}
              alt={playlist.name}
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-semibold text-gray-900 truncate">{playlist.name}</h4>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  playlist.isPublic 
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {playlist.isPublic ? t('creative.public') : t('creative.private')}
                </span>
              </div>
              
              <div className="text-sm text-gray-600 mb-3">
                <p>{playlist.trackCount} {t('creative.songs')}</p>
                <p>{playlist.totalDuration}</p>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">
                  {t('creative.created.on')} {playlist.createdAt}
                </span>
                <div className="flex items-center space-x-2">
                  <button className="text-indigo-600 hover:text-indigo-700 text-sm">
                    {t('creative.edit')}
                  </button>
                  <button className="text-gray-600 hover:text-gray-700">
                    <ShareIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderFavorites = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{t('creative.my.favorites')}</h3>
        <p className="text-sm text-gray-600">{t('creative.total')} {favorites.length} {t('creative.songs')}</p>
      </div>
      
      <div className="space-y-4">
        {favorites.map((favorite) => (
          <div key={favorite.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center space-x-4">
              <img
                src={favorite.cover}
                alt={favorite.title}
                className="w-16 h-16 rounded-lg object-cover"
              />
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900">{favorite.title}</h4>
                <p className="text-gray-600">{favorite.artist}</p>
                <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                  <span>{favorite.duration}</span>
                  <span>{t('creative.favorited.on')} {favorite.addedAt}</span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button className="w-10 h-10 bg-indigo-600 text-white rounded-full flex items-center justify-center hover:bg-indigo-700">
                  <PlayIcon className="h-5 w-5" />
                </button>
                <button className="w-10 h-10 border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50">
                  <HeartIcon className="h-5 w-5 text-red-500" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'works':
        return renderWorks();
      case 'collaborations':
        return renderCollaborations();
      case 'playlists':
        return renderPlaylists();
      case 'favorites':
        return renderFavorites();
      default:
        return renderWorks();
    }
  };

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex space-x-8">
          {sections.map((section) => {
            const Icon = section.icon;
            const isActive = activeSection === section.id;
            
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  isActive
                    ? 'bg-indigo-50 text-indigo-700'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Icon className={`h-5 w-5 ${isActive ? 'text-indigo-500' : 'text-gray-400'}`} />
                <span className="font-medium">{section.name}</span>
              </button>
            );
          })}
        </div>
      </div>
      
      {/* Content Area */}
      {renderContent()}
    </div>
  );
};

export default CreativeCenter;