import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  UserGroupIcon, 
  DocumentTextIcon, 
  FolderIcon, 
  UserIcon,
  PlusIcon,
  ShareIcon,
  ChatBubbleLeftRightIcon,
  MusicalNoteIcon,
  DocumentCheckIcon,
  ClockIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  RocketLaunchIcon,
  CubeTransparentIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

interface CollabSpace {
  id: number;
  name: string;
  description: string;
  coverImage: string;
  privacy: 'public' | 'participants' | 'searchable';
  memberCount: number;
  memberAvatars: string[];
  creator: string;
  isCreator: boolean;
  hasSignedContract: boolean;
  recentActivity: string;
  createdTime: string;
  role?: string;
}

interface Contract {
  id: number;
  spaceName: string;
  type: 'signed' | 'pending';
  signedDate?: string;
  parties: string[];
  status: string;
}

const MyCollaborationSpaces: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'created' | 'joined' | 'signed-contracts' | 'pending-contracts' | 'my-contracts' | ''>('');
  const [selectedSpace, setSelectedSpace] = useState<CollabSpace | null>(null);
  const [spaceDetailTab, setSpaceDetailTab] = useState<'discussion' | 'activity' | 'media' | 'members'>('discussion');
  const [isPublishing, setIsPublishing] = useState(false);
  const [isCreatingNFT, setIsCreatingNFT] = useState(false);
  const navigate = useNavigate();
  const { t, language } = useLanguage();

  const createdSpaces: CollabSpace[] = useMemo(() => [
    {
      id: 1,
      name: t('collab.space.pop.title'),
      description: t('collab.space.pop.desc'),
      coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=300&fit=crop',
      privacy: 'public',
      memberCount: 4,
      memberAvatars: [
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face'
      ],
      creator: t('collab.creator.wang'),
      isCreator: true,
      hasSignedContract: false,
      recentActivity: t('collab.time.2hours.ago') + t('collab.activity.melody'),
      createdTime: "2024-01-15"
    },
    {
      id: 2,
      name: t('collab.space.folk.title'),
      description: t('collab.space.folk.desc'),
      coverImage: 'https://images.unsplash.com/photo-1520166012956-add9ba0835cb?w=600&h=300&fit=crop',
      privacy: 'searchable',
      memberCount: 3,
      memberAvatars: [
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
      ],
      creator: t('collab.creator.wang'),
      isCreator: true,
      hasSignedContract: true,
      recentActivity: t('collab.time.yesterday') + t('collab.activity.recording'),
      createdTime: "2024-01-10"
    }
  ], [t, language]);

  const joinedSpaces: CollabSpace[] = useMemo(() => [
    {
      id: 3,
      name: t('collab.space.electronic.title'),
      description: t('collab.space.electronic.desc'),
      coverImage: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=600&h=300&fit=crop',
      privacy: 'public',
      memberCount: 6,
      memberAvatars: [
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
      ],
      creator: t('collab.creator.dj.alex'),
      isCreator: false,
      hasSignedContract: true,
      recentActivity: t('collab.time.3hours.ago') + t('collab.activity.discussion'),
      createdTime: "2024-01-08",
      role: t('collab.role.arrangement')
    },
    {
      id: 4,
      name: t('collab.space.ancient.title'),
      description: t('collab.space.ancient.desc'),
      coverImage: 'https://images.unsplash.com/photo-1507838153414-b4b713384a76?w=600&h=300&fit=crop',
      privacy: 'participants',
      memberCount: 5,
      memberAvatars: [
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face'
      ],
      creator: t('collab.creator.ancient'),
      isCreator: false,
      hasSignedContract: false,
      recentActivity: t('collab.time.1day.ago') + t('collab.activity.participated'),
      createdTime: "2024-01-05",
      role: t('collab.role.lyrics')
    }
  ], [t, language]);

  const contracts: Contract[] = useMemo(() => [
    {
      id: 1,
      spaceName: t('collab.space.electronic.title'),
      type: 'signed',
      signedDate: "2024-01-20",
      parties: [t('collab.creator.dj.alex'), t('collab.creator.wang'), t('collab.creator.arranger'), t('collab.creator.mixer')],
      status: t('collab.contract.status.active')
    },
    {
      id: 2,
      spaceName: t('collab.space.folk.title'),
      type: 'signed',
      signedDate: "2024-01-18",
      parties: [t('collab.creator.wang'), t('collab.creator.guitarist'), t('collab.creator.singer')],
      status: t('collab.contract.status.active')
    },
    {
      id: 3,
      spaceName: t('collab.space.ancient.title'),
      type: 'pending',
      parties: [t('collab.creator.ancient'), t('collab.creator.wang'), t('collab.creator.zhao')],
      status: t('collab.contract.status.pending')
    }
  ], [t, language]);

  const myCreatedContracts = useMemo(() => [
    {
      id: 1,
      contractTitle: t('collab.contract.title.dreams'),
      spaceName: t('collab.space.pop.title'),
      createdDate: "2024-01-25",
      status: t('collab.contract.status.deployed'),
      blockchainHash: "0x1234567890abcdef...",
      parties: [t('collab.creator.wang'), t('collab.creator.lyricist'), t('collab.creator.vocalist'), t('collab.creator.mixing.engineer')],
      totalRevenue: t('collab.contract.revenue.composition')
    },
    {
      id: 2,
      contractTitle: t('collab.contract.title.folk'),
      spaceName: t('collab.space.folk.title'),
      createdDate: "2024-01-20",
      status: t('collab.contract.status.waiting'),
      blockchainHash: "",
      parties: [t('collab.creator.wang'), t('collab.creator.chen'), t('collab.creator.liu')],
      totalRevenue: t('collab.contract.revenue.folk')
    }
  ], [t, language]);

  const handleSpaceClick = (space: CollabSpace) => {
    setSelectedSpace(space);
    setSpaceDetailTab('discussion');
  };

  const handleInviteMember = () => {
    console.log(t('collab.invite.members'));
  };

  const handleShareSpace = () => {
    console.log(t('collab.share.space'));
  };

  const handleSignContract = () => {
    navigate('/collaboration/contract');
  };

  const handleOpenDAW = () => {
    console.log(t('collab.daw'));
    // 跳转到DAW页面
  };

  const handleCopyrightContract = () => {
    navigate('/collaboration/copyright-contract');
  };

  const renderSpaceList = (spaces: CollabSpace[]) => (
    <div className="space-y-4">
      {spaces.map((space) => (
        <div 
          key={space.id} 
          className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
          onClick={() => handleSpaceClick(space)}
        >
          <div className="flex">
            {/* 空间封面 */}
            <div className="w-32 h-24 flex-shrink-0">
              <img
                src={space.coverImage}
                alt={space.name}
                className="w-full h-full object-cover rounded-l-lg"
              />
            </div>
            
            {/* 空间信息 */}
            <div className="flex-1 p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                  {space.name}
                </h3>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    space.privacy === 'public' ? 'bg-green-100 text-green-800' :
                    space.privacy === 'searchable' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {space.privacy === 'public' ? t('collab.privacy.public') : 
                     space.privacy === 'searchable' ? t('collab.privacy.searchable') : t('collab.privacy.private')}
                  </span>
                  {space.hasSignedContract && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                      {t('collab.contract.signed')}
                    </span>
                  )}
                  {space.isCreator && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        alert(`${t('collab.publish.work')}: ${space.name}`);
                      }}
                      className="px-3 py-1 bg-green-600 text-white rounded-full text-xs font-medium hover:bg-green-700 transition-colors"
                    >
                      {t('collab.publish')}
                    </button>
                  )}
                </div>
              </div>
              
              <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                {space.description}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <UserGroupIcon className="w-4 h-4" />
                    <span>{space.memberCount} {t('collab.members.count')}</span>
                  </div>
                  {space.role && (
                    <div className="flex items-center gap-1">
                      <UserIcon className="w-4 h-4" />
                      <span>{t('collab.my.role')}: {space.role}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-1">
                    <ClockIcon className="w-4 h-4" />
                    <span>{space.recentActivity}</span>
                  </div>
                </div>
                
                {/* 成员头像 */}
                <div className="flex -space-x-2">
                  {space.memberAvatars.slice(0, 3).map((avatar, index) => (
                    <img
                      key={index}
                      src={avatar}
                      alt=""
                      className="w-6 h-6 rounded-full border-2 border-white"
                    />
                  ))}
                  {space.memberCount > 3 && (
                    <div className="w-6 h-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                      <span className="text-xs text-gray-600">+{space.memberCount - 3}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderContractList = () => (
    <div className="space-y-4">
      {contracts.map((contract) => (
        <div key={contract.id} className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex justify-between items-start mb-3">
            <h3 className="text-lg font-semibold text-gray-900">
              {contract.spaceName}
            </h3>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              contract.type === 'signed' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {contract.type === 'signed' ? t('collab.contracts.signed') : t('collab.contracts.pending')}
            </span>
          </div>
          
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <UserGroupIcon className="w-4 h-4" />
              <span>{t('collab.contract.parties')}: {contract.parties.join('、')}</span>
            </div>
            {contract.signedDate && (
              <div className="flex items-center gap-2">
                <DocumentCheckIcon className="w-4 h-4" />
                <span>{t('collab.contract.signed.date')}: {contract.signedDate}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <ClockIcon className="w-4 h-4" />
              <span>{t('collab.contract.status')}: {contract.status}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderMyCreatedContracts = () => (
    <div className="space-y-4">
      {myCreatedContracts.map((contract) => (
        <div key={contract.id} className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                {contract.contractTitle}
              </h3>
              <p className="text-sm text-gray-600">
                {t('collab.source.space')}: {contract.spaceName}
              </p>
            </div>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              contract.status === t('collab.contract.status.deployed') 
                ? 'bg-green-100 text-green-800' 
                : contract.status === t('collab.contract.status.waiting')
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-gray-100 text-gray-800'
            }`}>
              {contract.status}
            </span>
          </div>
          
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <CalendarIcon className="w-4 h-4" />
              <span>{t('collab.contract.created.date')}: {contract.createdDate}</span>
            </div>
            
            <div className="flex items-center gap-2">
              <UserGroupIcon className="w-4 h-4" />
              <span>{t('collab.contract.parties')}: {contract.parties.join('、')}</span>
            </div>
            
            <div className="flex items-start gap-2">
              <CurrencyDollarIcon className="w-4 h-4 mt-0.5" />
              <span>{t('collab.contract.revenue.split')}: {contract.totalRevenue}</span>
            </div>
            
            {contract.blockchainHash && (
              <div className="flex items-center gap-2">
                <DocumentTextIcon className="w-4 h-4" />
                <span>{t('collab.contract.blockchain.hash')}: {contract.blockchainHash}</span>
              </div>
            )}
          </div>

          <div className="mt-4 flex gap-2">
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
              {t('collab.contract.view.details')}
            </button>
            {contract.status === t('collab.contract.status.waiting') && (
              <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm">
                {t('collab.contract.send.invite')}
              </button>
            )}
            <button 
              onClick={() => navigate('/collaboration/copyright-contract')}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
            >
              {t('collab.contract.edit')}
            </button>
          </div>
        </div>
      ))}

      <div className="text-center py-8">
        <button
          onClick={() => navigate('/collaboration/copyright-contract')}
          className="flex items-center gap-2 mx-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon className="w-5 h-5" />
          {t('collab.contract.create.new')}
        </button>
      </div>
    </div>
  );

  const renderSpaceDetail = () => {
    if (!selectedSpace) return null;

    return (
      <div className="bg-white rounded-lg shadow-sm border">
        {/* 空间封面和基本信息 */}
        <div className="relative h-48 overflow-hidden rounded-t-lg">
          <img
            src={selectedSpace.coverImage}
            alt={selectedSpace.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          <div className="absolute bottom-4 left-4 text-white">
            <h2 className="text-2xl font-bold mb-1">{selectedSpace.name}</h2>
            <p className="text-sm opacity-90">{selectedSpace.description}</p>
          </div>
        </div>

        {/* 空间信息栏 */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-6 text-sm text-gray-600">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                selectedSpace.privacy === 'public' ? 'bg-green-100 text-green-800' :
                selectedSpace.privacy === 'searchable' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {selectedSpace.privacy === 'public' ? t('collab.privacy.public') : 
                 selectedSpace.privacy === 'searchable' ? t('collab.privacy.searchable') : t('collab.privacy.private')}
              </span>
              {!selectedSpace.isCreator && (
                <span>{t('collab.creator.label')}: {selectedSpace.creator}</span>
              )}
              <div className="flex items-center gap-1">
                <UserGroupIcon className="w-4 h-4" />
                <span>{selectedSpace.memberCount} {t('collab.members.count')}</span>
              </div>
              {/* 成员头像 */}
              <div className="flex -space-x-1">
                {selectedSpace.memberAvatars.map((avatar, index) => (
                  <img
                    key={index}
                    src={avatar}
                    alt=""
                    className="w-6 h-6 rounded-full border-2 border-white"
                  />
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              {selectedSpace.isCreator && (
                <button
                  onClick={handleInviteMember}
                  className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  <PlusIcon className="w-4 h-4" />
                  {t('collab.invite')}
                </button>
              )}
              <button
                onClick={handleShareSpace}
                className="flex items-center gap-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
              >
                <ShareIcon className="w-4 h-4" />
                {t('collab.share.space')}
              </button>
            </div>
          </div>
        </div>

        {/* Tab导航和操作按钮 */}
        <div className="px-6 py-4 border-b">
          <div className="flex flex-col gap-4">
            {/* Tab导航 */}
            <nav className="flex space-x-6">
              {[
                { key: 'discussion', label: t('collab.tabs.discussion'), icon: ChatBubbleLeftRightIcon },
                { key: 'activity', label: t('collab.tabs.activity'), icon: ClockIcon },
                { key: 'media', label: t('collab.tabs.media'), icon: FolderIcon },
                { key: 'members', label: t('collab.tabs.members'), icon: UserGroupIcon }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSpaceDetailTab(tab.key as any)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                    spaceDetailTab === tab.key
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span className="text-sm">{tab.label}</span>
                </button>
              ))}
            </nav>

            {/* 操作按钮 - 单独一行 */}
            <div className="flex gap-2 flex-wrap justify-end">
              <button 
                onClick={handleSignContract}
                className="flex items-center gap-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
              >
                <DocumentCheckIcon className="w-4 h-4" />
                {t('collab.sign.contract')}
              </button>
              <button 
                onClick={handleOpenDAW}
                className="flex items-center gap-1 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
              >
                <MusicalNoteIcon className="w-4 h-4" />
                {t('collab.daw')}
              </button>
              <button 
                onClick={handleCopyrightContract}
                className="flex items-center gap-1 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm"
              >
                <DocumentTextIcon className="w-4 h-4" />
                {t('collab.copyright.contract')}
              </button>
              
              <button
                onClick={() => {
                  // Check if space has created works
                  const hasWorks = selectedSpace && selectedSpace.memberCount > 2; // Mock condition
                  if (hasWorks) {
                    navigate('/crowdfunding/apply');
                  } else {
                    alert(language === 'zh' ? '需要先创作作品才能申请众筹' : 'Need to create works before applying for crowdfunding');
                  }
                }}
                disabled={!selectedSpace || selectedSpace.memberCount <= 2}
                className="flex items-center gap-1 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RocketLaunchIcon className="w-4 h-4" />
                {language === 'zh' ? '申请众筹' : 'Apply for Crowdfunding'}
              </button>
              
              <button
                onClick={async () => {
                  setIsPublishing(true);
                  try {
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    alert(`《${selectedSpace.name}》${t('collab.success.publish')}`)
                  } catch (error) {
                    alert(t('collab.error.publish'));
                  } finally {
                    setIsPublishing(false);
                  }
                }}
                disabled={isPublishing}
                className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <PlusIcon className="w-4 h-4" />
                {isPublishing ? t('collab.publish.progress') : t('collab.publish')}
              </button>
              
              <button
                onClick={async () => {
                  setIsCreatingNFT(true);
                  try {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    const nftId = Date.now();
                    alert(`${t('collab.success.nft')}\n\n作品: ${selectedSpace.name}\nNFT ID: ${nftId}\n\n${t('collab.nft.details')}`);
                  } catch (error) {
                    alert(t('collab.error.nft'));
                  } finally {
                    setIsCreatingNFT(false);
                  }
                }}
                disabled={isCreatingNFT}
                className="flex items-center gap-1 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ShareIcon className="w-4 h-4" />
                {isCreatingNFT ? t('collab.nft.creating') : t('collab.nft.create')}
              </button>
            </div>
          </div>
        </div>

        {/* Tab内容 */}
        <div className="p-6">
          {spaceDetailTab === 'discussion' && (
            <div className="text-center py-12 text-gray-500">
              <ChatBubbleLeftRightIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>{t('collab.no.discussion')}</p>
            </div>
          )}
          {spaceDetailTab === 'activity' && (
            <div className="text-center py-12 text-gray-500">
              <ClockIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>{t('collab.no.activity')}</p>
            </div>
          )}
          {spaceDetailTab === 'media' && (
            <div className="text-center py-12 text-gray-500">
              <FolderIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>{t('collab.no.media')}</p>
            </div>
          )}
          {spaceDetailTab === 'members' && (
            <div className="text-center py-12 text-gray-500">
              <UserGroupIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>{t('collab.members.loading')}</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* 左侧菜单 */}
      <div className="w-80 bg-white shadow-sm border-r min-h-screen">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">{t('collab.my.spaces.title')}</h1>
          
          <nav className="space-y-2">
            <button
              onClick={() => {setActiveTab('created'); setSelectedSpace(null);}}
              className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                activeTab === 'created'
                  ? 'bg-blue-50 text-blue-700 border border-blue-200'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              {t('collab.created.spaces.title')} ({createdSpaces.length})
            </button>
            
            <button
              onClick={() => {setActiveTab('joined'); setSelectedSpace(null);}}
              className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                activeTab === 'joined'
                  ? 'bg-blue-50 text-blue-700 border border-blue-200'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              {t('collab.joined.spaces.title')} ({joinedSpaces.length})
            </button>
            
            <button
              onClick={() => {setActiveTab('signed-contracts'); setSelectedSpace(null);}}
              className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                activeTab === 'signed-contracts'
                  ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              {t('collab.signed.contracts.title')} ({contracts.filter(c => c.type === 'signed').length})
            </button>
            
            <button
              onClick={() => {setActiveTab('pending-contracts'); setSelectedSpace(null);}}
              className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                activeTab === 'pending-contracts'
                  ? 'bg-blue-50 text-blue-700 border border-blue-200'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              {t('collab.pending.contracts.title')} ({contracts.filter(c => c.type === 'pending').length})
            </button>
            
            <button
              onClick={() => {setActiveTab('my-contracts'); setSelectedSpace(null);}}
              className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                activeTab === 'my-contracts'
                  ? 'bg-blue-50 text-blue-700 border border-blue-200'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              {t('collab.copyright.contracts.title')} (2)
            </button>
          </nav>
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className="flex-1 p-6">
        {selectedSpace ? (
          renderSpaceDetail()
        ) : activeTab === '' ? (
          <div className="text-center py-20">
            <div className="max-w-md mx-auto">
              <UserGroupIcon className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">{t('collab.welcome.title')}</h2>
              <p className="text-gray-600 mb-6">
                {t('collab.welcome.desc')}
              </p>
              <p className="text-sm text-gray-500">
                {t('collab.welcome.instruction')}
              </p>
            </div>
          </div>
        ) : (
          <div>
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                {activeTab === 'created' ? t('collab.created.spaces.title') :
                 activeTab === 'joined' ? t('collab.joined.spaces.title') : 
                 activeTab === 'signed-contracts' ? t('collab.signed.contracts.title') : 
                 activeTab === 'pending-contracts' ? t('collab.pending.contracts.title') : t('collab.copyright.contracts.title')}
              </h2>
              <p className="text-gray-600">
                {activeTab === 'created' ? t('collab.manage.created') :
                 activeTab === 'joined' ? t('collab.view.joined') : 
                 activeTab === 'signed-contracts' ? t('collab.view.signed.contracts') : 
                 activeTab === 'pending-contracts' ? t('collab.view.pending.contracts') : t('collab.manage.copyright.contracts')}
              </p>
            </div>

            {activeTab === 'created' && renderSpaceList(createdSpaces)}
            {activeTab === 'joined' && renderSpaceList(joinedSpaces)}
            {activeTab === 'signed-contracts' && renderContractList()}
            {activeTab === 'pending-contracts' && renderContractList()}
            {activeTab === 'my-contracts' && renderMyCreatedContracts()}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyCollaborationSpaces;