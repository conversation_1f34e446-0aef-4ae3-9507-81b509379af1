import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as Tone from 'tone';
import Mixer from './Mixer';
import Sequencer from './Sequencer';
import PianoRoll from './PianoRoll';
import WaveformEditor from './WaveformEditor';
import AutomationEditor from './AutomationEditor';
import RecordingManager from './RecordingManager';
import Transport from './Transport';
import TrackList from './TrackList';
import EffectsPanel from './EffectsPanel';
import ProjectManager from './ProjectManager';
import { dawSocketService } from '../../services/dawSocket';
import { dawApiService, Project } from '../../services/dawApi';
import { useUser } from '../../contexts/UserContext';
import { useLanguage } from '../../contexts/LanguageContext';
import SimpleProAudioPanel from './effects/SimpleProAudioPanel';
import './DAW.css';

// Audio initialization utility functions
const ensureAudioContext = async () => {
  try {
    if (Tone.context.state !== 'running') {
      await Tone.start();
      console.log('Audio context started');
    }
    return true;
  } catch (error) {
    console.error('Failed to start audio context:', error);
    return false;
  }
};

// Safely create instruments
const createSafeInstrument = async () => {
  try {
    await ensureAudioContext();
    const instrument = new Tone.PolySynth(Tone.Synth, {
      oscillator: {
        type: "triangle"
      },
      envelope: {
        attack: 0.02,
        decay: 0.1,
        sustain: 0.3,
        release: 1
      }
    }).toDestination();
    return instrument;
  } catch (error) {
    console.error('Failed to create instrument:', error);
    return null;
  }
};

interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  instrument?: any; // Tone instrument instance
  effects: any[]; // Tone effect instances
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
}

interface Clip {
  id: string;
  trackId: string;
  start: number;
  duration: number;
  name: string;
  color: string;
  notes?: Array<{
    note: string;
    time: number;
    duration: number;
    velocity: number;
  }>;
}

const DAW: React.FC = () => {
  const { user } = useUser();
  const { t } = useLanguage();
  // Create mock user if no logged-in user
  const effectiveUser = user || {
    id: 'demo-user',
    name: 'Demo User',
    email: '<EMAIL>',
    role: 'musician' as const,
    verified: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [tracks, setTracks] = useState<Track[]>([]);
  const [clips, setClips] = useState<Clip[]>([]);
  const [selectedTrack, setSelectedTrack] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [tempo, setTempo] = useState(120);
  const [currentTime, setCurrentTime] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [showProjectManager, setShowProjectManager] = useState(true);
  const [collaborators, setCollaborators] = useState<any[]>([]);
  const [scheduledEvents, setScheduledEvents] = useState<number[]>([]);
  const [trackNotes, setTrackNotes] = useState<{ [trackId: string]: any[] }>({});
  const [isConnectedToAPI, setIsConnectedToAPI] = useState(false);
  const [sequencerHeight, setSequencerHeight] = useState(60); // percentage
  const [isDragging, setIsDragging] = useState(false);
  const [showAutomation, setShowAutomation] = useState(true); // Control automation panel display
  const containerRef = useRef<HTMLDivElement>(null);

  // Check API connection status
  useEffect(() => {
    const checkAPIConnection = async () => {
      const connected = await dawApiService.checkConnection();
      setIsConnectedToAPI(connected);
    };
    
    checkAPIConnection();
    
    // Listen for connection status changes
    const unsubscribe = dawApiService.onConnectionChange(setIsConnectedToAPI);
    
    // Periodically check connection status
    const connectionTimer = setInterval(checkAPIConnection, 30000); // Check every 30 seconds
    
    return () => {
      unsubscribe();
      clearInterval(connectionTimer);
    };
  }, []);

  // Initialize audio and WebSocket connections
  useEffect(() => {
    const initAudio = async () => {
      await ensureAudioContext();
      
      // Ensure master volume is set correctly
      try {
        Tone.Destination.volume.value = 0; // 0 dB
        console.log('Master volume set to 0 dB');
      } catch (error) {
        console.error('Failed to set master volume:', error);
      }
    };

    initAudio();

    // Set up WebSocket event listeners
    if (currentProject && effectiveUser) {
      dawSocketService.connect(currentProject._id!, effectiveUser.id, effectiveUser.name);
      
      dawSocketService.on('project-joined', (data: any) => {
        setCollaborators(data.activeUsers);
      });
      
      dawSocketService.on('user-joined', (data: any) => {
        setCollaborators(data.activeUsers);
      });
      
      dawSocketService.on('user-left', (data: any) => {
        setCollaborators(data.activeUsers);
      });
      
      dawSocketService.on('track-added', (data: any) => {
        if (data.userId !== effectiveUser.id) {
          // Track added by other users
          addTrackFromRemote(data.track);
        }
      });
      
      dawSocketService.on('track-updated', (data: any) => {
        if (data.userId !== effectiveUser.id) {
          updateTrackFromRemote(data.trackId, data.updates);
        }
      });
      
      dawSocketService.on('transport-changed', (data: any) => {
        if (data.userId !== effectiveUser.id) {
          setIsPlaying(data.transportState.isPlaying);
          setTempo(data.transportState.tempo);
        }
      });
    }

    return () => {
      Tone.Transport.stop();
      Tone.Transport.cancel();
      dawSocketService.disconnect();
    };
  }, [currentProject, effectiveUser]);

  useEffect(() => {
    Tone.Transport.bpm.value = tempo;
  }, [tempo]);

  // Listen for playback time updates
  useEffect(() => {
    let animationFrame: number;
    
    const updateTime = () => {
      if (isPlaying) {
        const currentSeconds = Tone.Transport.seconds;
        setCurrentTime(currentSeconds);
        animationFrame = requestAnimationFrame(updateTime);
      }
    };
    
    if (isPlaying) {
      updateTime();
    }
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying]);

  // Initialize example clips data - temporarily disabled for testing
  // useEffect(() => {
  //   if (tracks.length > 0 && clips.length === 0) {
  //     const exampleClips: Clip[] = [
  //       {
  //         id: 'clip-1',
  //         trackId: tracks[0]?.id || '',
  //         start: 0,
  //         duration: 4,
  //         name: 'Bass Pattern',
  //         color: '#00d4ff',
  //         notes: [
  //           { note: 'C2', time: 0, duration: 0.5, velocity: 0.8 },
  //           { note: 'C2', time: 1, duration: 0.5, velocity: 0.7 },
  //           { note: 'F2', time: 2, duration: 0.5, velocity: 0.8 },
  //           { note: 'G2', time: 3, duration: 0.5, velocity: 0.7 }
  //         ]
  //       }
  //     ];
      
  //     if (tracks[1]) {
  //       exampleClips.push({
  //         id: 'clip-2',
  //         trackId: tracks[1].id,
  //         start: 0,
  //         duration: 4,
  //         name: 'Lead Melody',
  //         color: '#ff6b6b',
  //         notes: [
  //           { note: 'C4', time: 0, duration: 1, velocity: 0.6 },
  //           { note: 'E4', time: 1, duration: 1, velocity: 0.7 },
  //           { note: 'G4', time: 2, duration: 1, velocity: 0.8 },
  //           { note: 'C5', time: 3, duration: 1, velocity: 0.9 }
  //         ]
  //       });
  //     }
      
  //     setClips(exampleClips);
  //     console.log('Created example clips:', exampleClips);
  //   }
  // }, [tracks, clips.length]);

  // Remote update handler functions
  const addTrackFromRemote = useCallback((track: Track) => {
    setTracks(prev => [...prev, track]);
  }, []);

  const updateTrackFromRemote = useCallback((trackId: string, updates: Partial<Track>) => {
    setTracks(prev => prev.map(track => 
      track.id === trackId ? { ...track, ...updates } : track
    ));
  }, []);

  // Schedule clips playback
  const scheduleClips = useCallback(() => {
    // Clear previous events
    scheduledEvents.forEach(eventId => {
      Tone.Transport.clear(eventId);
    });
    setScheduledEvents([]);

    const newEvents: number[] = [];
    
    console.log(`Scheduling ${clips.length} clips for playback`);
    
    if (clips.length === 0) {
      console.log('No clips to schedule');
      return;
    }
    
    // Set loop length (based on the longest clip, minimum 8 beats)
    const clipEnds = clips.map(clip => clip.start + clip.duration);
    const maxClipEnd = Math.max(8, ...clipEnds);
    Tone.Transport.loopEnd = maxClipEnd;
    Tone.Transport.loop = true;
    
    console.log(`Setting loop length to ${maxClipEnd} seconds for ${clips.length} clips`);

    // 为每个clip创建独立的事件，但使用更精确的时间间隔避免冲突
    clips.forEach((clip, clipIndex) => {
      const track = tracks.find(t => t.id === clip.trackId);
      console.log(`Processing clip ${clipIndex + 1}/${clips.length}: ${clip.name} on track ${track?.name}`);
      
      if (!track) {
        console.log(`Track not found for clip ${clip.id}`);
        return;
      }
      
      if (!track.instrument) {
        console.log(`No instrument found for track ${track.name}`);
        return;
      }
      
      if (track.muted) {
        console.log(`Track ${track.name} is muted, skipping`);
        return;
      }

      if (clip.notes && clip.notes.length > 0) {
        console.log(`Clip ${clip.name} has ${clip.notes.length} notes`);
        
        // 为每个音符单独调度，使用精确的开始时间避免冲突
        clip.notes.forEach((note, noteIndex) => {
          // 计算精确的开始时间：clip开始时间 + 音符偏移时间 + 微小的索引偏移避免冲突
          const noteStartTime = clip.start + note.time + (noteIndex * 0.001); // 1ms偏移避免冲突
          
          const eventId = Tone.Transport.scheduleRepeat((time) => {
            if (!track.muted && track.instrument && !track.instrument.disposed) {
              try {
                console.log(`Playing note ${note.note} on track ${track.name} at time ${time}`);
                track.instrument.triggerAttackRelease(
                  note.note, 
                  note.duration, 
                  time, // 使用Tone.js提供的精确时间
                  note.velocity * track.volume
                );
              } catch (error) {
                console.warn(`Error playing note on ${track.name}:`, error);
              }
            }
          }, maxClipEnd, noteStartTime);
          
          newEvents.push(eventId);
        });
        
      } else {
        console.log(`Clip ${clip.name} has no notes, using default pattern`);
        // 为没有音符的clip创建默认模式，使用微小偏移避免时间冲突
        const defaultStartTime = clip.start + (clipIndex * 0.001); // 每个clip微小偏移
        const eventId = Tone.Transport.scheduleRepeat((time) => {
          if (!track.muted && track.instrument && !track.instrument.disposed) {
            try {
              console.log(`Playing default note on track ${track.name} at time ${time}`);
              track.instrument.triggerAttackRelease('C4', '8n', time, 0.7 * track.volume);
            } catch (error) {
              console.warn(`Error playing default note on ${track.name}:`, error);
            }
          }
        }, '4n', defaultStartTime);
        
        newEvents.push(eventId);
      }
    });

    setScheduledEvents(newEvents);
    console.log(`Successfully scheduled ${newEvents.length} events for ${clips.length} clips`);
  }, [clips, tracks, scheduledEvents]);

  // Test instrument function
  const testInstrument = async (trackId: string) => {
    console.log('=== TESTING INSTRUMENT ===');
    console.log('Audio Context State:', Tone.context.state);
    console.log('Audio Context Current Time:', Tone.context.currentTime);
    
    // Ensure audio context is running
    if (Tone.context.state !== 'running') {
      console.log('Starting audio context...');
      try {
        await Tone.start();
        console.log('Audio context started successfully');
      } catch (error) {
        console.error('Failed to start audio context:', error);
        return;
      }
    }
    
    const track = tracks.find(t => t.id === trackId);
    if (!track || !track.instrument) {
      console.log('No instrument found for track:', trackId);
      return;
    }
    
    // 检查合成器是否已经被dispose
    if (track.instrument.disposed) {
      console.log('Instrument already disposed for track:', trackId);
      return;
    }
    
    console.log(`Testing instrument for track: ${track.name}`);
    console.log('Instrument type:', track.instrument.constructor.name);
    console.log('Instrument volume:', track.instrument.volume?.value);
    console.log('Track volume:', track.volume);
    console.log('Track muted:', track.muted);
    
    if (track.muted) {
      console.log('Track is muted, unmuting for test...');
      updateTrack(track.id, { muted: false });
    }
    
    try {
      console.log('Triggering note C4...');
      if (!track.instrument.disposed) {
        track.instrument.triggerAttackRelease('C4', '4n');
      }
      
      setTimeout(() => {
        if (!track.instrument.disposed) {
          console.log('Triggering note E4...');
          track.instrument.triggerAttackRelease('E4', '4n');
        }
      }, 500);
      
      setTimeout(() => {
        if (!track.instrument.disposed) {
          console.log('Triggering note G4...');
          track.instrument.triggerAttackRelease('G4', '4n');
        }
      }, 1000);
      
      console.log('All notes triggered');
    } catch (error) {
      console.error('Error triggering notes:', error);
    }
  };

  // Transport controls
  const handlePlay = async () => {
    const newIsPlaying = !isPlaying;
    
    try {
      // Ensure audio context is started
      if (Tone.context.state !== 'running') {
        await Tone.start();
        console.log('Audio context resumed');
      }
      
      setIsPlaying(newIsPlaying);
      
      if (newIsPlaying) {
        console.log('Starting transport...');
        // Schedule clips playback
        scheduleClips();
        Tone.Transport.start();
      } else {
        console.log('Pausing transport...');
        Tone.Transport.pause();
      }
      
      // Broadcast transport state change
      dawSocketService.updateTransport({
        isPlaying: newIsPlaying,
        tempo,
        currentTime
      });
    } catch (error) {
      console.error('Error during play/pause:', error);
    }
  };

  const handleStop = () => {
    console.log('Stopping transport...');
    setIsPlaying(false);
    setCurrentTime(0);
    
    // Clear all scheduled events
    scheduledEvents.forEach(eventId => {
      Tone.Transport.clear(eventId);
    });
    setScheduledEvents([]);
    
    Tone.Transport.stop();
    Tone.Transport.position = 0;
    
    dawSocketService.updateTransport({
      isPlaying: false,
      tempo,
      currentTime: 0
    });
  };

  const handleRecord = () => {
    // Recording state will be managed by RecordingManager
    // This is just for Transport UI feedback
    setIsRecording(!isRecording);
  };

  const handleRecordingStateChange = (recording: boolean) => {
    setIsRecording(recording);
  };

  const handleTempoChange = (newTempo: number) => {
    setTempo(newTempo);
    Tone.Transport.bpm.value = newTempo;
    
    dawSocketService.updateTransport({
      isPlaying,
      tempo: newTempo,
      currentTime
    });
  };

  // Drag divider handling
  const handleDividerMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      const workspaceElement = containerRef.current.querySelector('.daw-workspace') as HTMLElement;
      if (!workspaceElement) return;
      
      const workspaceRect = workspaceElement.getBoundingClientRect();
      const relativeY = e.clientY - workspaceRect.top;
      const percentage = Math.max(10, Math.min(90, (relativeY / workspaceRect.height) * 100));
      
      setSequencerHeight(percentage);
    };
    
    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Create different types of instruments
  const createInstrument = async (instrumentType: string) => {
    try {
      await ensureAudioContext();
      
      let instrument;
      console.log(`Creating instrument of type: ${instrumentType}`);
      
      switch (instrumentType) {
        // Keyboard & Synth
        case 'piano':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "triangle" },
            envelope: { attack: 0.02, decay: 0.3, sustain: 0.7, release: 1.2 }
          }).toDestination();
          break;
        case 'electricpiano':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "triangle4" },
            envelope: { attack: 0.01, decay: 0.5, sustain: 0.6, release: 1.0 }
          }).toDestination();
          break;
        case 'organ':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sine8" },
            envelope: { attack: 0.1, decay: 0.1, sustain: 0.9, release: 0.5 }
          }).toDestination();
          break;
        case 'synth':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "square" },
            envelope: { attack: 0.1, decay: 0.2, sustain: 0.6, release: 0.8 }
          }).toDestination();
          break;
        case 'pad':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sawtooth8" },
            envelope: { attack: 0.5, decay: 0.3, sustain: 0.8, release: 2.0 }
          }).toDestination();
          break;
        case 'lead':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sawtooth" },
            envelope: { attack: 0.01, decay: 0.1, sustain: 0.7, release: 0.5 }
          }).toDestination();
          break;
        case 'arpeggiator':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "pulse" },
            envelope: { attack: 0.001, decay: 0.2, sustain: 0.3, release: 0.3 }
          }).toDestination();
          break;
          
        // Strings
        case 'guitar':
        case 'acousticguitar':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sawtooth" },
            envelope: { attack: 0.01, decay: 0.2, sustain: 0.5, release: 0.8 }
          }).toDestination();
          break;
        case 'electricguitar':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "square" },
            envelope: { attack: 0.001, decay: 0.3, sustain: 0.6, release: 1.0 }
          }).toDestination();
          break;
        case 'bass':
        case 'electricbass':
        case 'upright_bass':
          instrument = new Tone.MonoSynth({
            oscillator: { type: "square" },
            envelope: { attack: 0.01, decay: 0.1, sustain: 0.8, release: 0.5 }
          }).toDestination();
          break;
        case 'bass_synth':
          instrument = new Tone.MonoSynth({
            oscillator: { type: "sawtooth" },
            envelope: { attack: 0.01, decay: 0.2, sustain: 0.7, release: 0.6 }
          }).toDestination();
          break;
        case 'violin':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sawtooth" },
            envelope: { attack: 0.2, decay: 0.1, sustain: 0.9, release: 1.5 }
          }).toDestination();
          break;
        case 'cello':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sawtooth4" },
            envelope: { attack: 0.3, decay: 0.2, sustain: 0.8, release: 2.0 }
          }).toDestination();
          break;
        case 'harp':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sine" },
            envelope: { attack: 0.01, decay: 1.0, sustain: 0.1, release: 1.5 }
          }).toDestination();
          break;
        case 'banjo':
        case 'mandolin':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "triangle" },
            envelope: { attack: 0.001, decay: 0.5, sustain: 0.2, release: 0.5 }
          }).toDestination();
          break;
        case 'string_ensemble':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sawtooth8" },
            envelope: { attack: 0.3, decay: 0.2, sustain: 0.9, release: 1.8 }
          }).toDestination();
          break;
          
        // Winds
        case 'trumpet':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sine" },
            envelope: { attack: 0.1, decay: 0.2, sustain: 0.8, release: 1.0 }
          }).toDestination();
          break;
        case 'french_horn':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sine4" },
            envelope: { attack: 0.15, decay: 0.2, sustain: 0.7, release: 1.2 }
          }).toDestination();
          break;
        case 'trombone':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "square" },
            envelope: { attack: 0.1, decay: 0.3, sustain: 0.8, release: 1.0 }
          }).toDestination();
          break;
        case 'tuba':
          instrument = new Tone.MonoSynth({
            oscillator: { type: "sine" },
            envelope: { attack: 0.15, decay: 0.3, sustain: 0.9, release: 1.5 }
          }).toDestination();
          break;
        case 'flute':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sine2" },
            envelope: { attack: 0.05, decay: 0.1, sustain: 0.8, release: 0.8 }
          }).toDestination();
          break;
        case 'piccolo':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sine" },
            envelope: { attack: 0.02, decay: 0.1, sustain: 0.7, release: 0.6 }
          }).toDestination();
          break;
        case 'oboe':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "triangle" },
            envelope: { attack: 0.08, decay: 0.2, sustain: 0.8, release: 1.0 }
          }).toDestination();
          break;
        case 'clarinet':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "square" },
            envelope: { attack: 0.05, decay: 0.1, sustain: 0.9, release: 1.2 }
          }).toDestination();
          break;
        case 'saxophone':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sawtooth" },
            envelope: { attack: 0.05, decay: 0.2, sustain: 0.8, release: 1.0 }
          }).toDestination();
          break;
          
        // Percussion
        case 'drums':
          instrument = new Tone.MembraneSynth({
            pitchDecay: 0.1,
            octaves: 8,
            oscillator: { type: "sine" },
            envelope: { attack: 0.001, decay: 0.3, sustain: 0.01, release: 0.8 }
          }).toDestination();
          break;
        case 'timpani':
          instrument = new Tone.MembraneSynth({
            pitchDecay: 0.05,
            octaves: 4,
            oscillator: { type: "sine" },
            envelope: { attack: 0.01, decay: 0.5, sustain: 0.1, release: 1.5 }
          }).toDestination();
          break;
        case 'marimba':
        case 'vibraphone':
        case 'xylophone':
          instrument = new Tone.PolySynth(Tone.Synth, {
            oscillator: { type: "sine" },
            envelope: { attack: 0.001, decay: 0.8, sustain: 0.1, release: 1.0 }
          }).toDestination();
          break;
          
        default:
          console.log(`Unknown instrument type: ${instrumentType}, creating default instrument`);
          instrument = await createSafeInstrument();
      }
      
      // Set volume for all instruments
      if (instrument && instrument.volume) {
        instrument.volume.value = 0; // 0 dB volume
      }
      
      console.log(`Created ${instrumentType} instrument successfully:`, instrument?.constructor?.name);
      return instrument;
    } catch (error) {
      console.error(`❌ Failed to create ${instrumentType} instrument:`, error);
      console.log('Falling back to safe instrument');
      return await createSafeInstrument();
    }
  };

  // Track operations
  const addTrack = async (type: 'audio' | 'midi', instrumentType: string = 'synth') => {
    try {
      let instrument = undefined;
      let trackName = '';
      
      if (type === 'midi') {
        console.log(`=== CREATING TRACK WITH INSTRUMENT: ${instrumentType} ===`);
        instrument = await createInstrument(instrumentType);
        
        // Instrument name mapping
        const instrumentNames = {
          // Keyboard & Synth
          piano: t('daw.instruments.piano'),
          electricpiano: t('daw.instruments.electricpiano'),
          organ: t('daw.instruments.organ'),
          synth: t('daw.instruments.synth'),
          pad: t('daw.instruments.pad'),
          lead: t('daw.instruments.lead'),
          arpeggiator: t('daw.instruments.arpeggiator'),
          // Strings
          guitar: t('daw.instruments.guitar'),
          electricguitar: t('daw.instruments.electricguitar'),
          acousticguitar: t('daw.instruments.acousticguitar'),
          bass: t('daw.instruments.bass'),
          electricbass: t('daw.instruments.electricbass'),
          upright_bass: t('daw.instruments.upright_bass'),
          bass_synth: t('daw.instruments.bass_synth'),
          violin: t('daw.instruments.violin'),
          cello: t('daw.instruments.cello'),
          harp: t('daw.instruments.harp'),
          banjo: t('daw.instruments.banjo'),
          mandolin: t('daw.instruments.mandolin'),
          string_ensemble: t('daw.instruments.string_ensemble'),
          // Winds
          trumpet: t('daw.instruments.trumpet'),
          french_horn: t('daw.instruments.french_horn'),
          trombone: t('daw.instruments.trombone'),
          tuba: t('daw.instruments.tuba'),
          flute: t('daw.instruments.flute'),
          piccolo: t('daw.instruments.piccolo'),
          oboe: t('daw.instruments.oboe'),
          clarinet: t('daw.instruments.clarinet'),
          saxophone: t('daw.instruments.saxophone'),
          // Percussion
          drums: t('daw.instruments.drums'),
          timpani: t('daw.instruments.timpani'),
          marimba: t('daw.instruments.marimba'),
          vibraphone: t('daw.instruments.vibraphone'),
          xylophone: t('daw.instruments.xylophone')
        };
        trackName = instrumentNames[instrumentType as keyof typeof instrumentNames] || t('daw.instruments.synth');
        
        if (instrument) {
          console.log(`✅ Successfully created ${instrumentType} instrument:`, instrument.constructor.name);
          console.log(`Instrument volume:`, instrument.volume?.value || 'N/A');
        } else {
          console.error(`❌ Failed to create ${instrumentType} instrument`);
        }
      } else {
        trackName = t('daw.track.audio');
      }
      
      const newTrack: Track = {
        id: `track-${Date.now()}`,
        name: `${trackName} ${tracks.filter(t => t.type === type).length + 1}`,
        type,
        instrument,
        effects: [],
        volume: 0.8,
        pan: 0,
        muted: false,
        solo: false,
        armed: false
      };
      
      console.log('Adding track:', newTrack);
      setTracks([...tracks, newTrack]);
      setSelectedTrack(newTrack.id);
      
      // Broadcast track addition
      dawSocketService.addTrack(newTrack);
    } catch (error) {
      console.error('Failed to add track:', error);
    }
  };

  const updateTrack = (trackId: string, updates: Partial<Track>) => {
    setTracks(tracks.map(track => 
      track.id === trackId ? { ...track, ...updates } : track
    ));
    
    // Broadcast track update
    dawSocketService.updateTrack(trackId, updates);
  };

  const deleteTrack = (trackId: string) => {
    const track = tracks.find(t => t.id === trackId);
    
    // 首先清理与该track相关的所有scheduled events
    scheduledEvents.forEach(eventId => {
      try {
        Tone.Transport.clear(eventId);
      } catch (error) {
        console.warn('Error clearing scheduled event:', error);
      }
    });
    setScheduledEvents([]);
    
    // 删除track相关的clips
    const trackClips = clips.filter(clip => clip.trackId === trackId);
    setClips(prev => prev.filter(clip => clip.trackId !== trackId));
    
    // 然后dispose合成器
    if (track?.instrument) {
      try {
        track.instrument.dispose();
      } catch (error) {
        console.warn('Error disposing instrument:', error);
      }
    }
    
    const remainingTracks = tracks.filter(t => t.id !== trackId);
    setTracks(remainingTracks);
    
    if (selectedTrack === trackId) {
      // If deleting currently selected track, select first remaining track, or null if none remain
      setSelectedTrack(remainingTracks.length > 0 ? remainingTracks[0].id : null);
    }
    
    // Delete track-related notes
    setTrackNotes(prev => {
      const newNotes = { ...prev };
      delete newNotes[trackId];
      return newNotes;
    });
    
    // 重新调度剩余clips的播放
    setTimeout(() => {
      scheduleClips();
    }, 100);
    
    // Broadcast track deletion
    dawSocketService.deleteTrack(trackId);
  };
  
  // Update track notes handler function
  const handleUpdateNotes = (trackId: string, notes: any[]) => {
    setTrackNotes(prev => ({
      ...prev,
      [trackId]: notes
    }));
    console.log(`Updated notes for track ${trackId}:`, notes);
    
    // Auto-save project
    setTimeout(() => {
      handleSaveProject();
    }, 1000);
  };

  // Auto-save effect - when tracks or clips change
  useEffect(() => {
    if (currentProject && tracks.length > 0) {
      const saveTimer = setTimeout(() => {
        handleSaveProject();
      }, 5000); // Increase delay to 5 seconds
      
      return () => clearTimeout(saveTimer);
    }
  }, [tracks.length, clips.length]); // Only listen to quantity changes, not content changes

  // Project operations
  const handleProjectSelect = async (project: Project) => {
    try {
      setCurrentProject(project);
      setShowProjectManager(false);
      
      await ensureAudioContext();
      
      // Load project data
      if (project.tracks) {
        const loadedTracks = await Promise.all(
          project.tracks.map(async (track) => {
            let instrument = undefined;
            if (track.type === 'midi') {
              // Infer instrument type based on track name - comprehensive mapping
              const instrumentNameMap = [
                // Keyboard & Synth
                { name: t('daw.instruments.piano'), type: 'piano' },
                { name: t('daw.instruments.electricpiano'), type: 'electricpiano' },
                { name: t('daw.instruments.organ'), type: 'organ' },
                { name: t('daw.instruments.synth'), type: 'synth' },
                { name: t('daw.instruments.pad'), type: 'pad' },
                { name: t('daw.instruments.lead'), type: 'lead' },
                { name: t('daw.instruments.arpeggiator'), type: 'arpeggiator' },
                // Strings
                { name: t('daw.instruments.guitar'), type: 'guitar' },
                { name: t('daw.instruments.electricguitar'), type: 'electricguitar' },
                { name: t('daw.instruments.acousticguitar'), type: 'acousticguitar' },
                { name: t('daw.instruments.bass'), type: 'bass' },
                { name: t('daw.instruments.electricbass'), type: 'electricbass' },
                { name: t('daw.instruments.upright_bass'), type: 'upright_bass' },
                { name: t('daw.instruments.bass_synth'), type: 'bass_synth' },
                { name: t('daw.instruments.violin'), type: 'violin' },
                { name: t('daw.instruments.cello'), type: 'cello' },
                { name: t('daw.instruments.harp'), type: 'harp' },
                { name: t('daw.instruments.banjo'), type: 'banjo' },
                { name: t('daw.instruments.mandolin'), type: 'mandolin' },
                { name: t('daw.instruments.string_ensemble'), type: 'string_ensemble' },
                // Winds
                { name: t('daw.instruments.trumpet'), type: 'trumpet' },
                { name: t('daw.instruments.french_horn'), type: 'french_horn' },
                { name: t('daw.instruments.trombone'), type: 'trombone' },
                { name: t('daw.instruments.tuba'), type: 'tuba' },
                { name: t('daw.instruments.flute'), type: 'flute' },
                { name: t('daw.instruments.piccolo'), type: 'piccolo' },
                { name: t('daw.instruments.oboe'), type: 'oboe' },
                { name: t('daw.instruments.clarinet'), type: 'clarinet' },
                { name: t('daw.instruments.saxophone'), type: 'saxophone' },
                // Percussion
                { name: t('daw.instruments.drums'), type: 'drums' },
                { name: t('daw.instruments.timpani'), type: 'timpani' },
                { name: t('daw.instruments.marimba'), type: 'marimba' },
                { name: t('daw.instruments.vibraphone'), type: 'vibraphone' },
                { name: t('daw.instruments.xylophone'), type: 'xylophone' }
              ];
              
              const instrumentType = instrumentNameMap.find(inst => 
                track.name.includes(inst.name)
              )?.type || 'synth';
              console.log(`Loading track ${track.name} with instrument type: ${instrumentType}`);
              instrument = await createInstrument(instrumentType);
            }
            return {
              ...track,
              instrument
            };
          })
        );
        setTracks(loadedTracks);
        setSelectedTrack(loadedTracks[0]?.id || null);
      }
      
      setTempo(project.tempo);
      Tone.Transport.bpm.value = project.tempo;
    } catch (error) {
      console.error('Failed to load project:', error);
    }
  };

  const handleSaveProject = async () => {
    if (!currentProject || !effectiveUser) return;
    
    try {
      // Prepare complete project data
      const projectData = {
        name: currentProject.name,
        userId: effectiveUser.id,
        tempo,
        timeSignature: 4, // Can add state management
        tracks: tracks.map(({ instrument, ...track }) => ({
          ...track,
          // Save instrument type information using comprehensive mapping
          instrumentType: (() => {
            const instrumentNameMap = [
              // Keyboard & Synth
              { name: t('daw.instruments.piano'), type: 'piano' },
              { name: t('daw.instruments.electricpiano'), type: 'electricpiano' },
              { name: t('daw.instruments.organ'), type: 'organ' },
              { name: t('daw.instruments.synth'), type: 'synth' },
              { name: t('daw.instruments.pad'), type: 'pad' },
              { name: t('daw.instruments.lead'), type: 'lead' },
              { name: t('daw.instruments.arpeggiator'), type: 'arpeggiator' },
              // Strings
              { name: t('daw.instruments.guitar'), type: 'guitar' },
              { name: t('daw.instruments.electricguitar'), type: 'electricguitar' },
              { name: t('daw.instruments.acousticguitar'), type: 'acousticguitar' },
              { name: t('daw.instruments.bass'), type: 'bass' },
              { name: t('daw.instruments.electricbass'), type: 'electricbass' },
              { name: t('daw.instruments.upright_bass'), type: 'upright_bass' },
              { name: t('daw.instruments.bass_synth'), type: 'bass_synth' },
              { name: t('daw.instruments.violin'), type: 'violin' },
              { name: t('daw.instruments.cello'), type: 'cello' },
              { name: t('daw.instruments.harp'), type: 'harp' },
              { name: t('daw.instruments.banjo'), type: 'banjo' },
              { name: t('daw.instruments.mandolin'), type: 'mandolin' },
              { name: t('daw.instruments.string_ensemble'), type: 'string_ensemble' },
              // Winds
              { name: t('daw.instruments.trumpet'), type: 'trumpet' },
              { name: t('daw.instruments.french_horn'), type: 'french_horn' },
              { name: t('daw.instruments.trombone'), type: 'trombone' },
              { name: t('daw.instruments.tuba'), type: 'tuba' },
              { name: t('daw.instruments.flute'), type: 'flute' },
              { name: t('daw.instruments.piccolo'), type: 'piccolo' },
              { name: t('daw.instruments.oboe'), type: 'oboe' },
              { name: t('daw.instruments.clarinet'), type: 'clarinet' },
              { name: t('daw.instruments.saxophone'), type: 'saxophone' },
              // Percussion
              { name: t('daw.instruments.drums'), type: 'drums' },
              { name: t('daw.instruments.timpani'), type: 'timpani' },
              { name: t('daw.instruments.marimba'), type: 'marimba' },
              { name: t('daw.instruments.vibraphone'), type: 'vibraphone' },
              { name: t('daw.instruments.xylophone'), type: 'xylophone' }
            ];
            return instrumentNameMap.find(inst => track.name.includes(inst.name))?.type || 'synth';
          })()
        })),
        clips: clips.map(clip => ({
          ...clip,
          notes: clip.notes || []
        })),
        collaborators: collaborators.map(c => c.userId || c.userName),
        visibility: currentProject.visibility || 'private',
        lastModified: new Date()
      };
      
      console.log('Saving project data:', projectData);
      
      const savedProject = await dawApiService.saveProject(currentProject._id!, projectData);
      
      // Update current project state
      setCurrentProject({
        ...currentProject,
        ...savedProject
      });
      
      console.log('Project saved successfully:', savedProject);
      
      // Show save success notification
      const saveButton = document.querySelector('[data-save-button]') as HTMLElement;
      if (saveButton) {
        const originalText = saveButton.textContent;
        saveButton.textContent = t('daw.saved');
        saveButton.style.backgroundColor = '#10b981';
        setTimeout(() => {
          saveButton.textContent = originalText;
          saveButton.style.backgroundColor = '';
        }, 2000);
      }
      
    } catch (error) {
      console.error('Failed to save project:', error);
      
      // Show save failure notification
      const saveButton = document.querySelector('[data-save-button]') as HTMLElement;
      if (saveButton) {
        const originalText = saveButton.textContent;
        saveButton.textContent = t('daw.save.failed');
        saveButton.style.backgroundColor = '#ef4444';
        setTimeout(() => {
          saveButton.textContent = originalText;
          saveButton.style.backgroundColor = '';
        }, 2000);
      }
    }
  };

  // If no project selected, show project manager
  if (showProjectManager || !currentProject) {
    return (
      <ProjectManager
        onProjectSelect={handleProjectSelect}
        onClose={() => setShowProjectManager(false)}
      />
    );
  }

  return (
    <div className="daw-container" ref={containerRef}>
      <div className="daw-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="daw-title">{t('daw.title')} - {currentProject.name}</h1>
            <button
              onClick={() => setShowProjectManager(true)}
              className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
            >
              {t('daw.projects')}
            </button>
            <button
              onClick={handleSaveProject}
              data-save-button
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors duration-200"
            >
              {t('daw.save.project')}
            </button>
            
            <button
              onClick={() => {
                const allProjects = JSON.parse(localStorage.getItem('dawProjects') || '[]');
                const dataStr = JSON.stringify(allProjects, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = 'daw-projects.json';
                link.click();
                URL.revokeObjectURL(url);
              }}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors duration-200"
            >
              {t('daw.export.data')}
            </button>
            
            <button
              onClick={() => setShowAutomation(!showAutomation)}
              className={`px-3 py-1 rounded text-sm transition-colors duration-200 ${
                showAutomation 
                  ? 'bg-green-600 text-white hover:bg-green-700' 
                  : 'bg-gray-600 text-white hover:bg-gray-700'
              }`}
            >
              {showAutomation ? t('daw.hide.automation') : t('daw.show.automation')}
            </button>
            
            {/* API connection status indicator */}
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${isConnectedToAPI ? 'bg-green-400' : 'bg-red-400'}`}></div>
              <span className="text-xs text-gray-400">
                {isConnectedToAPI ? t('daw.connected') : t('daw.offline')}
              </span>
            </div>
            
            {/* Debug information */}
            <div className="flex items-center gap-2 text-xs text-gray-400">
              <span>{t('daw.tracks')}: {tracks.length} | {t('daw.clips')}: {clips.length} | {t('daw.events')}: {scheduledEvents.length}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Collaborators display */}
            {collaborators.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-400">{t('daw.online')}:</span>
                {collaborators.map((collab, index) => (
                  <div
                    key={index}
                    className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-xs text-white"
                    title={collab.userName}
                  >
                    {collab.userName?.charAt(0).toUpperCase()}
                  </div>
                ))}
              </div>
            )}
            
            <Transport
              isPlaying={isPlaying}
              isRecording={isRecording}
              tempo={tempo}
              currentTime={currentTime}
              onPlay={handlePlay}
              onStop={handleStop}
              onRecord={handleRecord}
              onTempoChange={handleTempoChange}
            />
          </div>
        </div>
      </div>
      
      <div className="daw-main">
        <div className="daw-sidebar">
          <TrackList
            tracks={tracks}
            selectedTrack={selectedTrack}
            onSelectTrack={setSelectedTrack}
            onAddTrack={addTrack}
            onUpdateTrack={updateTrack}
            onDeleteTrack={deleteTrack}
            onTestInstrument={testInstrument}
          />
          
          <RecordingManager
            tracks={tracks}
            selectedTrack={selectedTrack}
            isRecording={isRecording}
            onUpdateTrack={updateTrack}
            onRecordingStateChange={handleRecordingStateChange}
          />
          
          {selectedTrack && (
            <EffectsPanel
              track={tracks.find(t => t.id === selectedTrack)!}
              onUpdateTrack={updateTrack}
            />
          )}
        </div>
        
        <div className="daw-workspace">
          <div 
            className="daw-sequencer-section" 
            style={{ 
              flex: `${sequencerHeight}`,
              minHeight: '200px'
            }}
          >
            <Sequencer
              tracks={tracks}
              clips={clips}
              selectedTrack={selectedTrack}
              isPlaying={isPlaying}
              currentTime={currentTime}
              onUpdateTrack={updateTrack}
              onUpdateClips={setClips}
            />
          </div>
          
          {/* Resizable divider */}
          <div 
            className="workspace-divider"
            onMouseDown={handleDividerMouseDown}
            style={{
              height: '8px',
              background: 'linear-gradient(90deg, #3a3a3a 0%, #4a4a4a 50%, #3a3a3a 100%)',
              cursor: 'row-resize',
              borderTop: '1px solid #5a5a5a',
              borderBottom: '1px solid #5a5a5a',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative'
            }}
          >
            {/* Drag handle dots */}
            <div style={{
              width: '20px',
              height: '3px',
              background: '#666',
              borderRadius: '2px',
              position: 'relative'
            }}>
              <div style={{
                content: '""',
                position: 'absolute',
                top: '-2px',
                left: '0',
                width: '20px',
                height: '1px',
                background: '#666',
                borderRadius: '1px'
              }} />
              <div style={{
                content: '""',
                position: 'absolute',
                bottom: '-2px',
                left: '0',
                width: '20px',
                height: '1px',
                background: '#666',
                borderRadius: '1px'
              }} />
            </div>
          </div>
          
          {selectedTrack && (
            <div 
              className="daw-pianoroll-section"
              style={{ 
                flex: `${100 - sequencerHeight}`,
                minHeight: '200px'
              }}
            >
              {tracks.find(t => t.id === selectedTrack)?.type === 'midi' ? (
                <PianoRoll
                  track={tracks.find(t => t.id === selectedTrack)!}
                  onUpdateTrack={updateTrack}
                  onUpdateNotes={handleUpdateNotes}
                />
              ) : (
                <WaveformEditor
                  track={tracks.find(t => t.id === selectedTrack)!}
                  isPlaying={isPlaying}
                  currentTime={currentTime}
                  onUpdateTrack={updateTrack}
                  onCurrentTimeUpdate={setCurrentTime}
                />
              )}
            </div>
          )}
          
          {/* Automation Editor Section - 可隐藏 */}
          {showAutomation && (
            <div className="daw-automation-section">
              <AutomationEditor
                tracks={tracks}
                selectedTrack={selectedTrack}
                currentTime={currentTime}
                zoom={1}
                onUpdateAutomation={(laneId, points) => {
                  console.log('Automation updated:', laneId, points);
                  // TODO: Apply automation to audio engine
                }}
              />
            </div>
          )}
        </div>
        
        <div className="daw-mixer-section">
          {/* 传统混音器 */}
          <div className="mixer-container" style={{ height: '40%', minHeight: '300px' }}>
            <Mixer
              tracks={tracks}
              selectedTrack={selectedTrack}
              onUpdateTrack={updateTrack}
            />
          </div>
          
          {/* 专业音频控制面板 */}
          <div className="pro-audio-controls" style={{ height: '60%', minHeight: '400px' }}>
            <SimpleProAudioPanel selectedTrack={selectedTrack} tracks={tracks} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DAW;