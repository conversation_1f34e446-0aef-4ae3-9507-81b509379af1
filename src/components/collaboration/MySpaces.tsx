import React, { useState } from 'react';
import { PencilIcon, UserPlusIcon, CameraIcon, EllipsisHorizontalIcon } from '@heroicons/react/24/outline';
import { HeartIcon, ChatBubbleLeftIcon, PaperAirplaneIcon, ShareIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

const MySpaces: React.FC = () => {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('discussion');
  const [showPostModal, setShowPostModal] = useState(false);

  const spaceInfo = {
    name: '轻音乐创作',
    visibility: '公开小组',
    memberCount: 1,
    coverImage: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=800&h=300&fit=crop'
  };

  const tabs = [
    { id: 'discussion', label: t('myspaces.tab.discussion') },
    { id: 'activity', label: t('myspaces.tab.activity') },
    { id: 'content', label: t('myspaces.tab.content') },
    { id: 'files', label: t('myspaces.tab.files') },
    { id: 'members', label: t('myspaces.tab.members') }
  ];

  const quickActions = [
    { id: 'anonymous', label: t('myspaces.quick.anonymous'), icon: '📝', color: 'bg-blue-100 hover:bg-blue-200' },
    { id: 'vote', label: t('myspaces.quick.vote'), icon: '🗳️', color: 'bg-orange-100 hover:bg-orange-200' },
    { id: 'activity', label: t('myspaces.quick.activity'), icon: '🎭', color: 'bg-yellow-100 hover:bg-yellow-200' }
  ];

  const posts = [
    {
      id: 1,
      author: {
        name: 'Fred Yee',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
        role: '创建了小组轻音乐创作'
      },
      time: '17小时前',
      status: '🔴',
      content: '',
      likes: 0,
      comments: 0,
      shares: 0
    }
  ];

  return (
    <div className="max-w-6xl mx-auto">
      {/* 空间封面 */}
      <div className="relative h-80 bg-gradient-to-r from-orange-400 via-pink-400 to-purple-500 rounded-lg overflow-hidden mb-6">
        <img
          src={spaceInfo.coverImage}
          alt={t('myspaces.cover.alt')}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        
        {/* 编辑按钮 */}
        <button className="absolute top-4 right-4 bg-white bg-opacity-90 text-gray-700 px-4 py-2 rounded-lg hover:bg-opacity-100 transition-opacity flex items-center">
          <PencilIcon className="w-4 h-4 mr-2" />
          {t('myspaces.edit')}
        </button>
      </div>

      {/* 空间信息 */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{spaceInfo.name}</h1>
            <div className="flex items-center text-sm text-gray-600 mt-1">
              <span className="mr-2">🌐</span>
              <span>{t('myspaces.public.group')} · {spaceInfo.memberCount}{t('myspaces.members.count')}</span>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
              <UserPlusIcon className="w-4 h-4 mr-2" />
              {t('myspaces.invite')}
            </button>
            <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center">
              <span className="mr-2">📤</span>
              {t('myspaces.share')}
            </button>
          </div>
        </div>

        {/* 空间成员头像 */}
        <div className="flex items-center">
          <img
            src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face"
            alt="成员"
            className="w-8 h-8 rounded-full border-2 border-white"
          />
        </div>
      </div>

      {/* 标签导航 */}
      <div className="bg-white rounded-lg shadow-sm border mb-6">
        <div className="flex border-b">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab.label}
            </button>
          ))}
          
          {/* 右侧按钮 */}
          <div className="ml-auto flex items-center px-6">
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
              {t('myspaces.sign.contract')}
            </button>
            <button className="ml-3 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
              {t('myspaces.daw')}
            </button>
            <button className="ml-3 text-gray-600 hover:text-gray-900">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
            <button className="ml-2 text-gray-600 hover:text-gray-900">
              <EllipsisHorizontalIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* 标签内容 */}
        <div className="p-6">
          {activeTab === 'discussion' && (
            <div>
              {/* 发帖区域 */}
              <div className="flex items-start mb-6">
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face"
                  alt="您的头像"
                  className="w-10 h-10 rounded-full mr-3"
                />
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder={t('myspaces.share.mood')}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    onClick={() => setShowPostModal(true)}
                  />
                </div>
              </div>

              {/* 快捷操作 */}
              <div className="grid grid-cols-3 gap-4 mb-8">
                {quickActions.map((action) => (
                  <button
                    key={action.id}
                    className={`p-4 rounded-lg transition-colors text-center ${action.color}`}
                  >
                    <div className="text-2xl mb-2">{action.icon}</div>
                    <div className="text-sm font-medium text-gray-700">{action.label}</div>
                  </button>
                ))}
              </div>

              {/* 动态列表 */}
              <div className="space-y-4">
                {posts.map((post) => (
                  <div key={post.id} className="bg-gray-50 rounded-lg p-6 border">
                    <div className="flex items-start">
                      <img
                        src={post.author.avatar}
                        alt={post.author.name}
                        className="w-10 h-10 rounded-full mr-3"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="font-semibold text-gray-900">{post.author.name}</span>
                          <span className="text-gray-500 text-sm">{post.author.role}</span>
                          <span className="text-gray-500 text-sm">·</span>
                          <span className="text-gray-500 text-sm">{post.time}</span>
                          <span className="text-red-500">{post.status}</span>
                        </div>
                        
                        {/* 空内容提示 */}
                        {!post.content && (
                          <div className="text-gray-400 italic mb-4">{t('myspaces.no.content')}</div>
                        )}
                        
                        {/* 互动按钮 */}
                        <div className="flex items-center space-x-6 text-gray-500">
                          <button className="flex items-center hover:text-blue-600 transition-colors">
                            <HeartIcon className="w-5 h-5 mr-1" />
                            <span className="text-sm">{t('myspaces.like')}</span>
                          </button>
                          <button className="flex items-center hover:text-blue-600 transition-colors">
                            <ChatBubbleLeftIcon className="w-5 h-5 mr-1" />
                            <span className="text-sm">{t('myspaces.comment')}</span>
                          </button>
                          <button className="flex items-center hover:text-blue-600 transition-colors">
                            <PaperAirplaneIcon className="w-5 h-5 mr-1" />
                            <span className="text-sm">{t('myspaces.send')}</span>
                          </button>
                          <button className="flex items-center hover:text-blue-600 transition-colors">
                            <ShareIcon className="w-5 h-5 mr-1" />
                            <span className="text-sm">{t('myspaces.share.post')}</span>
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    {/* 评论输入 */}
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex items-center">
                        <img
                          src="https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=32&h=32&fit=crop&crop=face"
                          alt="头像"
                          className="w-8 h-8 rounded-full mr-3"
                        />
                        <input
                          type="text"
                          placeholder={t('myspaces.write.comment')}
                          className="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <div className="ml-3 flex space-x-2">
                          <button className="text-gray-400 hover:text-gray-600" title="表情">
                            <span className="text-lg">😊</span>
                          </button>
                          <button className="text-gray-400 hover:text-gray-600" title="图片">
                            <span className="text-lg">📷</span>
                          </button>
                          <button className="text-gray-400 hover:text-gray-600" title="标签">
                            <span className="text-lg">🏷️</span>
                          </button>
                          <button className="text-gray-400 hover:text-gray-600" title="位置">
                            <span className="text-lg">📍</span>
                          </button>
                          <button className="text-gray-400 hover:text-gray-600" title="GIF">
                            <span className="text-sm font-bold">GIF</span>
                          </button>
                          <button className="text-gray-400 hover:text-gray-600" title="表情符号">
                            <span className="text-lg">🎭</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 空状态提示 */}
              <div className="text-center py-12 mt-8">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('myspaces.start.conversation')}</h3>
                <p className="text-gray-600 mb-4">{t('myspaces.share.ideas')}</p>
                <button 
                  onClick={() => setShowPostModal(true)}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {t('myspaces.first.post')}
                </button>
              </div>
            </div>
          )}

          {/* 其他标签的内容 */}
          {activeTab !== 'discussion' && (
            <div className="text-center py-12 text-gray-500">
              {tabs.find(tab => tab.id === activeTab)?.label} {t('myspaces.content.developing')}
            </div>
          )}
        </div>
      </div>

      {/* 发帖弹窗 */}
      {showPostModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={() => setShowPostModal(false)}>
          <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4" onClick={e => e.stopPropagation()}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{t('myspaces.create.post')}</h3>
              <button
                onClick={() => setShowPostModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="flex items-start mb-4">
              <img
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face"
                alt="您的头像"
                className="w-10 h-10 rounded-full mr-3"
              />
              <div className="flex-1">
                <div className="font-medium text-gray-900">Fred Yee</div>
                <div className="text-sm text-gray-500">轻音乐创作</div>
              </div>
            </div>
            
            <textarea
              placeholder={t('myspaces.what.thinking')}
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
            
            <div className="flex items-center justify-between mt-4">
              <div className="flex space-x-3">
                <button className="text-gray-400 hover:text-gray-600" title="添加照片">
                  <CameraIcon className="w-5 h-5" />
                </button>
                <button className="text-gray-400 hover:text-gray-600" title="标记朋友">
                  <UserPlusIcon className="w-5 h-5" />
                </button>
                <button className="text-gray-400 hover:text-gray-600" title="表情">
                  <span className="text-lg">😊</span>
                </button>
              </div>
              <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                {t('myspaces.post')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MySpaces;