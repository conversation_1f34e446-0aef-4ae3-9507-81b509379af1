import React, { useState } from 'react';
import { TrashIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
}

interface TrackListProps {
  tracks: Track[];
  selectedTrack: string | null;
  onSelectTrack: (trackId: string) => void;
  onAddTrack: (type: 'audio' | 'midi', instrument?: string) => void;
  onUpdateTrack: (trackId: string, updates: Partial<Track>) => void;
  onDeleteTrack: (trackId: string) => void;
  onTestInstrument: (trackId: string) => void;
}

const TrackList: React.FC<TrackListProps> = ({
  tracks,
  selectedTrack,
  onSelectTrack,
  onAddTrack,
  onUpdateTrack,
  onDeleteTrack,
  onTestInstrument
}) => {
  const { t } = useLanguage();
  
  // Expanded instrument list with categories
  const instrumentCategories = {
    keyboard: [
      { id: 'piano', emoji: '🎹', key: 'daw.instruments.piano' },
      { id: 'electricpiano', emoji: '🎹', key: 'daw.instruments.electricpiano' },
      { id: 'organ', emoji: '🎹', key: 'daw.instruments.organ' },
      { id: 'synth', emoji: '🎛️', key: 'daw.instruments.synth' },
      { id: 'pad', emoji: '🎛️', key: 'daw.instruments.pad' },
      { id: 'lead', emoji: '🎛️', key: 'daw.instruments.lead' },
      { id: 'arpeggiator', emoji: '🎛️', key: 'daw.instruments.arpeggiator' }
    ],
    strings: [
      { id: 'guitar', emoji: '🎸', key: 'daw.instruments.guitar' },
      { id: 'electricguitar', emoji: '🎸', key: 'daw.instruments.electricguitar' },
      { id: 'acousticguitar', emoji: '🎸', key: 'daw.instruments.acousticguitar' },
      { id: 'bass', emoji: '🎸', key: 'daw.instruments.bass' },
      { id: 'electricbass', emoji: '🎸', key: 'daw.instruments.electricbass' },
      { id: 'upright_bass', emoji: '🎻', key: 'daw.instruments.upright_bass' },
      { id: 'bass_synth', emoji: '🎛️', key: 'daw.instruments.bass_synth' },
      { id: 'violin', emoji: '🎻', key: 'daw.instruments.violin' },
      { id: 'cello', emoji: '🎻', key: 'daw.instruments.cello' },
      { id: 'harp', emoji: '🪕', key: 'daw.instruments.harp' },
      { id: 'banjo', emoji: '🪕', key: 'daw.instruments.banjo' },
      { id: 'mandolin', emoji: '🪕', key: 'daw.instruments.mandolin' },
      { id: 'string_ensemble', emoji: '🎻', key: 'daw.instruments.string_ensemble' }
    ],
    winds: [
      { id: 'trumpet', emoji: '🎺', key: 'daw.instruments.trumpet' },
      { id: 'french_horn', emoji: '🎺', key: 'daw.instruments.french_horn' },
      { id: 'trombone', emoji: '🎺', key: 'daw.instruments.trombone' },
      { id: 'tuba', emoji: '🎺', key: 'daw.instruments.tuba' },
      { id: 'flute', emoji: '🎶', key: 'daw.instruments.flute' },
      { id: 'piccolo', emoji: '🎶', key: 'daw.instruments.piccolo' },
      { id: 'oboe', emoji: '🎶', key: 'daw.instruments.oboe' },
      { id: 'clarinet', emoji: '🎶', key: 'daw.instruments.clarinet' },
      { id: 'saxophone', emoji: '🎷', key: 'daw.instruments.saxophone' }
    ],
    percussion: [
      { id: 'drums', emoji: '🥁', key: 'daw.instruments.drums' },
      { id: 'timpani', emoji: '🥁', key: 'daw.instruments.timpani' },
      { id: 'marimba', emoji: '🎵', key: 'daw.instruments.marimba' },
      { id: 'vibraphone', emoji: '🎵', key: 'daw.instruments.vibraphone' },
      { id: 'xylophone', emoji: '🎵', key: 'daw.instruments.xylophone' }
    ]
  };
  
  const [selectedInstrument, setSelectedInstrument] = useState('piano');
  const [selectedCategory, setSelectedCategory] = useState('keyboard');
  
  return (
    <div className="track-list">
      <h3 className="text-sm font-bold mb-3 text-blue-400">{t('daw.tracks.title')}</h3>
      
      {tracks.map((track) => (
        <div
          key={track.id}
          className={`track-item ${selectedTrack === track.id ? 'selected' : ''}`}
          onClick={() => onSelectTrack(track.id)}
        >
          <div className="track-info">
            <div className="track-name font-medium">{track.name}</div>
            <div className="track-type text-xs text-gray-400">{track.type.toUpperCase()}</div>
          </div>
          
          <div className="track-controls">
            <button
              className={`track-control-btn ${track.armed ? 'active' : ''}`}
              onClick={(e) => {
                e.stopPropagation();
                onUpdateTrack(track.id, { armed: !track.armed });
              }}
              title="Arm for recording"
            >
              R
            </button>
            
            <button
              className={`track-control-btn ${track.muted ? 'active' : ''}`}
              onClick={(e) => {
                e.stopPropagation();
                onUpdateTrack(track.id, { muted: !track.muted });
              }}
              title="Mute"
            >
              M
            </button>
            
            <button
              className={`track-control-btn ${track.solo ? 'active' : ''}`}
              onClick={(e) => {
                e.stopPropagation();
                onUpdateTrack(track.id, { solo: !track.solo });
              }}
              title="Solo"
            >
              S
            </button>
            
            <button
              className="track-control-btn hover:bg-green-600"
              onClick={(e) => {
                e.stopPropagation();
                onTestInstrument(track.id);
              }}
              title="Test instrument sound"
            >
              ♪
            </button>
            
            <button
              className="track-control-btn hover:bg-red-600"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteTrack(track.id);
              }}
              title="Delete track"
            >
              <TrashIcon className="w-3 h-3" />
            </button>
          </div>
          
          <div className="track-faders">
            <div className="fader-group">
              <label className="fader-label">Volume</label>
              <input
                type="range"
                min="0"
                max="100"
                value={track.volume}
                onChange={(e) => {
                  e.stopPropagation();
                  onUpdateTrack(track.id, { volume: parseInt(e.target.value) });
                }}
                className="horizontal-slider volume-slider"
              />
              <span className="fader-value">{track.volume}</span>
            </div>
            
            <div className="fader-group">
              <label className="fader-label">Pan</label>
              <input
                type="range"
                min="-100"
                max="100"
                value={track.pan}
                onChange={(e) => {
                  e.stopPropagation();
                  onUpdateTrack(track.id, { pan: parseInt(e.target.value) });
                }}
                className="horizontal-slider pan-slider"
              />
              <span className="fader-value">{track.pan > 0 ? `R${track.pan}` : track.pan < 0 ? `L${Math.abs(track.pan)}` : 'C'}</span>
            </div>
          </div>
        </div>
      ))}
      
      <div className="add-track-section">
        <h4 className="text-xs font-medium mb-2 text-gray-400">{t('daw.add.track')}</h4>
        
        {/* MIDI Instruments */}
        <div className="instrument-category mb-3">
          <h5 className="text-xs font-medium mb-1 text-blue-400">{t('daw.midi.instruments')}</h5>
          
          {/* Category Selection */}
          <div className="mb-2">
            <label className="text-xs text-gray-400 block mb-1">Category:</label>
            <select
              value={selectedCategory}
              onChange={(e) => {
                setSelectedCategory(e.target.value);
                // Auto-select first instrument in new category
                const firstInstrument = instrumentCategories[e.target.value as keyof typeof instrumentCategories][0];
                setSelectedInstrument(firstInstrument.id);
              }}
              className="w-full px-2 py-1 text-xs bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500"
            >
              <option value="keyboard">🎹 Keyboard & Synth</option>
              <option value="strings">🎸 Strings</option>
              <option value="winds">🎺 Winds</option>
              <option value="percussion">🥁 Percussion</option>
            </select>
          </div>
          
          {/* Instrument Selection */}
          <div className="mb-2">
            <label className="text-xs text-gray-400 block mb-1">Instrument:</label>
            <select
              value={selectedInstrument}
              onChange={(e) => setSelectedInstrument(e.target.value)}
              className="w-full px-2 py-1 text-xs bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500"
            >
              {instrumentCategories[selectedCategory as keyof typeof instrumentCategories].map((instrument) => (
                <option key={instrument.id} value={instrument.id}>
                  {instrument.emoji} {t(instrument.key)}
                </option>
              ))}
            </select>
          </div>
          
          {/* Add Track Button */}
          <button
            className="add-track-btn w-full"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log(`Adding ${selectedInstrument} track!`);
              try {
                onAddTrack('midi', selectedInstrument);
              } catch (error) {
                console.error(`Error adding ${selectedInstrument} track:`, error);
              }
            }}
          >
            <PlusIcon className="w-4 h-4 inline mr-2" />
            Add {t(instrumentCategories[selectedCategory as keyof typeof instrumentCategories]
              .find(inst => inst.id === selectedInstrument)?.key || 'daw.instruments.piano')} Track
          </button>
        </div>
        
        {/* Audio Track */}
        <div className="instrument-category">
          <h5 className="text-xs font-medium mb-1 text-green-400">{t('daw.audio.track')}</h5>
          <button
            className="add-track-btn audio-track-btn"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('Audio track button clicked!');
              try {
                onAddTrack('audio');
              } catch (error) {
                console.error('Error adding audio track:', error);
              }
            }}
          >
            <PlusIcon className="w-4 h-4 inline mr-2" />
            {t('daw.audio.recording.track')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TrackList;