import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { BellIcon, UserIcon, ChevronDownIcon, GlobeAltIcon, ChatBubbleLeftIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useUser } from '../contexts/UserContext';
import { useLanguage } from '../contexts/LanguageContext';
import { USER_ROLE_LABELS } from '../types/user';

const Navigation: React.FC = () => {
  const location = useLocation();
  const { user, hasPermission, logout } = useUser();
  const { language, setLanguage, t } = useLanguage();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);
  
  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="bg-white shadow-md">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Link to="/" className="text-xl font-bold text-gray-900">iBOM</Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link 
                  to="/musicians" 
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    isActive('/musicians') ? 'text-blue-600 bg-blue-50' : 'text-gray-500 hover:text-blue-600'
                  }`}
                >
                  {language === 'en' ? 'Creators' : '创作者'}
                </Link>

                <Link 
                  to="/works" 
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    isActive('/works') ? 'text-blue-600 bg-blue-50' : 'text-gray-500 hover:text-blue-600'
                  }`}
                >
                  {t('nav.works')}
                </Link>
                <Link 
                  to="/music-studio" 
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    isActive('/music-studio') ? 'text-blue-600 bg-blue-50' : 'text-gray-500 hover:text-blue-600'
                  }`}
                >
                  DAW
                </Link>
                {hasPermission('canInvest') && (
                  <Link 
                    to="/investment" 
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      isActive('/investment') ? 'text-blue-600 bg-blue-50' : 'text-gray-500 hover:text-blue-600'
                    }`}
                  >
                    {t('nav.investment')}
                  </Link>
                )}
                {hasPermission('canPublish') && (
                  <Link 
                    to="/publishing" 
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      isActive('/publishing') ? 'text-blue-600 bg-blue-50' : 'text-gray-500 hover:text-blue-600'
                    }`}
                  >
                    {t('nav.publishing')}
                  </Link>
                )}
              </div>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="ml-4 flex items-center md:ml-6">
              <Link 
                  to="/collaboration" 
                  className={`mr-3 px-3 py-2 rounded-md text-sm font-medium ${
                    isActive('/collaboration') ? 'text-blue-600 bg-blue-50' : 'text-gray-500 hover:text-blue-600'
                  }`}
                >
                  {t('nav.collaboration')}
              </Link>

              {/* Chat Icon */}
              <Link 
                to="/chat" 
                className="mr-3 bg-gray-100 p-2 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <span className="sr-only">Chat</span>
                <ChatBubbleLeftIcon className="h-5 w-5" aria-hidden="true" />
              </Link>
              {/* Language Switcher */}
              <div className="mr-3 relative">
                <button 
                  onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                  className="bg-gray-100 p-2 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <GlobeAltIcon className="h-5 w-5" aria-hidden="true" />
                </button>
                {showLanguageMenu && (
                  <div className="origin-top-right absolute right-0 mt-2 w-24 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                    <button
                      onClick={() => {
                        setLanguage('zh');
                        setShowLanguageMenu(false);
                      }}
                      className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${
                        language === 'zh' ? 'text-blue-600 bg-blue-50' : 'text-gray-700'
                      }`}
                    >
                      {t('lang.chinese')}
                    </button>
                    <button
                      onClick={() => {
                        setLanguage('en');
                        setShowLanguageMenu(false);
                      }}
                      className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${
                        language === 'en' ? 'text-blue-600 bg-blue-50' : 'text-gray-700'
                      }`}
                    >
                      {t('lang.english')}
                    </button>
                  </div>
                )}
              </div>
              <button className="bg-gray-100 p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <span className="sr-only">View notifications</span>
                <BellIcon className="h-6 w-6" aria-hidden="true" />
              </button>
              <div className="ml-3 relative">
                <div>
                  <button 
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    className="max-w-xs bg-gray-100 rounded-full flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" 
                    id="user-menu-button" 
                    aria-expanded={showUserMenu} 
                    aria-haspopup="true"
                  >
                    <span className="sr-only">Open user menu</span>
                    {user ? (
                      <div className="flex items-center space-x-2 px-3 py-1">
                        <UserIcon className="h-6 w-6" />
                        <span className="text-sm text-gray-700">{user.name}</span>
                        <span className="text-xs text-gray-500">({USER_ROLE_LABELS[user.role]})</span>
                        <ChevronDownIcon className="h-4 w-4" />
                      </div>
                    ) : (
                      <UserIcon className="h-8 w-8 rounded-full p-1" />
                    )}
                  </button>
                </div>
                {showUserMenu && user && (
                  <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                    <div className="px-4 py-2 text-sm text-gray-700 border-b">
                      <div className="font-medium">{user.name}</div>
                      <div className="text-xs text-gray-500">{user.email}</div>
                    </div>
                    <Link
                      to="/account"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      {t('nav.profile')}
                    </Link>
                    <button
                      onClick={() => {
                        logout();
                        setShowUserMenu(false);
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      {t('nav.logout')}
                    </button>
                  </div>
                )}
                {showUserMenu && !user && (
                  <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                    <Link
                      to="/login"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      {t('nav.login')}
                    </Link>
                    <Link
                      to="/register"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      {t('nav.register')}
                    </Link>
                  </div>
                )}
              </div>
              {/* Create Collaboration Space Button - Most Right */}
              <Link
                to="/collaboration/create"
                className="ml-3 bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 text-sm font-medium"
              >
                <PlusIcon className="h-4 w-4" />
                {language === 'zh' ? '创建协创空间' : 'Create Space'}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;