declare module 'midi-file' {
  interface MidiEvent {
    type: string;
    deltaTime: number;
    noteNumber?: number;
    velocity?: number;
    channel?: number;
    microsecondsPerBeat?: number;
  }

  interface ParsedMidi {
    tracks: MidiEvent[][];
    timeDivision: number;
  }

  export function parseMidi(midiData: Uint8Array): ParsedMidi;
  export function writeMidi(midiData: ParsedMidi): Uint8Array;
}