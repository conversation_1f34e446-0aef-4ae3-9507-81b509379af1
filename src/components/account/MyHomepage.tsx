import React, { useState } from 'react';
import { 
  UserIcon, 
  MusicalNoteIcon, 
  UserGroupIcon, 
  DocumentTextIcon,
  ShareIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  LinkIcon,
  CameraIcon,
  PencilIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

const MyHomepage: React.FC = () => {
  const [showIntro, setShowIntro] = useState(false);
  const { t } = useLanguage();

  // 用户信息
  const userInfo = {
    name: 'Musician XXX',
    username: '@musician_xxx',
    avatar: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=120&h=120&fit=crop&crop=face',
    coverImage: 'https://images.unsplash.com/photo-*************-c1f69419868d?w=800&h=300&fit=crop'
  };

  const quickActions = [
    { id: 'live', label: t('account.homepage.quick.actions.live'), icon: '📺', color: 'bg-red-50 text-red-600' },
    { id: 'photo', label: t('account.homepage.quick.actions.photo'), icon: '📷', color: 'bg-green-50 text-green-600' },
    { id: 'life', label: t('account.homepage.quick.actions.music.life'), icon: '🎵', color: 'bg-blue-50 text-blue-600' }
  ];

  const posts = [
    {
      id: 1,
      author: t('account.homepage.post.author'),
      time: `3${t('account.homepage.post.minutes.ago')}`,
      content: t('account.homepage.post.content1'),
      likes: 0,
      comments: 0,
      shares: 0
    },
    {
      id: 2,
      author: t('account.homepage.post.author'),
      time: `8${t('account.homepage.post.minutes.ago')}`,  
      content: t('account.homepage.post.content2'),
      likes: 0,
      comments: 0,
      shares: 0
    }
  ];

  return (
    <div className="max-w-6xl mx-auto">
      {/* 封面图片 */}
      <div className="relative h-80 bg-gradient-to-r from-gray-300 to-gray-400 rounded-lg overflow-hidden mb-4">
        <img
          src={userInfo.coverImage}
          alt={t('myhomepage.cover.alt')}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        
        {/* 添加封面照片按钮 */}
        <button className="absolute top-4 right-4 bg-white bg-opacity-90 text-gray-700 px-4 py-2 rounded-lg hover:bg-opacity-100 transition-opacity flex items-center">
          <CameraIcon className="w-4 h-4 mr-2" />
          {t('myhomepage.cover.add')}
        </button>
      </div>

      {/* 用户信息栏 */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {/* 头像 */}
            <div className="relative mr-4">
              <img
                src={userInfo.avatar}
                alt={t('myhomepage.avatar.alt')}
                className="w-24 h-24 rounded-full border-4 border-white shadow-lg"
              />
              <button className="absolute bottom-0 right-0 w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                <CameraIcon className="w-4 h-4 text-white" />
              </button>
            </div>
            
            {/* 用户信息 */}
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{userInfo.name}</h1>
            </div>
          </div>
          
          {/* 操作按钮 */}
          <div className="flex space-x-3">
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
              <span className="mr-1">+</span>
              {t('myhomepage.publish.news')}
            </button>
            <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center">
              <PencilIcon className="w-4 h-4 mr-2" />
              {t('myhomepage.edit.profile')}
            </button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧栏 */}
        <div className="col-span-4 space-y-6">
          {/* 个人资料 */}
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('myhomepage.personal.profile')}</h3>
            
            <div className="space-y-3">
              <button className="w-full text-left px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                {t('myhomepage.add.signature')}
              </button>
              <button className="w-full text-left px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                {t('myhomepage.edit.details')}
              </button>
              <button className="w-full text-left px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                {t('myhomepage.add.featured')}
              </button>
            </div>
          </div>

          {/* 作品 */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">{t('myhomepage.works.title')}</h3>
                <button className="text-blue-600 hover:text-blue-800 text-sm">
                  {t('myhomepage.all.works')}
                </button>
              </div>
            </div>
            <div className="p-4">
              <p className="text-gray-500 text-center py-8">{t('myhomepage.no.works')}</p>
            </div>
          </div>

          {/* 好友 */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">{t('myhomepage.friends.title')}</h3>
                <button className="text-blue-600 hover:text-blue-800 text-sm">
                  {t('myhomepage.all.friends')}
                </button>
              </div>
            </div>
            <div className="p-4">
              <p className="text-gray-500 text-center py-8">{t('myhomepage.no.friends')}</p>
            </div>
          </div>

          {/* 底部信息 */}
          <div className="text-xs text-gray-500 space-y-1">
            <div className="flex flex-wrap">
              <span className="mr-2">{t('myhomepage.privacy.policy')}</span>
              <span className="mr-2">{t('myhomepage.terms.service')}</span>
              <span className="mr-2">{t('myhomepage.advertising')}</span>
              <span className="mr-2">{t('myhomepage.ad.choices')}</span>
              <span className="mr-2">{t('myhomepage.cookies')}</span>
              <span>{t('myhomepage.more')}</span>
            </div>
            <div>{t('myhomepage.copyright')}</div>
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="col-span-8 space-y-6">
          {/* 介绍自己 */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-start">
              <img
                src={userInfo.avatar}
                alt={t('myhomepage.avatar.alt')}
                className="w-10 h-10 rounded-full mr-3"
              />
              <div className="flex-1">
                <input
                  type="text"
                  placeholder={t('myhomepage.share.music')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  onClick={() => setShowIntro(true)}
                />
                
                {/* 快捷操作 */}
                <div className="flex items-center space-x-4 mt-4">
                  {quickActions.map((action) => (
                    <button
                      key={action.id}
                      className={`flex items-center px-4 py-2 rounded-lg transition-colors ${action.color} hover:opacity-80`}
                    >
                      <span className="text-lg mr-2">{action.icon}</span>
                      <span className="text-sm font-medium">{action.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 介绍一下自己卡片 */}
          <div className="bg-white rounded-lg shadow-sm border border-orange-200">
            <div className="p-4 border-b border-orange-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">{t('myhomepage.introduce.yourself')}</h3>
                <button className="text-orange-600 hover:text-orange-800 text-sm">
                  {t('myhomepage.introduce.yourself')}
                </button>
              </div>
            </div>
            <div className="p-4">
              <div className="flex items-center text-sm text-gray-600">
                <GlobeAltIcon className="w-4 h-4 mr-2" />
                <span>{t('myhomepage.public')}</span>
                <button className="ml-auto text-blue-600 hover:text-blue-800">
                  {t('myhomepage.cancel')}
                </button>
                <button className="ml-2 text-blue-600 hover:text-blue-800">
                  {t('myhomepage.save')}
                </button>
              </div>
            </div>
          </div>

          {/* 动态列表 */}
          <div className="space-y-4">
            {posts.map((post) => (
              <div key={post.id} className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-start">
                  <img
                    src={userInfo.avatar}
                    alt={post.author}
                    className="w-10 h-10 rounded-full mr-3"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="font-semibold text-gray-900">{post.author}</span>
                      <span className="text-gray-500 text-sm">{t('myhomepage.updated.status')}</span>
                      <span className="text-gray-500 text-sm">·</span>
                      <span className="text-gray-500 text-sm">{post.time}</span>
                    </div>
                    
                    <p className="text-gray-900 mb-4">{post.content}</p>
                    
                    {/* 互动按钮 */}
                    <div className="flex items-center space-x-6 text-gray-500">
                      <button className="flex items-center hover:text-blue-600 transition-colors">
                        <HeartIcon className="w-5 h-5 mr-1" />
                        <span className="text-sm">{t('myhomepage.like.action')}</span>
                      </button>
                      <button className="flex items-center hover:text-blue-600 transition-colors">
                        <ChatBubbleLeftIcon className="w-5 h-5 mr-1" />
                        <span className="text-sm">{t('myhomepage.comment.action')}</span>
                      </button>
                      <button className="flex items-center hover:text-blue-600 transition-colors">
                        <ShareIcon className="w-5 h-5 mr-1" />
                        <span className="text-sm">{t('myhomepage.send.action')}</span>
                      </button>
                      <button className="flex items-center hover:text-blue-600 transition-colors">
                        <ShareIcon className="w-5 h-5 mr-1" />
                        <span className="text-sm">{t('myhomepage.share.action')}</span>
                      </button>
                    </div>
                  </div>
                </div>
                
                {/* 评论输入 */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center">
                    <img
                      src={userInfo.avatar}
                      alt={t('myhomepage.avatar.alt')}
                      className="w-8 h-8 rounded-full mr-3"
                    />
                    <input
                      type="text"
                      placeholder={t('myhomepage.write.comment')}
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <div className="ml-3 flex space-x-2">
                      <button className="text-gray-400 hover:text-gray-600" title="表情">
                        <span className="text-lg">😊</span>
                      </button>
                      <button className="text-gray-400 hover:text-gray-600" title="图片">
                        <span className="text-lg">📷</span>
                      </button>
                      <button className="text-gray-400 hover:text-gray-600" title="标签">
                        <span className="text-lg">📄</span>
                      </button>
                      <button className="text-gray-400 hover:text-gray-600" title="位置">
                        <span className="text-lg">📍</span>
                      </button>
                      <button className="text-gray-400 hover:text-gray-600" title="GIF">
                        <span className="text-sm font-bold">GIF</span>
                      </button>
                      <button className="text-gray-400 hover:text-gray-600" title="表情符号">
                        <span className="text-lg">🎭</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 支持生成CV链接 */}
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="text-center">
              <span className="text-sm text-gray-600">{t('myhomepage.support.cv.link')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

};

export default MyHomepage;