import React, { useState, useEffect } from 'react';
import { useUser } from '../contexts/UserContext';
import { 
  DocumentTextIcon, 
  CheckCircleIcon, 
  ClockIcon, 
  XCircleIcon 
} from '@heroicons/react/24/outline';

interface StreamerApplicationData {
  id: string;
  userId: string;
  realName: string;
  idNumber: string;
  phone: string;
  email: string;
  experience: string;
  selfIntroduction: string;
  profilePhoto?: string;
  idCardFront?: string;
  idCardBack?: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  reviewedAt?: string;
  reviewNotes?: string;
}

const StreamerApplication: React.FC = () => {
  const { user } = useUser();
  const [application, setApplication] = useState<StreamerApplicationData | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    realName: '',
    idNumber: '',
    phone: '',
    email: user?.email || '',
    experience: '',
    selfIntroduction: '',
    profilePhoto: null as File | null,
    idCardFront: null as File | null,
    idCardBack: null as File | null,
  });

  useEffect(() => {
    fetchApplication();
  }, []);

  const fetchApplication = async () => {
    try {
      const { default: ApiService } = await import('../services/api');
      const data = await ApiService.getMyStreamerApplication();
      setApplication(data);
    } catch (error) {
      console.error('Failed to fetch application:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = (field: string, file: File) => {
    setFormData(prev => ({
      ...prev,
      [field]: file
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const { default: ApiService } = await import('../services/api');
      const newApplication = await ApiService.submitStreamerApplication(formData as any);
      setApplication(newApplication);
      setShowForm(false);
      alert('申请提交成功，请等待审核');
    } catch (error) {
      console.error('Failed to submit application:', error);
      alert('申请提交失败，请重试');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-8 w-8 text-yellow-500" />;
      case 'approved':
        return <CheckCircleIcon className="h-8 w-8 text-green-500" />;
      case 'rejected':
        return <XCircleIcon className="h-8 w-8 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '审核中';
      case 'approved':
        return '已通过';
      case 'rejected':
        return '已拒绝';
      default:
        return '';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">主播申请</h1>
          </div>

          <div className="p-6">
            {application ? (
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  {getStatusIcon(application.status)}
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">
                      申请状态：{getStatusText(application.status)}
                    </h2>
                    <p className="text-sm text-gray-500">
                      提交时间：{new Date(application.submittedAt).toLocaleDateString('zh-CN')}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">真实姓名</label>
                    <p className="mt-1 text-sm text-gray-900">{application.realName}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">身份证号</label>
                    <p className="mt-1 text-sm text-gray-900">{application.idNumber.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">手机号</label>
                    <p className="mt-1 text-sm text-gray-900">{application.phone}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">邮箱</label>
                    <p className="mt-1 text-sm text-gray-900">{application.email}</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">直播经验</label>
                  <p className="mt-1 text-sm text-gray-900">{application.experience}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">自我介绍</label>
                  <p className="mt-1 text-sm text-gray-900">{application.selfIntroduction}</p>
                </div>

                {application.status === 'rejected' && application.reviewNotes && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <h3 className="text-sm font-medium text-red-800">拒绝原因</h3>
                    <p className="mt-1 text-sm text-red-700">{application.reviewNotes}</p>
                  </div>
                )}

                {application.status === 'approved' && (
                  <div className="bg-green-50 border border-green-200 rounded-md p-4">
                    <h3 className="text-sm font-medium text-green-800">恭喜！</h3>
                    <p className="mt-1 text-sm text-green-700">您的主播申请已通过审核，现在可以开始直播了！</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">还未提交申请</h3>
                <p className="mt-1 text-sm text-gray-500">成为主播需要先提交申请并通过审核</p>
                <div className="mt-6">
                  <button
                    onClick={() => setShowForm(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    开始申请
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {showForm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">主播申请表</h3>
                  <button
                    onClick={() => setShowForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XCircleIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">真实姓名 *</label>
                      <input
                        type="text"
                        required
                        value={formData.realName}
                        onChange={(e) => setFormData({...formData, realName: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">身份证号 *</label>
                      <input
                        type="text"
                        required
                        value={formData.idNumber}
                        onChange={(e) => setFormData({...formData, idNumber: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">手机号 *</label>
                      <input
                        type="tel"
                        required
                        value={formData.phone}
                        onChange={(e) => setFormData({...formData, phone: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">邮箱 *</label>
                      <input
                        type="email"
                        required
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">直播经验 *</label>
                    <select
                      required
                      value={formData.experience}
                      onChange={(e) => setFormData({...formData, experience: e.target.value})}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">请选择</option>
                      <option value="none">无经验</option>
                      <option value="beginner">初级（1年以下）</option>
                      <option value="intermediate">中级（1-3年）</option>
                      <option value="advanced">高级（3年以上）</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">自我介绍 *</label>
                    <textarea
                      required
                      rows={4}
                      value={formData.selfIntroduction}
                      onChange={(e) => setFormData({...formData, selfIntroduction: e.target.value})}
                      placeholder="请介绍您的直播风格、特长等..."
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">个人照片 *</label>
                      <input
                        type="file"
                        required
                        accept="image/*"
                        onChange={(e) => e.target.files && handleFileUpload('profilePhoto', e.target.files[0])}
                        className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">身份证正面 *</label>
                      <input
                        type="file"
                        required
                        accept="image/*"
                        onChange={(e) => e.target.files && handleFileUpload('idCardFront', e.target.files[0])}
                        className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">身份证反面 *</label>
                      <input
                        type="file"
                        required
                        accept="image/*"
                        onChange={(e) => e.target.files && handleFileUpload('idCardBack', e.target.files[0])}
                        className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowForm(false)}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      提交申请
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StreamerApplication;