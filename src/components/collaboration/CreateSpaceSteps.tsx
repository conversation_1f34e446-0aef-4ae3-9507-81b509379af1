import React, { useState, useMemo } from 'react';
import { ArrowLeftIcon, ArrowRightIcon, CheckIcon } from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../../contexts/UserContext';
import { useLanguage } from '../../contexts/LanguageContext';

// Step 1: Basic Information
import BasicInfoStep from './steps/BasicInfoStep';
// Step 2: Role Setup
import RoleSetupStep from './steps/RoleSetupStep';
// Step 3: Invite Members
import InviteMembersStep from './steps/InviteMembersStep';

interface SpaceData {
  // Basic Info
  spaceName: string;
  spaceDescription: string;
  privacy: string;
  
  // Role Setup
  selectedRoles: string[];
  roleAssignments: {[role: string]: {count: number, members: any[]}};
  revenueShares: {[key: string]: number};
  
  // Invite Members
  invitedMembers: any[];
}

const CreateSpaceSteps: React.FC = () => {
  const { t, language } = useLanguage();
  const { user } = useUser();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  // Combined state for all steps
  const [spaceData, setSpaceData] = useState<SpaceData>({
    // Step 1 data
    spaceName: '',
    spaceDescription: '',
    privacy: 'public',
    
    // Step 2 data
    selectedRoles: [],
    roleAssignments: {},
    revenueShares: {},
    
    // Step 3 data
    invitedMembers: []
  });

  const steps = useMemo(() => [
    {
      id: 1,
      title: language === 'zh' ? '基本信息' : 'Basic Info',
      description: language === 'zh' ? '填写空间名称和简介' : 'Fill space name and description'
    },
    {
      id: 2,
      title: language === 'zh' ? '角色设置' : 'Role Setup',
      description: language === 'zh' ? '设置所需角色和分成' : 'Set required roles and revenue sharing'
    },
    {
      id: 3,
      title: language === 'zh' ? '邀请成员' : 'Invite Members',
      description: language === 'zh' ? '邀请和推荐成员' : 'Invite and recommend members'
    }
  ], [language]);

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFinish = () => {
    // Validation
    if (!spaceData.spaceName.trim()) {
      alert(language === 'zh' ? '请填写空间名称' : 'Please fill in space name');
      return;
    }
    if (spaceData.selectedRoles.length === 0) {
      alert(language === 'zh' ? '请选择至少一个角色' : 'Please select at least one role');
      return;
    }
    
    // Calculate total percentage
    const totalPercentage = spaceData.selectedRoles.reduce((sum, role) => {
      return sum + (spaceData.revenueShares[role] || getStandardRevenueShare(role));
    }, 0);
    
    if (totalPercentage !== 100) {
      alert(language === 'zh' ? `分成比例总计应为100%，当前为${totalPercentage}%` : `Revenue sharing should total 100%, currently ${totalPercentage}%`);
      return;
    }

    // Generate new space
    const newSpaceId = Date.now();
    const finalSpaceData = {
      id: newSpaceId,
      ...spaceData,
      creator: user?.email || (language === 'zh' ? '当前用户' : 'Current User'),
      status: language === 'zh' ? '招募中' : 'Recruiting',
      createdAt: new Date().toISOString()
    };
    
    // Save to localStorage
    const existingSpaces = JSON.parse(localStorage.getItem('myCreatedSpaces') || '[]');
    existingSpaces.push(finalSpaceData);
    localStorage.setItem('myCreatedSpaces', JSON.stringify(existingSpaces));
    
    console.log('Creating collaboration space:', finalSpaceData);
    alert(language === 'zh' ? '协创空间创建成功！' : 'Collaboration space created successfully!');
    
    // Navigate back to collaboration page
    navigate('/collaboration', { 
      state: { 
        newSpaceId: newSpaceId,
        showNewSpace: true,
        spaceData: finalSpaceData
      } 
    });
  };

  const getStandardRevenueShare = (role: string): number => {
    const standardShares: {[key: string]: number} = {
      [language === 'zh' ? '作词' : 'Lyrics']: 25,
      [language === 'zh' ? '作曲' : 'Composition']: 35,
      [language === 'zh' ? '编曲' : 'Arrangement']: 20,
      [language === 'zh' ? '演唱' : 'Vocals']: 15,
      [language === 'zh' ? '混音' : 'Mixing']: 10,
      [language === 'zh' ? '制作' : 'Production']: 15,
      [language === 'zh' ? '其他' : 'Other']: 5
    };
    return standardShares[role] || 10;
  };

  const isStepValid = (stepNumber: number): boolean => {
    switch (stepNumber) {
      case 1:
        return spaceData.spaceName.trim() !== '';
      case 2:
        return spaceData.selectedRoles.length > 0;
      case 3:
        return true;
      default:
        return false;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <BasicInfoStep 
            spaceData={spaceData}
            setSpaceData={setSpaceData}
          />
        );
      case 2:
        return (
          <RoleSetupStep 
            spaceData={spaceData}
            setSpaceData={setSpaceData}
            getStandardRevenueShare={getStandardRevenueShare}
          />
        );
      case 3:
        return (
          <InviteMembersStep 
            spaceData={spaceData}
            setSpaceData={setSpaceData}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center gap-4 mb-6">
            <button
              onClick={() => navigate('/collaboration')}
              className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="w-5 h-5" />
              {language === 'zh' ? '返回' : 'Back'}
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {language === 'zh' ? '创建协创空间' : 'Create Collaboration Space'}
              </h1>
              <p className="text-gray-600">
                {language === 'zh' ? '按步骤创建您的音乐协创空间' : 'Create your music collaboration space step by step'}
              </p>
            </div>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-8">
            {steps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div className="flex flex-col items-center flex-1">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium mb-2 ${
                    step.id < currentStep 
                      ? 'bg-green-600 text-white' 
                      : step.id === currentStep 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-200 text-gray-600'
                  }`}>
                    {step.id < currentStep ? (
                      <CheckIcon className="w-5 h-5" />
                    ) : (
                      step.id
                    )}
                  </div>
                  <div className="text-center">
                    <div className={`text-sm font-medium ${
                      step.id <= currentStep ? 'text-gray-900' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </div>
                    <div className={`text-xs ${
                      step.id <= currentStep ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      {step.description}
                    </div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-4 ${
                    step.id < currentStep ? 'bg-green-600' : 'bg-gray-200'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border p-8">
              {renderStepContent()}
            </div>

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between mt-8">
              <button
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className={`flex items-center gap-2 px-6 py-3 rounded-lg transition-colors ${
                  currentStep === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                <ArrowLeftIcon className="w-5 h-5" />
                {language === 'zh' ? '上一步' : 'Previous'}
              </button>

              {currentStep < totalSteps ? (
                <button
                  onClick={handleNext}
                  disabled={!isStepValid(currentStep)}
                  className={`flex items-center gap-2 px-6 py-3 rounded-lg transition-colors ${
                    isStepValid(currentStep)
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  {language === 'zh' ? '下一步' : 'Next'}
                  <ArrowRightIcon className="w-5 h-5" />
                </button>
              ) : (
                <button
                  onClick={handleFinish}
                  className="flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                >
                  <CheckIcon className="w-5 h-5" />
                  {language === 'zh' ? '创建空间' : 'Create Space'}
                </button>
              )}
            </div>
          </div>

          {/* Right Sidebar - Tips and Help */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border p-6 sticky top-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {language === 'zh' ? '创建提示' : 'Creation Tips'}
              </h3>
              
              {currentStep === 1 && (
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-start gap-2">
                    <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">1</div>
                    <p>{language === 'zh' ? '起一个有吸引力的空间名称' : 'Choose an attractive space name'}</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">2</div>
                    <p>{language === 'zh' ? '详细描述项目内容和目标' : 'Describe project content and goals in detail'}</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">3</div>
                    <p>{language === 'zh' ? '选择合适的隐私设置' : 'Choose appropriate privacy settings'}</p>
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-start gap-2">
                    <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">1</div>
                    <p>{language === 'zh' ? '根据项目需要选择角色' : 'Select roles based on project needs'}</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">2</div>
                    <p>{language === 'zh' ? '每个角色可设置多人参与' : 'Multiple people can be assigned to each role'}</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">3</div>
                    <p>{language === 'zh' ? '确保分成比例总计为100%' : 'Ensure revenue sharing totals 100%'}</p>
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-start gap-2">
                    <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">1</div>
                    <p>{language === 'zh' ? '查看平台推荐的优质创作者' : 'Browse platform-recommended quality creators'}</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">2</div>
                    <p>{language === 'zh' ? '发送个性化邀请消息' : 'Send personalized invitation messages'}</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">3</div>
                    <p>{language === 'zh' ? '可稍后继续邀请成员' : 'You can continue inviting members later'}</p>
                  </div>
                </div>
              )}

              <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-sm text-blue-800">
                  {language === 'zh' 
                    ? '💡 提示：每个步骤的信息都会自动保存，您可以随时返回修改。'
                    : '💡 Tip: Information from each step is automatically saved, you can go back and modify at any time.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateSpaceSteps;