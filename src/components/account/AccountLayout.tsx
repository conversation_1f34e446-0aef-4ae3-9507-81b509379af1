import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import AccountSidebar from './AccountSidebar';
import AccountInfo from './AccountInfo';
import MyHomepage from './MyHomepage';
import PersonalProfile from './PersonalProfile';
import MessageCenter from './MessageCenter';
import CreativeCenter from './CreativeCenter';
import MyRewards from './MyRewards';
import MyWallet from './MyWallet';
import Certification from './Certification';
import PurchaseCoins from './PurchaseCoins';

const AccountLayout: React.FC = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState(() => {
    const path = location.pathname;
    if (path.includes('homepage')) return 'homepage';
    if (path.includes('profile')) return 'profile';
    if (path.includes('messages')) return 'messages';
    if (path.includes('creative')) return 'creative';
    if (path.includes('rewards')) return 'rewards';
    if (path.includes('wallet')) return 'wallet';
    if (path.includes('certification')) return 'certification';
    if (path.includes('purchase')) return 'purchase';
    return 'info';
  });

  const renderContent = () => {
    switch (activeTab) {
      case 'info':
        return <AccountInfo />;
      case 'homepage':
        return <MyHomepage />;
      case 'profile':
        return <PersonalProfile />;
      case 'messages':
        return <MessageCenter />;
      case 'creative':
        return <CreativeCenter />;
      case 'rewards':
        return <MyRewards />;
      case 'wallet':
        return <MyWallet onPurchase={() => setActiveTab('purchase')} />;
      case 'certification':
        return <Certification />;
      case 'purchase':
        return <PurchaseCoins onBack={() => setActiveTab('wallet')} />;
      default:
        return <AccountInfo />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex max-w-7xl mx-auto">
        {/* 左侧导航 */}
        <AccountSidebar 
          activeTab={activeTab} 
          onTabChange={setActiveTab}
        />
        
        {/* 右侧内容 */}
        <div className="flex-1 ml-64 p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default AccountLayout;