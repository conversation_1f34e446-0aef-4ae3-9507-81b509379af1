import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Work } from '../services/api';

interface WorksContextType {
  publishedWorks: Work[];
  addPublishedWork: (work: Work) => void;
  updateWorkNFT: (workId: number | string, nft: Work['nft']) => void;
  updateWorkNFTByCollabSpaceId: (collabSpaceId: number, nft: Work['nft']) => void;
}

const WorksContext = createContext<WorksContextType | undefined>(undefined);

export const useWorks = () => {
  const context = useContext(WorksContext);
  if (!context) {
    throw new Error('useWorks must be used within a WorksProvider');
  }
  return context;
};

export const WorksProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [publishedWorks, setPublishedWorks] = useState<Work[]>([]);

  const addPublishedWork = (work: Work) => {
    setPublishedWorks(prev => [work, ...prev]);
  };

  const updateWorkNFT = (workId: number | string, nft: Work['nft']) => {
    setPublishedWorks(prev => 
      prev.map(work => 
        work.id === workId 
          ? { ...work, nft } 
          : work
      )
    );
  };

  const updateWorkNFTByCollabSpaceId = (collabSpaceId: number, nft: Work['nft']) => {
    setPublishedWorks(prev => 
      prev.map(work => 
        work.collaborationSpaceId === collabSpaceId 
          ? { ...work, nft } 
          : work
      )
    );
  };

  return (
    <WorksContext.Provider value={{ publishedWorks, addPublishedWork, updateWorkNFT, updateWorkNFTByCollabSpaceId }}>
      {children}
    </WorksContext.Provider>
  );
};