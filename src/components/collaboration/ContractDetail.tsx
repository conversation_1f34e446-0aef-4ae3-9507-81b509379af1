import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  ArrowLeftIcon,
  UserGroupIcon,
  CalendarIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

const ContractDetail: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const contract = location.state?.contract;
  const { t } = useLanguage();

  if (!contract) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">{t('contract.not.found.title')}</h2>
          <p className="text-gray-600 mb-4">{t('contract.not.found.message')}</p>
          <button
            onClick={() => navigate(-1)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {t('common.back')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeftIcon className="w-5 h-5" />
              {t('common.back')}
            </button>
            <div className="flex-1">
              <h1 className="text-2xl font-semibold text-gray-900">{t('contract.detail.title')}</h1>
              <p className="text-gray-600 mt-1">{t('contract.detail.subtitle')}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 合约内容 */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-sm">
          {/* 合约头部信息 */}
          <div className="p-6 border-b">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  {t('contract.title.copyright')}
                </h2>
                <p className="text-gray-600">{t('contract.collaboration.space')}: {contract.spaceName}</p>
              </div>
              <div className="text-right">
                <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${
                  contract.type === 'signed'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {contract.type === 'signed' ? t('contract.status.signed') : t('contract.status.pending')}
                </span>
                {contract.signedDate && (
                  <p className="text-sm text-gray-500 mt-2">
                    {t('contract.signed.date')}：{contract.signedDate}
                  </p>
                )}
              </div>
            </div>

            {/* 基本信息卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <UserGroupIcon className="w-5 h-5 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-600">{t('contract.participants.count')}</p>
                  <p className="font-medium text-gray-900">{contract.parties.length} {t('contract.participants.people')}</p>
                </div>
              </div>
              {contract.signedDate && (
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <CalendarIcon className="w-5 h-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600">{t('contract.signing.time')}</p>
                    <p className="font-medium text-gray-900">{contract.signedDate}</p>
                  </div>
                </div>
              )}
              {contract.blockchainHash && (
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <ShieldCheckIcon className="w-5 h-5 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-600">{t('contract.blockchain.verification')}</p>
                    <p className="font-medium text-gray-900">{t('contract.blockchain.verified')}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 参与方信息 */}
          <div className="p-6 border-b">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('contract.participants.info')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {contract.parties.map((party: string, index: number) => (
                <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-lg">
                      {party.charAt(0)}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{party}</h4>
                    <p className="text-sm text-gray-600">{t('contract.participant.creator')}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">25%</p>
                    <p className="text-xs text-gray-500">{t('contract.participant.percentage')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 合约条款 */}
          <div className="p-6 border-b">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('contract.terms')}</h3>
            <div className="space-y-6">
              <div className="border-l-4 border-blue-500 pl-6">
                <h4 className="font-semibold text-gray-900 mb-2">{t('contract.clause.cooperation')}</h4>
                <p className="text-gray-700 leading-relaxed">
                  {t('contract.cooperation.content').replace('{spaceName}', contract.spaceName)}
                </p>
              </div>

              <div className="border-l-4 border-green-500 pl-6">
                <h4 className="font-semibold text-gray-900 mb-2">{t('contract.clause.revenue')}</h4>
                <p className="text-gray-700 leading-relaxed">
                  {t('contract.revenue.content')}
                </p>
              </div>

              <div className="border-l-4 border-purple-500 pl-6">
                <h4 className="font-semibold text-gray-900 mb-2">{t('contract.clause.copyright')}</h4>
                <p className="text-gray-700 leading-relaxed">
                  {t('contract.copyright.content')}
                </p>
              </div>

              <div className="border-l-4 border-orange-500 pl-6">
                <h4 className="font-semibold text-gray-900 mb-2">{t('contract.clause.breach')}</h4>
                <p className="text-gray-700 leading-relaxed">
                  {t('contract.breach.content')}
                </p>
              </div>

              <div className="border-l-4 border-red-500 pl-6">
                <h4 className="font-semibold text-gray-900 mb-2">{t('contract.clause.dispute')}</h4>
                <p className="text-gray-700 leading-relaxed">
                  {t('contract.dispute.content')}
                </p>
              </div>
            </div>
          </div>

          {/* 区块链信息 */}
          {contract.blockchainHash && (
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('contract.blockchain.info')}</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">{t('contract.blockchain.hash')}</p>
                    <p className="text-sm text-gray-900 font-mono break-all">
                      {contract.blockchainHash}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">{t('contract.blockchain.timestamp')}</p>
                    <p className="text-sm text-gray-900">
                      {contract.signedDate || '2024-01-15 14:30:22'}
                    </p>
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-xs text-gray-600">
                    {t('contract.blockchain.notice')}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="p-6">
            <div className="flex flex-wrap gap-4">
              <button
                onClick={() => navigate(-1)}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                {t('contract.action.back')}
              </button>
              {contract.type === 'pending' && (
                <button
                  onClick={() => navigate('/collaboration/contract')}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  {t('contract.action.sign')}
                </button>
              )}
              <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                {t('contract.action.download')}
              </button>
              <button className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                {t('contract.action.share')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractDetail;