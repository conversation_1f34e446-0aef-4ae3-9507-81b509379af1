import React, { useState, useRef } from 'react';
import { DocumentTextIcon, PencilIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';

interface ContractParty {
  id: number;
  name: string;
  role: string;
  percentage: number;
  signed: boolean;
  signature?: string;
}

const ContractSigning: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [contractData, setContractData] = useState({
    title: '《繁星点点》音乐版权合约',
    contractNumber: 'CTZ023T0T50001',
    date: '2023年10月15日',
    parties: [
      { id: 1, name: '王梓博', role: '作词方', percentage: 40, signed: false },
      { id: 2, name: '林雨薇', role: '作曲方', percentage: 30, signed: false },
      { id: 3, name: '陈志远', role: '演唱方', percentage: 30, signed: true, signature: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMTUwIDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjx0ZXh0IHg9IjE1IiB5PSI0NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE4IiBmaWxsPSIjMzMzIj7pmYflv5flhYs8L3RleHQ+PC9zdmc+' }
    ] as ContractParty[]
  });

  const [showSignatureInput, setShowSignatureInput] = useState<number | null>(null);

  const contractContent = {
    firstClause: {
      title: '第一条：合作内容',
      content: '各方就音乐作品《繁星点点》的创作、演唱及版权分配达成以下协议：',
      details: [
        '1.甲方负责歌词创作，享有作词版权；',
        '2.乙方负责乐曲创作，享有作曲版权；',
        '3.丙方负责演唱，享有录音制作版权。'
      ]
    },
    secondClause: {
      title: '第二条：收益分配',
      content: '作品产生的收益按以下比例分配：',
      details: [
        '甲方（作词）：40%',
        '乙方（作曲）：30%',
        '丙方（演唱）：30%'
      ]
    }
  };

  // 鼠标签名功能
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (canvas) {
      const rect = canvas.getBoundingClientRect();
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.beginPath();
        ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
      }
    }
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;
    const canvas = canvasRef.current;
    if (canvas) {
      const rect = canvas.getBoundingClientRect();
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
        ctx.stroke();
      }
    }
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
  };

  const saveSignature = (partyId: number) => {
    const canvas = canvasRef.current;
    if (canvas) {
      const signatureData = canvas.toDataURL();
      setContractData(prev => ({
        ...prev,
        parties: prev.parties.map(party => 
          party.id === partyId 
            ? { ...party, signed: true, signature: signatureData }
            : party
        )
      }));
      setShowSignatureInput(null);
    }
  };

  const handleSign = (partyId: number) => {
    setShowSignatureInput(partyId);
  };

  const handleConfirmSign = (partyId: number) => {
    // 检查是否有签名
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const hasSignature = imageData.data.some((channel, index) => 
          index % 4 === 3 && channel !== 0 // 检查alpha通道
        );
        
        if (hasSignature) {
          saveSignature(partyId);
          alert(t('contract.signing.success'));
        } else {
          alert(t('contract.signing.please.sign.first'));
        }
      }
    }
  };

  const handleReject = () => {
    navigate('/collaboration');
  };

  const handleConfirmContract = () => {
    const allSigned = contractData.parties.every(party => party.signed);
    if (allSigned) {
      alert(t('contract.signing.completed'));
      navigate('/collaboration');
    } else {
      alert(t('contract.signing.wait.all'));
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* 返回按钮 */}
      <div className="mb-6">
        <button
          onClick={() => navigate('/collaboration')}
          className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeftIcon className="w-5 h-5" />
          {t('contract.signing.back.collab')}
        </button>
      </div>

      {/* 合约标题 */}
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">{contractData.title}</h1>
        <p className="text-gray-600">{t('contract.signing.contract.number')}：{contractData.contractNumber}</p>
      </div>

      {/* 合约内容 */}
      <div className="bg-white rounded-lg shadow-sm border p-8 mb-8">
        <div className="mb-8">
          <div className="text-center mb-6">
            <p className="text-gray-700 text-lg leading-relaxed">
              {t('contract.signing.signed.by')}<span className="font-semibold text-gray-900">{contractData.date}</span>{t('contract.signing.signed.on')}：
            </p>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="p-4">
                <div className="font-semibold text-gray-900 mb-1">{t('contract.signing.party.first')}</div>
                <div className="text-gray-700">王梓博</div>
                <div className="text-sm text-gray-500">{t('contract.signing.role.lyricist')}</div>
              </div>
              <div className="p-4">
                <div className="font-semibold text-gray-900 mb-1">{t('contract.signing.party.second')}</div>
                <div className="text-gray-700">林雨薇</div>
                <div className="text-sm text-gray-500">{t('contract.signing.role.composer')}</div>
              </div>
              <div className="p-4">
                <div className="font-semibold text-gray-900 mb-1">{t('contract.signing.party.third')}</div>
                <div className="text-gray-700">陈志远</div>
                <div className="text-sm text-gray-500">{t('contract.signing.role.vocalist')}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 第一条 */}
        <div className="mb-8 border-l-4 border-blue-500 pl-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            {contractContent.firstClause.title}
          </h3>
          <p className="text-gray-700 mb-4 leading-relaxed">{contractContent.firstClause.content}</p>
          <div className="bg-blue-50 rounded-lg p-4">
            {contractContent.firstClause.details.map((detail, index) => (
              <p key={index} className="text-gray-700 mb-2 flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-500 text-white text-xs rounded-full mr-3 mt-0.5 flex-shrink-0">
                  {index + 1}
                </span>
                {detail}
              </p>
            ))}
          </div>
        </div>

        {/* 第二条 */}
        <div className="mb-8 border-l-4 border-green-500 pl-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            {contractContent.secondClause.title}
          </h3>
          <p className="text-gray-700 mb-4 leading-relaxed">{contractContent.secondClause.content}</p>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {contractContent.secondClause.details.map((detail, index) => (
                <div key={index} className="text-center p-3 bg-white rounded border-2 border-green-200">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {detail.split('：')[1]}
                  </div>
                  <div className="text-sm text-gray-600">{detail.split('：')[0]}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 签署区域 */}
      <div className="bg-white rounded-lg shadow-sm border p-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">{t('contract.signing.area')}</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {contractData.parties.map((party) => (
            <div key={party.id} className="text-center">
              <h4 className="font-medium text-gray-900 mb-2">{party.role}</h4>
              
              {/* 签名区域 */}
              <div className="border border-gray-300 rounded-lg h-32 mb-4 flex items-center justify-center bg-gray-50">
                {party.signed && party.signature ? (
                  <img
                    src={party.signature}
                    alt={`${party.name}${t('contract.signing.signature.of')}`}
                    className="max-h-20 max-w-full"
                  />
                ) : showSignatureInput === party.id ? (
                  <div className="w-full p-4">
                    <div className="mb-2">
                      <p className="text-sm text-gray-600 mb-2">{t('contract.signing.please.sign')}</p>
                      <canvas
                        ref={canvasRef}
                        width={200}
                        height={80}
                        className="border border-gray-300 rounded bg-white cursor-crosshair mx-auto"
                        onMouseDown={startDrawing}
                        onMouseMove={draw}
                        onMouseUp={stopDrawing}
                        onMouseLeave={stopDrawing}
                        style={{ touchAction: 'none' }}
                      />
                    </div>
                    <div className="flex justify-center space-x-2 mt-2">
                      <button
                        onClick={clearSignature}
                        className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded hover:bg-gray-200"
                      >
                        {t('contract.signing.clear')}
                      </button>
                      <button
                        onClick={() => handleConfirmSign(party.id)}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                      >
                        {t('contract.signing.confirm')}
                      </button>
                      <button
                        onClick={() => setShowSignatureInput(null)}
                        className="px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400"
                      >
                        {t('contract.signing.cancel')}
                      </button>
                    </div>
                  </div>
                ) : (
                  <button
                    onClick={() => handleSign(party.id)}
                    className="text-blue-600 hover:text-blue-800 flex items-center"
                  >
                    <PencilIcon className="w-5 h-5 mr-1" />
                    {t('contract.signing.click.sign')}
                  </button>
                )}
              </div>
              
              <p className="text-sm text-gray-700">{party.name}</p>
              {party.signed ? (
                <span className="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full mt-1">
                  {t('contract.signing.signed')}
                </span>
              ) : (
                <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full mt-1">
                  {t('contract.signing.pending')}
                </span>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-center space-x-4 mt-8">
        <button
          onClick={handleReject}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          {t('contract.signing.cancel')}
        </button>
        <button
          onClick={handleConfirmContract}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          {t('contract.signing.confirm')}
        </button>
      </div>

      {/* 底部提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <DocumentTextIcon className="w-5 h-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <p className="text-sm text-blue-800">
              {t('contract.signing.notice')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractSigning;