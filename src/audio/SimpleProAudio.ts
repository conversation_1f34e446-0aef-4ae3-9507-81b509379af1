import * as Tone from 'tone';

// 简化的专业音频系统，避免复杂的继承问题
export interface SimpleEQBand {
  frequency: number;
  gain: number;
  Q: number;
}

export class SimpleEQ {
  private filters: Tone.Filter[] = [];
  public input: Tone.Gain;
  public output: Tone.Gain;

  constructor(numBands: number = 4) {
    this.input = new Tone.Gain(1);
    this.output = new Tone.Gain(1);
    this.createBands(numBands);
  }

  private createBands(numBands: number) {
    const frequencies = [80, 250, 1000, 4000];
    let currentNode: Tone.ToneAudioNode = this.input;

    for (let i = 0; i < numBands; i++) {
      const filter = new Tone.Filter({
        frequency: frequencies[i] || 1000,
        type: 'peaking',
        Q: 1,
        gain: 0
      });
      
      currentNode.connect(filter);
      currentNode = filter as Tone.ToneAudioNode;
      this.filters.push(filter);
    }

    currentNode.connect(this.output);
  }

  setBand(index: number, params: Partial<SimpleEQBand>) {
    if (index >= 0 && index < this.filters.length) {
      const filter = this.filters[index];
      if (params.frequency) filter.frequency.value = params.frequency;
      if (params.gain !== undefined) filter.gain.value = params.gain;
      if (params.Q) filter.Q.value = params.Q;
    }
  }

  getBand(index: number): SimpleEQBand | null {
    if (index >= 0 && index < this.filters.length) {
      const filter = this.filters[index];
      return {
        frequency: typeof filter.frequency.value === 'number' ? filter.frequency.value : parseFloat(filter.frequency.value.toString()),
        gain: filter.gain.value,
        Q: filter.Q.value
      };
    }
    return null;
  }

  connect(destination: any) {
    this.output.connect(destination);
    return this;
  }

  dispose() {
    this.filters.forEach(filter => filter.dispose());
    this.input.dispose();
    this.output.dispose();
  }
}

export class SimpleCompressor {
  private compressor: Tone.Compressor;
  private makeupGain: Tone.Gain;
  public input: Tone.Gain;
  public output: Tone.Gain;

  constructor() {
    this.input = new Tone.Gain(1);
    this.makeupGain = new Tone.Gain(1);
    this.output = new Tone.Gain(1);
    
    this.compressor = new Tone.Compressor({
      threshold: -24,
      ratio: 4,
      attack: 0.003,
      release: 0.1,
      knee: 6
    });

    // 连接信号链
    this.input.connect(this.compressor);
    this.compressor.connect(this.makeupGain);
    this.makeupGain.connect(this.output);
  }

  setThreshold(value: number) {
    this.compressor.threshold.value = value;
  }

  setRatio(value: number) {
    this.compressor.ratio.value = value;
  }

  setAttack(value: number) {
    this.compressor.attack.value = value;
  }

  setRelease(value: number) {
    this.compressor.release.value = value;
  }

  setMakeupGain(value: number) {
    this.makeupGain.gain.value = Tone.dbToGain(value);
  }

  getGainReduction(): number {
    return Math.abs(this.compressor.reduction || 0);
  }

  connect(destination: any) {
    this.output.connect(destination);
    return this;
  }

  dispose() {
    this.compressor.dispose();
    this.makeupGain.dispose();
    this.input.dispose();
    this.output.dispose();
  }
}

export class SimpleReverb {
  private reverb: Tone.Reverb;
  private wetDry: Tone.CrossFade;
  private input: Tone.Gain;
  private output: Tone.Gain;

  constructor() {
    this.input = new Tone.Gain(1);
    this.output = new Tone.Gain(1);
    this.wetDry = new Tone.CrossFade(0.3);
    
    this.reverb = new Tone.Reverb({
      decay: 2.0,
      wet: 1
    });

    // 连接信号链
    this.input.connect(this.wetDry.a); // dry
    this.input.connect(this.reverb);
    this.reverb.connect(this.wetDry.b); // wet
    this.wetDry.connect(this.output);
  }

  setRoomSize(value: number) {
    // Tone.js Reverb doesn't have roomSize, using decay as alternative
    this.reverb.decay = value * 5; // Scale 0-1 to 0-5 seconds
  }

  setDecay(value: number) {
    this.reverb.decay = value;
  }

  setWetness(value: number) {
    this.wetDry.fade.value = value;
  }

  connect(destination: any) {
    this.output.connect(destination);
    return this;
  }

  dispose() {
    this.reverb.dispose();
    this.wetDry.dispose();
    this.input.dispose();
    this.output.dispose();
  }
}

export class SimpleChannelStrip {
  public id: string;
  private input: Tone.Gain;
  private output: Tone.Gain;
  private preGain: Tone.Gain;
  private eq: SimpleEQ;
  private compressor: SimpleCompressor;
  
  constructor(id: string) {
    this.id = id;
    this.input = new Tone.Gain(1);
    this.output = new Tone.Gain(1);
    this.preGain = new Tone.Gain(1);
    this.eq = new SimpleEQ(4);
    this.compressor = new SimpleCompressor();

    // 连接信号链
    this.input.connect(this.preGain);
    this.preGain.connect(this.eq.input);
    this.eq.connect(this.compressor.input);
    this.compressor.connect(this.output);
  }

  setGain(dB: number) {
    this.preGain.gain.value = Tone.dbToGain(dB);
  }

  getEQ(): SimpleEQ {
    return this.eq;
  }

  getCompressor(): SimpleCompressor {
    return this.compressor;
  }

  connect(destination: any) {
    this.output.connect(destination);
    return this;
  }

  dispose() {
    this.eq.dispose();
    this.compressor.dispose();
    this.preGain.dispose();
    this.input.dispose();
    this.output.dispose();
  }
}

export class SimpleProAudioManager {
  private static instance: SimpleProAudioManager;
  private masterGain: Tone.Gain;
  private tracks: Map<string, SimpleChannelStrip> = new Map();
  private isInitialized = false;

  private constructor() {
    this.masterGain = new Tone.Gain(0.8).toDestination();
  }

  static getInstance(): SimpleProAudioManager {
    if (!SimpleProAudioManager.instance) {
      SimpleProAudioManager.instance = new SimpleProAudioManager();
    }
    return SimpleProAudioManager.instance;
  }

  async initialize(): Promise<void> {
    if (!this.isInitialized) {
      await Tone.start();
      this.isInitialized = true;
      console.log('Simple Pro Audio Manager initialized');
    }
  }

  async createTrack(id: string, name: string): Promise<{ channelStrip: SimpleChannelStrip }> {
    const channelStrip = new SimpleChannelStrip(id);
    channelStrip.connect(this.masterGain);
    this.tracks.set(id, channelStrip);
    
    return { channelStrip };
  }

  getTrack(id: string) {
    const channelStrip = this.tracks.get(id);
    return channelStrip ? { channelStrip } : null;
  }

  removeTrack(id: string) {
    const track = this.tracks.get(id);
    if (track) {
      track.dispose();
      this.tracks.delete(id);
    }
  }

  getPerformanceMetrics() {
    return {
      cpuUsage: Math.random() * 50, // 模拟值
      latency: 8.5,
      droppedFrames: 0
    };
  }
}

export const simpleProAudioManager = SimpleProAudioManager.getInstance();