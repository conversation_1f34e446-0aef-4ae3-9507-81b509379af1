import { io, Socket } from 'socket.io-client';

class DAWSocketService {
  private socket: Socket | null = null;
  private projectId: string | null = null;
  private userId: string | null = null;
  private callbacks: Map<string, Function[]> = new Map();

  connect(projectId: string, userId: string, userName: string) {
    if (this.socket) {
      this.disconnect();
    }

    this.socket = io('http://localhost:8080/daw', {
      withCredentials: true,
    });

    this.projectId = projectId;
    this.userId = userId;

    this.socket.on('connect', () => {
      console.log('Connected to DAW service');
      this.joinProject(projectId, userId, userName);
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from DAW service');
    });

    this.socket.on('project-joined', (data) => {
      console.log('Joined project:', data);
      this.emit('project-joined', data);
    });

    this.socket.on('user-joined', (data) => {
      console.log('User joined:', data);
      this.emit('user-joined', data);
    });

    this.socket.on('user-left', (data) => {
      console.log('User left:', data);
      this.emit('user-left', data);
    });

    this.socket.on('project-changed', (data) => {
      console.log('Project changed:', data);
      this.emit('project-changed', data);
    });

    this.socket.on('track-added', (data) => {
      console.log('Track added:', data);
      this.emit('track-added', data);
    });

    this.socket.on('track-updated', (data) => {
      console.log('Track updated:', data);
      this.emit('track-updated', data);
    });

    this.socket.on('track-deleted', (data) => {
      console.log('Track deleted:', data);
      this.emit('track-deleted', data);
    });

    this.socket.on('note-added', (data) => {
      console.log('Note added:', data);
      this.emit('note-added', data);
    });

    this.socket.on('note-updated', (data) => {
      console.log('Note updated:', data);
      this.emit('note-updated', data);
    });

    this.socket.on('note-deleted', (data) => {
      console.log('Note deleted:', data);
      this.emit('note-deleted', data);
    });

    this.socket.on('transport-changed', (data) => {
      console.log('Transport changed:', data);
      this.emit('transport-changed', data);
    });

    this.socket.on('cursor-moved', (data) => {
      console.log('Cursor moved:', data);
      this.emit('cursor-moved', data);
    });

    this.socket.on('project-synced', (data) => {
      console.log('Project synced:', data);
      this.emit('project-synced', data);
    });

    this.socket.on('error', (data) => {
      console.error('Socket error:', data);
      this.emit('error', data);
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.projectId = null;
    this.userId = null;
    this.callbacks.clear();
  }

  private joinProject(projectId: string, userId: string, userName: string) {
    if (this.socket) {
      this.socket.emit('join-project', { projectId, userId, userName });
    }
  }

  // Project operations
  updateProject(changes: any) {
    if (this.socket && this.projectId && this.userId) {
      this.socket.emit('project-update', {
        projectId: this.projectId,
        changes,
        userId: this.userId,
      });
    }
  }

  // Track operations
  addTrack(track: any) {
    if (this.socket && this.projectId && this.userId) {
      this.socket.emit('track-add', {
        projectId: this.projectId,
        track,
        userId: this.userId,
      });
    }
  }

  updateTrack(trackId: string, updates: any) {
    if (this.socket && this.projectId && this.userId) {
      this.socket.emit('track-update', {
        projectId: this.projectId,
        trackId,
        updates,
        userId: this.userId,
      });
    }
  }

  deleteTrack(trackId: string) {
    if (this.socket && this.projectId && this.userId) {
      this.socket.emit('track-delete', {
        projectId: this.projectId,
        trackId,
        userId: this.userId,
      });
    }
  }

  // Note operations
  addNote(trackId: string, note: any) {
    if (this.socket && this.projectId && this.userId) {
      this.socket.emit('note-add', {
        projectId: this.projectId,
        trackId,
        note,
        userId: this.userId,
      });
    }
  }

  updateNote(trackId: string, noteId: string, updates: any) {
    if (this.socket && this.projectId && this.userId) {
      this.socket.emit('note-update', {
        projectId: this.projectId,
        trackId,
        noteId,
        updates,
        userId: this.userId,
      });
    }
  }

  deleteNote(trackId: string, noteId: string) {
    if (this.socket && this.projectId && this.userId) {
      this.socket.emit('note-delete', {
        projectId: this.projectId,
        trackId,
        noteId,
        userId: this.userId,
      });
    }
  }

  // Transport operations
  updateTransport(transportState: any) {
    if (this.socket && this.projectId && this.userId) {
      this.socket.emit('transport-update', {
        projectId: this.projectId,
        transportState,
        userId: this.userId,
      });
    }
  }

  // Cursor operations
  updateCursor(cursor: any) {
    if (this.socket && this.projectId && this.userId) {
      this.socket.emit('cursor-update', {
        projectId: this.projectId,
        cursor,
        userId: this.userId,
      });
    }
  }

  // Sync operations
  requestSync() {
    if (this.socket && this.projectId) {
      this.socket.emit('request-sync', {
        projectId: this.projectId,
      });
    }
  }

  // Event handling
  on(event: string, callback: Function) {
    if (!this.callbacks.has(event)) {
      this.callbacks.set(event, []);
    }
    this.callbacks.get(event)!.push(callback);
  }

  off(event: string, callback?: Function) {
    if (callback) {
      const callbacks = this.callbacks.get(event) || [];
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.callbacks.delete(event);
    }
  }

  private emit(event: string, data: any) {
    const callbacks = this.callbacks.get(event) || [];
    callbacks.forEach(callback => callback(data));
  }

  // Getters
  get isConnected(): boolean {
    return this.socket?.connected || false;
  }

  get currentProjectId(): string | null {
    return this.projectId;
  }

  get currentUserId(): string | null {
    return this.userId;
  }
}

export const dawSocketService = new DAWSocketService();