declare module 'midi-parser-js' {
  interface MidiEvent {
    type: number;
    deltaTime: number;
    data?: number[];
  }

  interface MidiTrack {
    events: MidiEvent[];
  }

  interface ParsedMidi {
    tracks: MidiTrack[];
    timeDivision: number;
  }

  export function parse(midiArray: number[]): ParsedMidi;

  const MidiParser: {
    parse: (midiArray: number[]) => ParsedMidi;
  };

  export default MidiParser;
}