import React, { useMemo, useState } from 'react';
import { PlusIcon, MinusIcon, UserGroupIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../../../contexts/LanguageContext';

interface RoleSetupStepProps {
  spaceData: any;
  setSpaceData: (data: any) => void;
  getStandardRevenueShare: (role: string) => number;
}

const RoleSetupStep: React.FC<RoleSetupStepProps> = ({ 
  spaceData, 
  setSpaceData, 
  getStandardRevenueShare 
}) => {
  const { language } = useLanguage();
  const [showRevenueSettings, setShowRevenueSettings] = useState(false);

  const availableRoles = useMemo(() => [
    language === 'zh' ? '作词' : 'Lyrics',
    language === 'zh' ? '作曲' : 'Composition',
    language === 'zh' ? '编曲' : 'Arrangement',
    language === 'zh' ? '演唱' : 'Vocals',
    language === 'zh' ? '混音' : 'Mixing',
    language === 'zh' ? '制作' : 'Production',
    language === 'zh' ? '其他' : 'Other'
  ], [language]);

  const handleRoleToggle = (role: string) => {
    setSpaceData((prev: any) => {
      const isRemoving = prev.selectedRoles.includes(role);
      if (isRemoving) {
        // Remove role and its assignments
        const newRoleAssignments = { ...prev.roleAssignments };
        delete newRoleAssignments[role];
        const newRevenueShares = { ...prev.revenueShares };
        delete newRevenueShares[role];
        
        // If revenue settings were shown and we're removing a role, keep them shown
        // but if all roles are removed, hide revenue settings
        const newSelectedRoles = prev.selectedRoles.filter((r: string) => r !== role);
        if (newSelectedRoles.length === 0) {
          setShowRevenueSettings(false);
        }
        
        return {
          ...prev,
          selectedRoles: newSelectedRoles,
          roleAssignments: newRoleAssignments,
          revenueShares: newRevenueShares
        };
      } else {
        // Add role and initialize with 1 person
        // Don't automatically show revenue settings - let user click the button
        return {
          ...prev,
          selectedRoles: [...prev.selectedRoles, role],
          roleAssignments: {
            ...prev.roleAssignments,
            [role]: { count: 1, members: [] }
          },
          revenueShares: {
            ...prev.revenueShares,
            [role]: getStandardRevenueShare(role)
          }
        };
      }
    });
  };

  const updateRoleMemberCount = (role: string, count: number) => {
    setSpaceData((prev: any) => ({
      ...prev,
      roleAssignments: {
        ...prev.roleAssignments,
        [role]: { 
          ...prev.roleAssignments[role], 
          count: Math.max(1, Math.min(5, count)) 
        }
      }
    }));
  };

  const updateRevenueShare = (role: string, share: number) => {
    setSpaceData((prev: any) => ({
      ...prev,
      revenueShares: {
        ...prev.revenueShares,
        [role]: Math.max(0, Math.min(100, share))
      }
    }));
  };

  const getTotalPercentage = () => {
    return spaceData.selectedRoles.reduce((sum: number, role: string) => {
      return sum + (spaceData.revenueShares[role] || getStandardRevenueShare(role));
    }, 0);
  };

  const getCurrentRevenueShare = (role: string) => {
    return spaceData.revenueShares[role] || getStandardRevenueShare(role);
  };

  const getRoleDescription = (role: string) => {
    const descriptions: {[key: string]: string} = {
      [language === 'zh' ? '作词' : 'Lyrics']: language === 'zh' ? '负责歌词创作，表达情感和故事' : 'Responsible for lyric creation, expressing emotions and stories',
      [language === 'zh' ? '作曲' : 'Composition']: language === 'zh' ? '创作旋律和和弦进行' : 'Create melodies and chord progressions',
      [language === 'zh' ? '编曲' : 'Arrangement']: language === 'zh' ? '为歌曲添加乐器配置和层次' : 'Add instrument arrangements and layers to songs',
      [language === 'zh' ? '演唱' : 'Vocals']: language === 'zh' ? '主唱和和声演唱' : 'Lead and backing vocals',
      [language === 'zh' ? '混音' : 'Mixing']: language === 'zh' ? '平衡各轨道音量和音效处理' : 'Balance track volumes and audio effects processing',
      [language === 'zh' ? '制作' : 'Production']: language === 'zh' ? '整体制作和质量把控' : 'Overall production and quality control',
      [language === 'zh' ? '其他' : 'Other']: language === 'zh' ? '其他特殊技能或贡献' : 'Other special skills or contributions'
    };
    return descriptions[role] || '';
  };

  return (
    <div className="space-y-8">
      {/* Step Title */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {language === 'zh' ? '设置所需角色' : 'Set Required Roles'}
        </h2>
        <p className="text-gray-600">
          {language === 'zh' ? '选择项目所需的角色和人数，并设置收益分成比例' : 'Select required roles and headcount for the project, and set revenue sharing ratios'}
        </p>
      </div>

      {/* Role Selection */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {language === 'zh' ? '选择需要的角色' : 'Select Required Roles'} <span className="text-red-500">*</span>
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {availableRoles.map((role) => (
            <div 
              key={role} 
              className={`border-2 rounded-lg p-4 transition-all ${
                spaceData.selectedRoles.includes(role)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  checked={spaceData.selectedRoles.includes(role)}
                  onChange={() => handleRoleToggle(role)}
                  className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div className="flex-1">
                  <h4 className="text-base font-medium text-gray-900 mb-1">
                    {role}
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">
                    {getRoleDescription(role)}
                  </p>
                  
                  {spaceData.selectedRoles.includes(role) && (
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <UserGroupIcon className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-600">
                          {language === 'zh' ? '人数:' : 'Count:'}
                        </span>
                        <div className="flex items-center border border-gray-300 rounded">
                          <button
                            type="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              updateRoleMemberCount(role, (spaceData.roleAssignments[role]?.count || 1) - 1);
                            }}
                            className="p-1 text-gray-600 hover:bg-gray-100 rounded-l"
                            disabled={(spaceData.roleAssignments[role]?.count || 1) <= 1}
                          >
                            <MinusIcon className="w-3 h-3" />
                          </button>
                          <span className="px-3 py-1 text-sm font-medium min-w-[2rem] text-center">
                            {spaceData.roleAssignments[role]?.count || 1}
                          </span>
                          <button
                            type="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              updateRoleMemberCount(role, (spaceData.roleAssignments[role]?.count || 1) + 1);
                            }}
                            className="p-1 text-gray-600 hover:bg-gray-100 rounded-r"
                            disabled={(spaceData.roleAssignments[role]?.count || 1) >= 5}
                          >
                            <PlusIcon className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                      <span className="text-xs text-gray-500">
                        {language === 'zh' ? '(最多5人)' : '(Max 5 people)'}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Configure Revenue Button */}
      {spaceData.selectedRoles.length > 0 && !showRevenueSettings && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-blue-900 mb-2">
                {language === 'zh' ? '配置收益分成' : 'Configure Revenue Sharing'}
              </h3>
              <p className="text-blue-700 text-sm">
                {language === 'zh' 
                  ? `已选择 ${spaceData.selectedRoles.length} 个角色，点击下方按钮设置各角色的收益分成比例`
                  : `${spaceData.selectedRoles.length} roles selected. Click the button below to set revenue sharing ratios for each role`
                }
              </p>
            </div>
            <button
              type="button"
              onClick={() => setShowRevenueSettings(true)}
              className="flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <span>
                {language === 'zh' ? '设置分成比例' : 'Set Revenue Sharing'}
              </span>
              <ArrowRightIcon className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Revenue Sharing */}
      {spaceData.selectedRoles.length > 0 && showRevenueSettings && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {language === 'zh' ? '设置分成比例' : 'Set Revenue Sharing'}
          </h3>
          <p className="text-sm text-gray-600 mb-6">
            {language === 'zh' 
              ? '调整各角色的收益分成比例，总计必须为100%。系统已根据行业标准预设了推荐比例。'
              : 'Adjust revenue sharing ratios for each role. Total must be 100%. System has preset recommended ratios based on industry standards.'
            }
          </p>
          
          <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
            <div className="space-y-6">
              {spaceData.selectedRoles.map((role: string) => {
                const currentShare = getCurrentRevenueShare(role);
                const standardShare = getStandardRevenueShare(role);
                return (
                  <div key={role} className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-gray-700 font-medium flex-1 min-w-[80px]">
                          {role}
                        </span>
                        {currentShare !== standardShare && (
                          <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                            {language === 'zh' ? '已调整' : 'Adjusted'}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-4">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={currentShare}
                          onChange={(e) => updateRevenueShare(role, parseInt(e.target.value) || 0)}
                          className="w-20 px-3 py-2 text-center border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <span className="text-gray-500 text-sm">%</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={currentShare}
                        onChange={(e) => updateRevenueShare(role, parseInt(e.target.value))}
                        className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        style={{
                          background: `linear-gradient(to right, #3B82F6 0%, #3B82F6 ${currentShare}%, #E5E7EB ${currentShare}%, #E5E7EB 100%)`
                        }}
                      />
                      <button
                        onClick={() => updateRevenueShare(role, standardShare)}
                        className="text-xs text-blue-600 hover:text-blue-800 underline"
                      >
                        {language === 'zh' ? '重置' : 'Reset'}
                      </button>
                    </div>
                  </div>
                );
              })}
              
              {/* Total */}
              <div className="border-t pt-4 mt-4">
                <div className="flex items-center justify-between">
                  <span className="text-lg font-medium text-gray-900">
                    {language === 'zh' ? '总计' : 'Total'}
                  </span>
                  <span className={`text-2xl font-bold ${
                    getTotalPercentage() === 100 ? 'text-green-600' : 
                    getTotalPercentage() > 100 ? 'text-red-600' : 'text-yellow-600'
                  }`}>
                    {getTotalPercentage()}%
                  </span>
                </div>
                {getTotalPercentage() !== 100 && (
                  <div className={`mt-3 p-3 rounded-lg text-sm ${
                    getTotalPercentage() > 100 ? 'bg-red-50 text-red-700' : 'bg-yellow-50 text-yellow-700'
                  }`}>
                    {getTotalPercentage() > 100 
                      ? (language === 'zh' ? '⚠️ 分成比例超过100%，请调整各角色比例' : '⚠️ Revenue sharing exceeds 100%, please adjust role ratios')
                      : (language === 'zh' ? '⚠️ 分成比例不足100%，请调整各角色比例' : '⚠️ Revenue sharing is less than 100%, please adjust role ratios')
                    }
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Revenue Visualization */}
      {spaceData.selectedRoles.length > 0 && showRevenueSettings && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {language === 'zh' ? '分成比例可视化' : 'Revenue Sharing Visualization'}
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Pie Chart */}
            <div className="flex justify-center">
              <div className="relative w-64 h-64">
                <svg viewBox="0 0 100 100" className="w-full h-full transform -rotate-90">
                  {spaceData.selectedRoles.map((role: string, index: number) => {
                    const percentage = getCurrentRevenueShare(role);
                    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#F97316'];
                    const offset = spaceData.selectedRoles.slice(0, index).reduce((sum: number, prevRole: string) => sum + getCurrentRevenueShare(prevRole), 0);
                    const strokeDasharray = `${percentage} ${100 - percentage}`;
                    const strokeDashoffset = -offset;
                    
                    return (
                      <circle
                        key={role}
                        cx="50"
                        cy="50"
                        r="15.915"
                        fill="transparent"
                        stroke={colors[index % colors.length]}
                        strokeWidth="4"
                        strokeDasharray={strokeDasharray}
                        strokeDashoffset={strokeDashoffset}
                        className="transition-all duration-300"
                      />
                    );
                  })}
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${
                      getTotalPercentage() === 100 ? 'text-green-600' : 
                      getTotalPercentage() > 100 ? 'text-red-600' : 'text-yellow-600'
                    }`}>
                      {getTotalPercentage()}%
                    </div>
                    <div className="text-sm text-gray-500">
                      {language === 'zh' ? '总计' : 'Total'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Legend */}
            <div className="space-y-4">
              <h4 className="text-base font-medium text-gray-900">
                {language === 'zh' ? '分成详情' : 'Sharing Details'}
              </h4>
              <div className="space-y-3">
                {spaceData.selectedRoles.map((role: string, index: number) => {
                  const percentage = getCurrentRevenueShare(role);
                  const memberCount = spaceData.roleAssignments[role]?.count || 1;
                  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#F97316'];
                  return (
                    <div key={role} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div
                          className="w-4 h-4 rounded"
                          style={{ backgroundColor: colors[index % colors.length] }}
                        ></div>
                        <div>
                          <span className="text-gray-900 font-medium">{role}</span>
                          <span className="text-gray-500 text-sm ml-2">
                            ({memberCount} {language === 'zh' ? '人' : 'people'})
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-gray-900">{percentage}%</div>
                        {memberCount > 1 && (
                          <div className="text-xs text-gray-500">
                            {(percentage / memberCount).toFixed(1)}%/{language === 'zh' ? '人' : 'person'}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoleSetupStep;