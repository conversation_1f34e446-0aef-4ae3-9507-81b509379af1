import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { 
  PlayIcon, 
  PauseIcon,
  HeartIcon, 
  ShareIcon, 
  ChatBubbleLeftIcon,
  ArrowLeftIcon,
  SpeakerWaveIcon,
  BookmarkIcon,
  EllipsisHorizontalIcon,
  CubeTransparentIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import { useWorks } from '../contexts/WorksContext';
import { useLanguage } from '../contexts/LanguageContext';

interface Comment {
  id: number;
  user: {
    id: number;
    name: string;
    avatar: string;
  };
  content: string;
  timestamp: string;
  likes: number;
  liked: boolean;
}

interface WorkDetailData {
  id: number;
  title: string;
  artist: {
    id: number;
    name: string;
    avatar: string;
    verified: boolean;
  };
  collaborator?: {
    id: number;
    name: string;
    avatar: string;
  };
  coverUrl: string;
  audioUrl?: string;
  duration: string;
  genre: string;
  description: string;
  plays: number;
  likes: number;
  comments: number;
  liked: boolean;
  bookmarked: boolean;
  releaseDate: string;
  tags: string[];
  copyrightInfo: {
    composer: string;
    lyricist: string;
    producer: string;
    label: string;
  };
  relatedWorks: Array<{
    id: number;
    title: string;
    artist: string;
    coverUrl: string;
    duration: string;
    plays: number;
  }>;
  commentsList: Comment[];
}

const WorkDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { publishedWorks } = useWorks();
  const [work, setWork] = useState<WorkDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [newComment, setNewComment] = useState('');
  const { t } = useLanguage();

  useEffect(() => {
    const fetchWorkDetail = async () => {
      try {
        // Mock data for demonstration
        setWork({
          id: parseInt(id || '1'),
          title: t('mock.work.title.galaxy.travel'),
          artist: {
            id: 1,
            name: t('mock.artist.chen.xiaowei'),
            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
            verified: true
          },
          collaborator: {
            id: 2,
            name: t('mock.artist.tianyu.music'),
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80'
          },
          coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
          duration: '2:15 / 4:30',
          genre: t('mock.genre.pop'),
          description: t('mock.work.desc.galaxy.travel'),
          plays: 28000,
          likes: 2800,
          comments: 128,
          liked: false,
          bookmarked: false,
          releaseDate: '2023-08-15',
          tags: [t('mock.genre.pop'), t('mock.genre.electronic'), t('mock.genre.healing'), t('mock.genre.original')],
          copyrightInfo: {
            composer: t('mock.artist.chen.xiaowei'),
            lyricist: t('mock.artist.chen.xiaowei'),
            producer: t('mock.studio.tianyu'),
            label: t('mock.label.ibom')
          },
          relatedWorks: [
            {
              id: 2,
              title: t('mock.work.title.mirage'),
              artist: t('mock.artist.yuan.yuxi'),
              coverUrl: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
              duration: '03:45',
              plays: 15400
            },
            {
              id: 3,
              title: t('mock.work.title.night.sky.wish'),
              artist: t('mock.artist.yang.yuming'),
              coverUrl: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
              duration: '04:12',
              plays: 22100
            },
            {
              id: 4,
              title: t('mock.work.title.spring.story'),
              artist: t('mock.artist.xiaoyu.music'),
              coverUrl: 'https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
              duration: '03:28',
              plays: 18600
            },
            {
              id: 5,
              title: t('mock.work.title.city.nightscape'),
              artist: t('mock.artist.electronic.band'),
              coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
              duration: '05:21',
              plays: 31200
            }
          ],
          commentsList: [
            {
              id: 1,
              user: {
                id: 1,
                name: t('mock.artist.zhang.meiqi'),
                avatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80'
              },
              content: t('mock.comment.1'),
              timestamp: '3' + t('time.hours.ago'),
              likes: 34,
              liked: false
            },
            {
              id: 2,
              user: {
                id: 2,
                name: t('mock.artist.wang.zhiqiang'),
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80'
              },
              content: t('mock.comment.2'),
              timestamp: '5' + t('time.hours.ago'),
              likes: 28,
              liked: true
            }
          ]
        });
      } catch (error) {
        console.error('Failed to fetch work detail:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWorkDetail();
  }, [id]);

  const handleLike = () => {
    if (work) {
      const newLiked = !work.liked;
      setWork({
        ...work,
        liked: newLiked,
        likes: newLiked ? work.likes + 1 : work.likes - 1
      });
    }
  };

  const handleBookmark = () => {
    if (work) {
      setWork({
        ...work,
        bookmarked: !work.bookmarked
      });
    }
  };

  const handleCommentLike = (commentId: number) => {
    if (work) {
      const updatedComments = work.commentsList.map(comment => {
        if (comment.id === commentId) {
          return {
            ...comment,
            liked: !comment.liked,
            likes: comment.liked ? comment.likes - 1 : comment.likes + 1
          };
        }
        return comment;
      });
      setWork({ ...work, commentsList: updatedComments });
    }
  };

  const handleSubmitComment = () => {
    if (work && newComment.trim()) {
      const comment: Comment = {
        id: Date.now(),
        user: {
          id: 999,
          name: t('mock.user.me'),
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80'
        },
        content: newComment,
        timestamp: t('time.now'),
        likes: 0,
        liked: false
      };
      setWork({
        ...work,
        commentsList: [comment, ...work.commentsList],
        comments: work.comments + 1
      });
      setNewComment('');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!work) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{t('work.detail.not.exist')}</h2>
          <button
            onClick={() => navigate('/works')}
            className="text-blue-600 hover:text-blue-800"
          >
            {t('work.detail.back.to.list')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-4">
            <button
              onClick={() => navigate('/works')}
              className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ArrowLeftIcon className="h-6 w-6" />
            </button>
            <h1 className="text-xl font-semibold text-gray-900">{t('works.title')}</h1>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Player Section */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
              <div className="relative">
                <img 
                  src={work.coverUrl} 
                  alt={work.title}
                  className="w-full h-96 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>
                
                {/* Play Controls Overlay */}
                <div className="absolute bottom-6 left-6 right-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <button 
                      onClick={() => setIsPlaying(!isPlaying)}
                      className="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-4 transition-all shadow-lg"
                    >
                      {isPlaying ? (
                        <PauseIcon className="h-8 w-8 text-gray-900" />
                      ) : (
                        <PlayIcon className="h-8 w-8 text-gray-900" />
                      )}
                    </button>
                    
                    <div className="flex-1">
                      <h2 className="text-2xl font-bold text-white mb-1">{work.title}</h2>
                      <div className="flex items-center space-x-2 text-white">
                        <img 
                          src={work.artist.avatar}
                          alt={work.artist.name}
                          className="w-6 h-6 rounded-full"
                        />
                        <span className="font-medium">{work.artist.name}</span>
                        {work.artist.verified && (
                          <div className="bg-blue-600 text-white px-2 py-0.5 rounded-full text-xs">
                            {t('work.detail.verified')}
                          </div>
                        )}
                        {work.collaborator && (
                          <>
                            <span className="text-gray-300">x</span>
                            <img 
                              src={work.collaborator.avatar}
                              alt={work.collaborator.name}
                              className="w-6 h-6 rounded-full"
                            />
                            <span className="font-medium">{work.collaborator.name}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="mb-2">
                    <div className="bg-white bg-opacity-30 rounded-full h-1">
                      <div 
                        className="bg-white rounded-full h-1 transition-all duration-1000"
                        style={{ width: `${(currentTime / 270) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-white text-sm">
                    <span>{work.duration}</span>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <SpeakerWaveIcon className="h-4 w-4" />
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={volume}
                          onChange={(e) => setVolume(parseFloat(e.target.value))}
                          className="w-20 h-1 bg-white bg-opacity-30 rounded-full appearance-none cursor-pointer"
                        />
                      </div>
                      <button className="p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors">
                        <EllipsisHorizontalIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Bar */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6">
                  <button 
                    onClick={handleLike}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-full transition-colors ${
                      work.liked 
                        ? 'bg-red-50 text-red-600' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {work.liked ? (
                      <HeartIconSolid className="h-5 w-5" />
                    ) : (
                      <HeartIcon className="h-5 w-5" />
                    )}
                    <span>{work.likes}{t('work.detail.likes')}</span>
                  </button>
                  
                  <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors">
                    <ChatBubbleLeftIcon className="h-5 w-5" />
                    <span>{work.comments} {t('work.detail.comment')}</span>
                  </button>
                  
                  <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors">
                    <ShareIcon className="h-5 w-5" />
                    <span>{t('work.detail.share')}</span>
                  </button>
                </div>
                
                <button 
                  onClick={handleBookmark}
                  className={`p-2 rounded-full transition-colors ${
                    work.bookmarked 
                      ? 'bg-blue-50 text-blue-600' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <BookmarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Description & Tags */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">{t('work.detail.intro')}</h3>
              <p className="text-gray-600 leading-relaxed mb-4">{work.description}</p>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {work.tags.map((tag, index) => (
                  <span 
                    key={index}
                    className="px-3 py-1 bg-blue-50 text-blue-600 text-sm rounded-full font-medium"
                  >
                    {tag}
                  </span>
                ))}
              </div>
              
              <div className="text-sm text-gray-500">
                {t('work.detail.release.time')}：{work.releaseDate} • {t('work.detail.plays.count')}：{work.plays.toLocaleString()}
              </div>
            </div>

            {/* Comments Section */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-6">{t('work.detail.comments.title')} ({work.comments})</h3>
              
              {/* Add Comment */}
              <div className="mb-6">
                <div className="flex space-x-3">
                  <img 
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
                    alt="Your avatar"
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <textarea
                      placeholder={t('work.detail.write.comment')}
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                      rows={3}
                    />
                    <div className="mt-3 flex justify-end">
                      <button 
                        onClick={handleSubmitComment}
                        disabled={!newComment.trim()}
                        className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                      >
                        {t('work.detail.post')}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Comments List */}
              <div className="space-y-6">
                {work.commentsList.map((comment) => (
                  <div key={comment.id} className="flex space-x-3">
                    <img 
                      src={comment.user.avatar}
                      alt={comment.user.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="font-medium text-gray-900">{comment.user.name}</span>
                          <span className="text-sm text-gray-500">{comment.timestamp}</span>
                        </div>
                        <p className="text-gray-700 leading-relaxed">{comment.content}</p>
                      </div>
                      <div className="mt-2 flex items-center space-x-4">
                        <button 
                          onClick={() => handleCommentLike(comment.id)}
                          className={`flex items-center space-x-1 text-sm transition-colors ${
                            comment.liked ? 'text-red-600' : 'text-gray-500 hover:text-red-600'
                          }`}
                        >
                          {comment.liked ? (
                            <HeartIconSolid className="h-4 w-4" />
                          ) : (
                            <HeartIcon className="h-4 w-4" />
                          )}
                          <span>{comment.likes}</span>
                        </button>
                        <button className="text-sm text-gray-500 hover:text-gray-700 transition-colors">
                          {t('work.detail.reply')}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Copyright Info */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">{t('work.detail.copyright')}</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">{t('work.detail.composer')}</span>
                  <span className="text-gray-900 font-medium">{work.copyrightInfo.composer}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">{t('work.detail.lyricist')}</span>
                  <span className="text-gray-900 font-medium">{work.copyrightInfo.lyricist}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">{t('work.detail.producer')}</span>
                  <span className="text-gray-900 font-medium">{work.copyrightInfo.producer}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">{t('work.detail.label')}</span>
                  <span className="text-gray-900 font-medium">{work.copyrightInfo.label}</span>
                </div>
              </div>
            </div>

            {/* NFT Information */}
            {(() => {
              // 查找当前作品的NFT信息 - 支持字符串和数字ID匹配
              const publishedWork = publishedWorks.find(w => 
                w.id.toString() === id || 
                (typeof w.id === 'string' && w.id === id)
              );
              if (publishedWork?.nft) {
                return (
                  <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-2xl shadow-lg p-6 border border-purple-200">
                    <div className="flex items-center gap-3 mb-4">
                      <CubeTransparentIcon className="w-6 h-6 text-purple-600" />
                      <h3 className="text-lg font-bold text-gray-900">{t('nft.digital.collectible')}</h3>
                      <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">
                        {t('nft.limited.edition')}
                      </span>
                    </div>
                    
                    <div className="space-y-3 text-sm mb-6">
                      <div className="flex justify-between">
                        <span className="text-gray-500">{t('nft.id')}</span>
                        <span className="text-gray-900 font-mono font-medium">{publishedWork.nft.id}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">{t('nft.token.id')}</span>
                        <span className="text-gray-900 font-mono font-medium">{publishedWork.nft.tokenId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">{t('nft.price')}</span>
                        <span className="text-gray-900 font-medium">{publishedWork.nft.price} {publishedWork.nft.currency}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">{t('nft.release.time')}</span>
                        <span className="text-gray-900 font-medium">
                          {new Date(publishedWork.nft.createdAt).toLocaleDateString('zh-CN')}
                        </span>
                      </div>
                    </div>
                    
                    {publishedWork.nft.isForSale && (
                      <button 
                        onClick={() => {
                          alert(`${t('nft.buy.alert')}\n\n${t('nft.buy.work')}: ${work?.title}\n${t('nft.id')}: ${publishedWork.nft?.id}\n${t('nft.buy.price')}: ${publishedWork.nft?.price} ${publishedWork.nft?.currency}\n\n${t('nft.buy.coming.soon')}`);
                        }}
                        className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-200 font-medium"
                      >
                        <CurrencyDollarIcon className="w-5 h-5" />
                        {t('nft.buy.now')} ({publishedWork.nft.price} {publishedWork.nft.currency})
                      </button>
                    )}
                  </div>
                );
              }
              return null;
            })()}

            {/* Related Works */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">{t('work.detail.related.works')}</h3>
              <div className="space-y-4">
                {work.relatedWorks.map((relatedWork) => (
                  <div 
                    key={relatedWork.id} 
                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={() => navigate(`/works/${relatedWork.id}`)}
                  >
                    <img 
                      src={relatedWork.coverUrl}
                      alt={relatedWork.title}
                      className="w-12 h-12 rounded object-cover"
                    />
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">{relatedWork.title}</h4>
                      <p className="text-xs text-gray-500 truncate">{relatedWork.artist}</p>
                      <div className="flex items-center space-x-2 text-xs text-gray-400 mt-1">
                        <span>{relatedWork.duration}</span>
                        <span>•</span>
                        <span>{(relatedWork.plays / 1000).toFixed(1)}{t('work.detail.plays.k')}</span>
                      </div>
                    </div>
                    <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                      <PlayIcon className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkDetail;