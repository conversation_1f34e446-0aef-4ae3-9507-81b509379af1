import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { XMarkIcon, EyeIcon, EyeSlashIcon, MusicalNoteIcon, ChevronDownIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';
import { ApiService } from '../../services/api';
import { useUser } from '../../contexts/UserContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { switchToRole } from '../../utils/mockData';
import { UserRole, USER_ROLE_LABELS } from '../../types/user';

interface LoginProps {
  onClose?: () => void;
  onSwitchToRegister?: () => void;
}

const Login: React.FC<LoginProps> = ({ onClose, onSwitchToRegister }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole>('user');
  const [showAppsMenu, setShowAppsMenu] = useState(false);
  const navigate = useNavigate();
  const { setUser } = useUser();
  const { language } = useLanguage();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (process.env.NODE_ENV === 'development') {
        // 开发模式下直接使用mock数据
        const mockUser = switchToRole(selectedRole);
        setUser(mockUser);
        localStorage.setItem('user', JSON.stringify(mockUser));
        onClose?.() || navigate('/');
      } else {
        // 生产模式下使用正常登录
        await ApiService.login(formData);
        onClose?.() || navigate('/');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || (language === 'zh' ? '登录失败' : 'Login failed'));
    } finally {
      setLoading(false);
    }
  };

  const handleOAuthLogin = (provider: string) => {
    // 跳转到OAuth登录页面
    window.location.href = `${process.env.REACT_APP_API_URL}/auth/oauth2/${provider}`;
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md relative overflow-hidden transform scale-[0.8]">
        {/* Close button */}
        {onClose && (
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors z-10"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        )}

        {/* Header with gradient background */}
        <div className="bg-gradient-to-r from-blue-600 to-slate-800 p-3 text-white relative">
          {/* Apps Menu */}
          <div className="absolute top-4 left-4">
            <div className="relative">
              <button
                onClick={() => setShowAppsMenu(!showAppsMenu)}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors backdrop-blur"
              >
                <DevicePhoneMobileIcon className="h-5 w-5" />
                <span className="text-sm font-medium">Apps</span>
                <ChevronDownIcon className="h-4 w-4" />
              </button>
              
              {showAppsMenu && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-xl shadow-xl border border-slate-200 overflow-hidden z-20">
                  <div className="py-2">
                    <a
                      href="#"
                      className="flex items-center px-4 py-3 text-slate-700 hover:bg-slate-50 transition-colors"
                      onClick={() => setShowAppsMenu(false)}
                    >
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                      </div>
                      <div>
                        <div className="font-medium text-sm">iOS App</div>
                        <div className="text-xs text-slate-500">在 App Store 下载</div>
                      </div>
                    </a>
                    <a
                      href="#"
                      className="flex items-center px-4 py-3 text-slate-700 hover:bg-slate-50 transition-colors"
                      onClick={() => setShowAppsMenu(false)}
                    >
                      <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M17.523 15.3414c-.5511-.2484-.9878-.5894-1.3103-.9878c-.3225-.3984-.5894-.8738-.7765-1.4049c-.1871-.5311-.2806-1.1246-.2806-1.7805c0-.6559.0935-1.2494.2806-1.7805.1871-.5311.4540-1.0065.7765-1.4049.3225-.3984.7594-.7394 1.3103-.9878.5508-.2484 1.1853-.3725 1.9035-.3725.7182 0 1.3527.1241 1.9035.3725.5508.2484.9878.5894 1.3103.9878.3225.3984.5894.8738.7765 1.4049.1871.5311.2806 1.1246.2806 1.7805 0 .6559-.0935 1.2494-.2806 1.7805-.1871.5311-.4540 1.0065-.7765 1.4049-.3225.3984-.7595.7394-1.3103.9878-.5508.2484-1.1853.3725-1.9035.3725-.7182 0-1.3527-.1241-1.9035-.3725z"/>
                        </svg>
                      </div>
                      <div>
                        <div className="font-medium text-sm">Android App</div>
                        <div className="text-xs text-slate-500">在 Google Play 下载</div>
                      </div>
                    </a>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center justify-center mb-2">
            <div className="relative">
              <MusicalNoteIcon className="h-6 w-6 text-white" />
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full opacity-60 animate-pulse"></div>
            </div>
            <span className="ml-2 text-xl font-bold">iBOM</span>
          </div>
          <h2 className="text-center text-lg font-bold">
            {language === 'zh' ? '欢迎回来' : 'Welcome Back'}
          </h2>
          <p className="text-center text-blue-100 mt-1 text-sm">
            {language === 'zh' ? '登录到你的专业音乐工作站' : 'Sign in to your professional music workstation'}
          </p>
        </div>

        <div className="p-8">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}
            
            {process.env.NODE_ENV === 'development' && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'zh' ? '开发模式 - 选择用户角色' : 'Development Mode - Select User Role'}
                </label>
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value as UserRole)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {Object.entries(USER_ROLE_LABELS).map(([role, label]) => (
                    <option key={role} value={role}>
                      {label}
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-xs text-gray-500">
                  {language === 'zh' ? '开发环境下可直接选择角色登录' : 'You can directly select a role to login in development environment'}
                </p>
              </div>
            )}
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {language === 'zh' ? '邮箱地址' : 'Email Address'}
                </label>
                <input
                  type="email"
                  required={process.env.NODE_ENV !== 'development'}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder={language === 'zh' ? '输入你的邮箱' : 'Enter your email'}
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {language === 'zh' ? '密码' : 'Password'}
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    required={process.env.NODE_ENV !== 'development'}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-12"
                    placeholder={language === 'zh' ? '输入你的密码' : 'Enter your password'}
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5" />
                    ) : (
                      <EyeIcon className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-600">{language === 'zh' ? '记住我' : 'Remember me'}</span>
              </label>
              <a href="#" className="text-sm text-blue-600 hover:text-blue-500">
                {language === 'zh' ? '忘记密码？' : 'Forgot password?'}
              </a>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 px-4 rounded-lg font-medium transition-all transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  {language === 'zh' ? '登录中...' : 'Signing in...'}
                </div>
              ) : (
                process.env.NODE_ENV === 'development' 
                  ? (language === 'zh' ? `登录为${USER_ROLE_LABELS[selectedRole]}` : `Login as ${USER_ROLE_LABELS[selectedRole]}`)
                  : (language === 'zh' ? '立即登录' : 'Sign In')
              )}
            </button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white text-gray-500">{language === 'zh' ? '或者使用社交账号登录' : 'Or continue with social account'}</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => handleOAuthLogin('github')}
                className="flex items-center justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                </svg>
                GitHub
              </button>
              <button
                type="button"
                onClick={() => handleOAuthLogin('google')}
                className="flex items-center justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Google
              </button>
            </div>

            {/* Register link moved to bottom right */}
            <div className="text-right mt-4">
              <p className="text-sm text-gray-600">
                {language === 'zh' ? '还没有账户？' : "Don't have an account?"}{' '}
                <button
                  onClick={onSwitchToRegister}
                  className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
                >
                  {language === 'zh' ? '免费注册' : 'Sign up for free'}
                </button>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;