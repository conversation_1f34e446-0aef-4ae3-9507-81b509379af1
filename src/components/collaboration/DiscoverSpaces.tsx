import React, { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

const DiscoverSpaces: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const recommendedSpaces = [
    {
      id: 1,
      title: "原创词作创作交流",
      memberCount: '2.8万位成员',
      postCount: '15+篇帖子每天',
      tags: ['作词', '原创'],
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=250&fit=crop&auto=format',
      type: '更多推荐'
    },
    {
      id: 2,
      title: "流行音乐创作联盟",
      memberCount: '1.9万位成员',
      postCount: '8篇帖子',
      tags: ['作曲', '流行'],
      image: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=400&h=250&fit=crop&auto=format',
      type: '更多推荐'
    },
    {
      id: 3,
      title: "独立歌手成长空间",
      memberCount: '3.2万位成员',
      postCount: '12个',
      tags: ['歌手', '独立'],
      image: 'https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=400&h=250&fit=crop&auto=format',
      type: '更多推荐'
    },
    {
      id: 4,
      title: "器乐演奏技巧分享",
      memberCount: '1.4万位成员',
      postCount: '6篇帖子',
      tags: ['器乐', '技巧'],
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=250&fit=crop&auto=format',
      type: '更多推荐'
    },
    {
      id: 5,
      title: "电子音乐制作工作室",
      memberCount: '2.1万位成员',
      postCount: '20篇帖子',
      tags: ['电音', '制作'],
      image: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=400&h=250&fit=crop&auto=format',
      type: '更多推荐'
    },
    {
      id: 6,
      title: "古典音乐演奏交流",
      memberCount: '1.8万位成员',
      postCount: '9篇帖子',
      tags: ['古典', '演奏'],
      image: 'https://images.unsplash.com/photo-1507838153414-b4b713384a76?w=400&h=250&fit=crop&auto=format',
      type: '更多推荐'
    },
    {
      id: 7,
      title: "说唱创作联盟",
      memberCount: '3.5万位成员',
      postCount: '25篇帖子',
      tags: ['说唱', 'hip-hop'],
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=250&fit=crop&auto=format',
      type: '更多推荐'
    },
    {
      id: 8,
      title: "民谣吉他社区",
      memberCount: '2.6万位成员',
      postCount: '18篇帖子',
      tags: ['民谣', '吉他'],
      image: 'https://images.unsplash.com/photo-1520166012956-add9ba0835cb?w=400&h=250&fit=crop&auto=format',
      type: '更多推荐'
    }
  ];

  const handleJoinSpace = (spaceId: number) => {
    console.log('加入空间:', spaceId);
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* 顶部搜索 */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">发现协创空间</h1>
        <div className="relative">
          <input
            type="text"
            placeholder="搜索协创空间..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full max-w-md px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* 推荐空间网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {recommendedSpaces.map((space) => (
          <div key={space.id} className="relative bg-white rounded-lg shadow-md overflow-hidden group">
            {/* 关闭按钮 */}
            <button className="absolute top-3 right-3 z-10 bg-white bg-opacity-80 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <XMarkIcon className="w-5 h-5 text-gray-600" />
            </button>

            {/* 空间图片 */}
            <div className="relative h-48 overflow-hidden">
              <img
                src={space.image}
                alt={space.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            </div>

            {/* 空间信息 */}
            <div className="p-4">
              <h3 className="font-semibold text-lg text-gray-900 mb-2 line-clamp-2">
                {space.title}
              </h3>
              
              <div className="flex items-center text-sm text-gray-600 mb-3">
                <span>{space.memberCount}</span>
                <span className="mx-2">•</span>
                <span>{space.postCount}</span>
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                {space.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              {/* 加入按钮 */}
              <button
                onClick={() => handleJoinSpace(space.id)}
                className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                加入空间
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* 加载更多 */}
      <div className="text-center mt-8">
        <button className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
          加载更多推荐
        </button>
      </div>

      {/* 分类筛选（可选） */}
      <div className="mt-12">
        <h2 className="text-xl font-bold text-gray-900 mb-6">按分类浏览</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {[
            { name: '词', icon: '✍️', count: '68个空间', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
            { name: '曲', icon: '🎵', count: '124个空间', color: 'bg-green-50 border-green-200 hover:bg-green-100' },
            { name: '歌手', icon: '🎤', count: '96个空间', color: 'bg-purple-50 border-purple-200 hover:bg-purple-100' },
            { name: '器乐', icon: '🎸', count: '87个空间', color: 'bg-orange-50 border-orange-200 hover:bg-orange-100' },
            { name: '制作', icon: '🎧', count: '152个空间', color: 'bg-red-50 border-red-200 hover:bg-red-100' }
          ].map((category, index) => (
            <div
              key={index}
              className={`p-6 rounded-lg shadow-sm border-2 transition-all cursor-pointer ${category.color}`}
            >
              <div className="text-3xl mb-3 text-center">{category.icon}</div>
              <div className="font-semibold text-gray-900 text-lg text-center mb-1">{category.name}</div>
              <div className="text-sm text-gray-600 text-center">{category.count}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DiscoverSpaces;