import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  UserGroupIcon, 
  DocumentTextIcon, 
  FolderIcon, 
  UserIcon,
  PlusIcon,
  ShareIcon,
  ChatBubbleLeftRightIcon,
  MusicalNoteIcon,
  DocumentCheckIcon,
  ClockIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  RocketLaunchIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  HeartIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../contexts/LanguageContext';

interface Invitee {
  id: number;
  name: string;
  avatar: string;
  role: string;
  expectedShare: number;
  payCash?: number;
  status: 'pending' | 'accepted' | 'declined';
}

interface CollabSpace {
  id: number;
  name: string;
  description: string;
  coverImage: string;
  category: 'recruiting' | 'completed' | 'in-progress';
  subCategory: 'lyrics' | 'composition' | 'vocals' | 'mixing' | 'production';
  memberCount: number;
  memberAvatars: string[];
  creator: string;
  isCreator: boolean;
  hasSignedContract: boolean;
  recentActivity: string;
  createdTime: string;
  role?: string;
  invitees: Invitee[];
  progress: number;
}

interface Contract {
  id: number;
  spaceName: string;
  type: 'signed' | 'pending';
  signedDate?: string;
  parties: string[];
  status: string;
}

const CollaborationSpace: React.FC = () => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('all');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['recruiting']));
  const [selectedSpace, setSelectedSpace] = useState<CollabSpace | null>(null);
  const [spaceDetailTab, setSpaceDetailTab] = useState<'discussion' | 'activity' | 'media' | 'members'>('discussion');
  const [isPublishing, setIsPublishing] = useState(false);
  const [rightSidebarTab, setRightSidebarTab] = useState<'created' | 'joined' | 'signed-contracts' | 'pending-contracts' | 'my-contracts' | 'my-crowdfunding'>('created');

  const navigate = useNavigate();
  const { t, language } = useLanguage();

  // Filter categories with subcategories
  const categories = useMemo(() => [
    {
      key: 'recruiting',
      label: language === 'zh' ? '招募中' : 'Recruiting',
      children: [
        { key: 'lyrics', label: language === 'zh' ? '找作词' : 'Looking for Lyricist' },
        { key: 'composition', label: language === 'zh' ? '找作曲' : 'Looking for Composer' },
        { key: 'vocals', label: language === 'zh' ? '找歌手' : 'Looking for Singer' },
        { key: 'mixing', label: language === 'zh' ? '找混音' : 'Looking for Mixing' },
        { key: 'production', label: language === 'zh' ? '找制作发行' : 'Looking for Production' }
      ]
    },
    {
      key: 'in-progress',
      label: language === 'zh' ? '进行中' : 'In Progress',
      children: [
        { key: 'lyrics', label: language === 'zh' ? '作词进行中' : 'Lyrics in Progress' },
        { key: 'composition', label: language === 'zh' ? '作曲进行中' : 'Composition in Progress' },
        { key: 'vocals', label: language === 'zh' ? '录制进行中' : 'Vocals in Progress' },
        { key: 'mixing', label: language === 'zh' ? '混音进行中' : 'Mixing in Progress' },
        { key: 'production', label: language === 'zh' ? '制作发行进行中' : 'Production in Progress' }
      ]
    },
    {
      key: 'completed',
      label: language === 'zh' ? '已完成' : 'Completed',
      children: [
        { key: 'lyrics', label: language === 'zh' ? '作词已完成' : 'Lyrics Completed' },
        { key: 'composition', label: language === 'zh' ? '作曲已完成' : 'Composition Completed' },
        { key: 'vocals', label: language === 'zh' ? '录制已完成' : 'Vocals Completed' },
        { key: 'mixing', label: language === 'zh' ? '混音已完成' : 'Mixing Completed' },
        { key: 'production', label: language === 'zh' ? '制作发行已完成' : 'Production Completed' }
      ]
    }
  ], [language]);

  // Mock collaboration spaces data
  const allSpaces: CollabSpace[] = useMemo(() => [
    {
      id: 1,
      name: language === 'zh' ? '梦想起飞协创空间' : 'Dreams Take Flight Collaboration',
      description: language === 'zh' ? '寻找优秀的作词人和主唱，共同打造一首励志流行歌曲' : 'Looking for talented lyricists and vocalists to create an inspiring pop song',
      coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=300&fit=crop',
      category: 'recruiting',
      subCategory: 'lyrics',
      memberCount: 4,
      memberAvatars: [
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face'
      ],
      creator: language === 'zh' ? '王音乐制作人' : 'Wang Music Producer',
      isCreator: false,
      hasSignedContract: false,
      recentActivity: language === 'zh' ? '2小时前上传了旋律小样' : '2 hours ago uploaded melody demo',
      createdTime: "2024-01-15",
      progress: 45,
      invitees: [
        {
          id: 1,
          name: language === 'zh' ? '李作词' : 'Li Lyricist',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
          role: language === 'zh' ? '作词人' : 'Lyricist',
          expectedShare: 25,
          payCash: 2000,
          status: 'accepted'
        },
        {
          id: 2,
          name: language === 'zh' ? '张歌手' : 'Zhang Singer',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
          role: language === 'zh' ? '主唱' : 'Vocalist',
          expectedShare: 30,
          payCash: 5000,
          status: 'pending'
        },
        {
          id: 3,
          name: language === 'zh' ? '陈编曲' : 'Chen Arranger',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
          role: language === 'zh' ? '编曲师' : 'Arranger',
          expectedShare: 20,
          status: 'declined'
        }
      ]
    },
    {
      id: 2,
      name: language === 'zh' ? '电子音乐实验室' : 'Electronic Music Lab',
      description: language === 'zh' ? '探索前沿电子音乐，寻找混音师和制作人' : 'Exploring cutting-edge electronic music, looking for mixing engineers and producers',
      coverImage: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=600&h=300&fit=crop',
      category: 'in-progress',
      subCategory: 'mixing',
      memberCount: 6,
      memberAvatars: [
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face'
      ],
      creator: language === 'zh' ? 'DJ Alex' : 'DJ Alex',
      isCreator: false,
      hasSignedContract: true,
      recentActivity: language === 'zh' ? '3小时前参与了讨论' : '3 hours ago participated in discussion',
      createdTime: "2024-01-08",
      role: language === 'zh' ? '编曲师' : 'Arranger',
      progress: 75,
      invitees: [
        {
          id: 4,
          name: language === 'zh' ? '刘混音师' : 'Liu Mixing Engineer',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
          role: language === 'zh' ? '混音师' : 'Mixing Engineer',
          expectedShare: 15,
          payCash: 8000,
          status: 'accepted'
        }
      ]
    },
    {
      id: 3,
      name: language === 'zh' ? '古风音乐创作' : 'Ancient Style Music Creation',
      description: language === 'zh' ? '传统与现代融合的音乐创作项目' : 'Music creation project blending traditional and modern elements',
      coverImage: 'https://images.unsplash.com/photo-1507838153414-b4b713384a76?w=600&h=300&fit=crop',
      category: 'completed',
      subCategory: 'production',
      memberCount: 5,
      memberAvatars: [
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face'
      ],
      creator: language === 'zh' ? '古韵音乐工作室' : 'Ancient Melody Studio',
      isCreator: false,
      hasSignedContract: false,
      recentActivity: language === 'zh' ? '1天前完成了最终制作' : '1 day ago completed final production',
      createdTime: "2024-01-05",
      role: language === 'zh' ? '作词人' : 'Lyricist',
      progress: 100,
      invitees: []
    }
  ], [language]);

  // Mock data for right sidebar
  const createdSpaces = allSpaces.filter(space => space.isCreator);
  const joinedSpaces = allSpaces.filter(space => !space.isCreator);
  const contracts: Contract[] = [
    {
      id: 1,
      spaceName: language === 'zh' ? '电子音乐实验室' : 'Electronic Music Lab',
      type: 'signed',
      signedDate: "2024-01-20",
      parties: ['DJ Alex', language === 'zh' ? '王音乐制作人' : 'Wang Music Producer'],
      status: language === 'zh' ? '活跃' : 'Active'
    }
  ];

  // Filter spaces based on selected category and subcategory
  const filteredSpaces = useMemo(() => {
    return allSpaces.filter(space => {
      const matchesSearch = space.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
                           space.description.toLowerCase().includes(searchKeyword.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || space.category === selectedCategory;
      const matchesSubCategory = selectedSubCategory === 'all' || space.subCategory === selectedSubCategory;
      
      return matchesSearch && matchesCategory && matchesSubCategory;
    });
  }, [allSpaces, searchKeyword, selectedCategory, selectedSubCategory]);

  const toggleCategory = (categoryKey: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryKey)) {
      newExpanded.delete(categoryKey);
    } else {
      newExpanded.add(categoryKey);
    }
    setExpandedCategories(newExpanded);
  };

  const handleSpaceClick = (space: CollabSpace) => {
    setSelectedSpace(space);
    setSpaceDetailTab('discussion');
  };

  const renderInviteeList = (space: CollabSpace) => {
    if (!space.invitees || space.invitees.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <UserGroupIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>{language === 'zh' ? '暂无邀请人员' : 'No invitees yet'}</p>
        </div>
      );
    }

    return (
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 font-medium text-gray-900">
                {language === 'zh' ? '成员' : 'Member'}
              </th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">
                {language === 'zh' ? '角色' : 'Role'}
              </th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">
                {language === 'zh' ? '期望分成' : 'Expected Share'}
              </th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">
                {language === 'zh' ? '付现金' : 'Pay Cash'}
              </th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">
                {language === 'zh' ? '状态' : 'Status'}
              </th>
            </tr>
          </thead>
          <tbody>
            {space.invitees.map((invitee) => (
              <tr key={invitee.id} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-3">
                    <img
                      src={invitee.avatar}
                      alt={invitee.name}
                      className="w-8 h-8 rounded-full"
                    />
                    <span className="font-medium text-gray-900">{invitee.name}</span>
                  </div>
                </td>
                <td className="py-3 px-4 text-gray-600">{invitee.role}</td>
                <td className="py-3 px-4 text-gray-600">{invitee.expectedShare}%</td>
                <td className="py-3 px-4 text-gray-600">
                  {invitee.payCash ? `¥${invitee.payCash.toLocaleString()}` : '-'}
                </td>
                <td className="py-3 px-4">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    invitee.status === 'accepted' ? 'bg-green-100 text-green-800' :
                    invitee.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {invitee.status === 'accepted' ? (language === 'zh' ? '已接受' : 'Accepted') :
                     invitee.status === 'pending' ? (language === 'zh' ? '待确认' : 'Pending') :
                     (language === 'zh' ? '已拒绝' : 'Declined')}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with Create Button */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">
              {language === 'zh' ? '协创空间' : 'Collaboration Space'}
            </h1>
            <button className="flex items-center gap-2 bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
              <PlusIcon className="h-5 w-5" />
              {language === 'zh' ? '创建协创空间' : 'Create Collaboration Space'}
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-12 gap-8">
          {/* Left Sidebar - Filters */}
          <div className="col-span-3">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {language === 'zh' ? '筛选条件' : 'Filters'}
              </h3>
              
              {/* Search */}
              <div className="mb-6">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder={language === 'zh' ? '搜索协创空间...' : 'Search collaboration spaces...'}
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Categories */}
              <div className="space-y-2">
                <button
                  onClick={() => {
                    setSelectedCategory('all');
                    setSelectedSubCategory('all');
                  }}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                    selectedCategory === 'all' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {language === 'zh' ? '全部' : 'All'}
                </button>
                
                {categories.map((category) => (
                  <div key={category.key}>
                    <button
                      onClick={() => toggleCategory(category.key)}
                      className={`w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors ${
                        selectedCategory === category.key ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <span>{category.label}</span>
                      {expandedCategories.has(category.key) ? 
                        <ChevronDownIcon className="h-4 w-4" /> : 
                        <ChevronRightIcon className="h-4 w-4" />
                      }
                    </button>
                    
                    {expandedCategories.has(category.key) && (
                      <div className="ml-4 mt-1 space-y-1">
                        {category.children.map((subCategory) => (
                          <button
                            key={subCategory.key}
                            onClick={() => {
                              setSelectedCategory(category.key);
                              setSelectedSubCategory(subCategory.key);
                            }}
                            className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                              selectedCategory === category.key && selectedSubCategory === subCategory.key 
                                ? 'bg-blue-50 text-blue-700' 
                                : 'text-gray-600 hover:bg-gray-50'
                            }`}
                          >
                            {subCategory.label}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Center Content - Space List or Detail */}
          <div className="col-span-6">
            {selectedSpace ? (
              <div className="bg-white rounded-lg shadow-sm border">
                {/* Space Header */}
                <div className="relative h-48 overflow-hidden rounded-t-lg">
                  <img
                    src={selectedSpace.coverImage}
                    alt={selectedSpace.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h2 className="text-2xl font-bold mb-1">{selectedSpace.name}</h2>
                    <p className="text-sm opacity-90">{selectedSpace.description}</p>
                  </div>
                  <button
                    onClick={() => setSelectedSpace(null)}
                    className="absolute top-4 left-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70"
                  >
                    ←
                  </button>
                </div>

                {/* Space Info */}
                <div className="p-6 border-b">
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center gap-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        selectedSpace.category === 'recruiting' ? 'bg-green-100 text-green-800' :
                        selectedSpace.category === 'in-progress' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {categories.find(c => c.key === selectedSpace.category)?.label}
                      </span>
                      <div className="flex items-center gap-1">
                        <UserGroupIcon className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-600">{selectedSpace.memberCount} {language === 'zh' ? '成员' : 'members'}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-sm text-gray-600">{language === 'zh' ? '进度' : 'Progress'}: {selectedSpace.progress}%</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <button className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        <HeartIcon className="w-4 h-4" />
                        {language === 'zh' ? '关注' : 'Follow'}
                      </button>
                      <button className="flex items-center gap-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm">
                        <BookmarkIcon className="w-4 h-4" />
                        {language === 'zh' ? '收藏' : 'Save'}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Tab Navigation */}
                <div className="px-6 py-4 border-b">
                  <nav className="flex space-x-6">
                    {[
                      { key: 'discussion', label: language === 'zh' ? '讨论' : 'Discussion', icon: ChatBubbleLeftRightIcon },
                      { key: 'members', label: language === 'zh' ? '成员列表' : 'Members', icon: UserGroupIcon },
                      { key: 'activity', label: language === 'zh' ? '动态' : 'Activity', icon: ClockIcon },
                      { key: 'media', label: language === 'zh' ? '媒体' : 'Media', icon: FolderIcon }
                    ].map((tab) => (
                      <button
                        key={tab.key}
                        onClick={() => setSpaceDetailTab(tab.key as any)}
                        className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                          spaceDetailTab === tab.key
                            ? 'bg-blue-50 text-blue-700 border border-blue-200'
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                      >
                        <tab.icon className="w-4 h-4" />
                        <span className="text-sm">{tab.label}</span>
                      </button>
                    ))}
                  </nav>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                  {spaceDetailTab === 'members' && renderInviteeList(selectedSpace)}
                  {spaceDetailTab === 'discussion' && (
                    <div className="text-center py-12 text-gray-500">
                      <ChatBubbleLeftRightIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <p>{language === 'zh' ? '暂无讨论内容' : 'No discussions yet'}</p>
                    </div>
                  )}
                  {spaceDetailTab === 'activity' && (
                    <div className="text-center py-12 text-gray-500">
                      <ClockIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <p>{language === 'zh' ? '暂无活动记录' : 'No activity yet'}</p>
                    </div>
                  )}
                  {spaceDetailTab === 'media' && (
                    <div className="text-center py-12 text-gray-500">
                      <FolderIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <p>{language === 'zh' ? '暂无媒体文件' : 'No media files yet'}</p>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {filteredSpaces.map((space) => (
                  <div 
                    key={space.id} 
                    className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => handleSpaceClick(space)}
                  >
                    <div className="flex">
                      <div className="w-32 h-24 flex-shrink-0">
                        <img
                          src={space.coverImage}
                          alt={space.name}
                          className="w-full h-full object-cover rounded-l-lg"
                        />
                      </div>
                      <div className="flex-1 p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                            {space.name}
                          </h3>
                          <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              space.category === 'recruiting' ? 'bg-green-100 text-green-800' :
                              space.category === 'in-progress' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {categories.find(c => c.key === space.category)?.label}
                            </span>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                          {space.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <UserGroupIcon className="w-4 h-4" />
                              <span>{space.memberCount} {language === 'zh' ? '成员' : 'members'}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <ClockIcon className="w-4 h-4" />
                              <span>{space.recentActivity}</span>
                            </div>
                          </div>
                          <div className="flex -space-x-2">
                            {space.memberAvatars.slice(0, 3).map((avatar, index) => (
                              <img
                                key={index}
                                src={avatar}
                                alt=""
                                className="w-6 h-6 rounded-full border-2 border-white"
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Right Sidebar - My Collaboration Spaces */}
          <div className="col-span-3">
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-6 border-b">
                <h3 className="text-lg font-semibold text-gray-900">
                  {language === 'zh' ? '我的协创空间' : 'My Collaboration Spaces'}
                </h3>
              </div>
              
              <div className="p-4">
                <nav className="space-y-1">
                  <button
                    onClick={() => setRightSidebarTab('created')}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                      rightSidebarTab === 'created' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {language === 'zh' ? '我创建的空间' : 'Created Spaces'} ({createdSpaces.length})
                  </button>
                  <button
                    onClick={() => setRightSidebarTab('joined')}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                      rightSidebarTab === 'joined' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {language === 'zh' ? '我加入的空间' : 'Joined Spaces'} ({joinedSpaces.length})
                  </button>
                  <button
                    onClick={() => setRightSidebarTab('signed-contracts')}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                      rightSidebarTab === 'signed-contracts' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {language === 'zh' ? '已签署合约' : 'Signed Contracts'} ({contracts.filter(c => c.type === 'signed').length})
                  </button>
                  <button
                    onClick={() => setRightSidebarTab('pending-contracts')}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                      rightSidebarTab === 'pending-contracts' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {language === 'zh' ? '待签署合约' : 'Pending Contracts'} ({contracts.filter(c => c.type === 'pending').length})
                  </button>
                  <button
                    onClick={() => setRightSidebarTab('my-contracts')}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                      rightSidebarTab === 'my-contracts' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {language === 'zh' ? '我的版权合约' : 'My Copyright Contracts'} (2)
                  </button>
                  <button
                    onClick={() => setRightSidebarTab('my-crowdfunding')}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                      rightSidebarTab === 'my-crowdfunding' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {language === 'zh' ? '我的众筹' : 'My Crowdfunding'} (1)
                  </button>
                </nav>
              </div>
              
              <div className="p-4 border-t">
                <div className="text-center py-8 text-gray-500 text-sm">
                  {rightSidebarTab === 'created' && (language === 'zh' ? '选择左侧菜单查看详情' : 'Select left menu to view details')}
                  {rightSidebarTab === 'joined' && (language === 'zh' ? '选择左侧菜单查看详情' : 'Select left menu to view details')}
                  {rightSidebarTab === 'signed-contracts' && (language === 'zh' ? '选择左侧菜单查看详情' : 'Select left menu to view details')}
                  {rightSidebarTab === 'pending-contracts' && (language === 'zh' ? '选择左侧菜单查看详情' : 'Select left menu to view details')}
                  {rightSidebarTab === 'my-contracts' && (language === 'zh' ? '选择左侧菜单查看详情' : 'Select left menu to view details')}
                  {rightSidebarTab === 'my-crowdfunding' && (language === 'zh' ? '选择左侧菜单查看详情' : 'Select left menu to view details')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CollaborationSpace;