import React, { useState, useEffect } from 'react';
import { simpleProAudioManager, SimpleEQ, SimpleCompressor } from '../../../audio/SimpleProAudio';

interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
}

interface SimpleProAudioPanelProps {
  selectedTrack: string | null;
  tracks: Track[];
}

type PanelView = 'eq' | 'compressor' | 'reverb' | 'master' | 'analyzer';

const SimpleProAudioPanel: React.FC<SimpleProAudioPanelProps> = ({ selectedTrack, tracks }) => {
  const [activeView, setActiveView] = useState<PanelView>('eq');
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [currentEQ, setCurrentEQ] = useState<SimpleEQ | null>(null);
  const [currentCompressor, setCurrentCompressor] = useState<SimpleCompressor | null>(null);

  useEffect(() => {
    const init = async () => {
      try {
        await simpleProAudioManager.initialize();
        setIsInitialized(true);
        console.log('Simple Pro Audio initialized');
      } catch (error) {
        console.error('Failed to initialize:', error);
      }
    };
    init();
  }, []);

  useEffect(() => {
    if (selectedTrack && isInitialized) {
      const trackAudio = simpleProAudioManager.getTrack(selectedTrack);
      if (trackAudio) {
        setCurrentEQ(trackAudio.channelStrip.getEQ());
        setCurrentCompressor(trackAudio.channelStrip.getCompressor());
      } else {
        // Create track if it doesn't exist
        simpleProAudioManager.createTrack(selectedTrack, `Track ${selectedTrack}`).then(track => {
          setCurrentEQ(track.channelStrip.getEQ());
          setCurrentCompressor(track.channelStrip.getCompressor());
        });
      }
    }
  }, [selectedTrack, isInitialized]);

  const panelTabs = [
    { id: 'eq' as PanelView, label: 'EQ', icon: '🎛️' },
    { id: 'compressor' as PanelView, label: 'Comp', icon: '🔧' },
    { id: 'reverb' as PanelView, label: 'Reverb', icon: '🌊' },
    { id: 'master' as PanelView, label: 'Master', icon: '🎚️' },
    { id: 'analyzer' as PanelView, label: 'Analyzer', icon: '📊' }
  ];

  const renderEQPanel = () => {
    if (!currentEQ) return <div className="text-gray-400">No EQ available</div>;

    return (
      <div className="space-y-4">
        <h4 className="text-white font-medium">4-Band EQ</h4>
        {[0, 1, 2, 3].map(bandIndex => {
          const band = currentEQ.getBand(bandIndex);
          if (!band) return null;

          return (
            <div key={bandIndex} className="p-3 bg-gray-800 rounded border border-gray-600">
              <div className="text-sm text-gray-400 mb-2">Band {bandIndex + 1}</div>
              <div className="grid grid-cols-3 gap-3">
                <div>
                  <label className="block text-xs text-gray-400 mb-1">Freq (Hz)</label>
                  <input
                    type="number"
                    min="20"
                    max="20000"
                    value={Math.round(band.frequency)}
                    onChange={(e) => {
                      currentEQ.setBand(bandIndex, { frequency: parseInt(e.target.value) });
                    }}
                    className="w-full px-2 py-1 bg-gray-700 text-white rounded text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-400 mb-1">
                    Gain: {band.gain.toFixed(1)} dB
                  </label>
                  <input
                    type="range"
                    min="-15"
                    max="15"
                    step="0.1"
                    value={band.gain}
                    onChange={(e) => {
                      currentEQ.setBand(bandIndex, { gain: parseFloat(e.target.value) });
                    }}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-400 mb-1">
                    Q: {band.Q.toFixed(1)}
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="10"
                    step="0.1"
                    value={band.Q}
                    onChange={(e) => {
                      currentEQ.setBand(bandIndex, { Q: parseFloat(e.target.value) });
                    }}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderCompressorPanel = () => {
    if (!currentCompressor) return <div className="text-gray-400">No Compressor available</div>;

    return (
      <div className="space-y-4">
        <h4 className="text-white font-medium">Compressor</h4>
        
        <div className="p-3 bg-gray-800 rounded border border-gray-600">
          <div className="text-sm text-gray-400 mb-3">Gain Reduction</div>
          <div className="text-lg text-red-400 font-mono">
            -{currentCompressor.getGainReduction().toFixed(1)} dB
          </div>
        </div>

        <div className="space-y-3">
          <div>
            <label className="block text-xs text-gray-400 mb-1">Threshold (dB)</label>
            <input
              type="range"
              min="-60"
              max="0"
              step="0.1"
              defaultValue="-24"
              onChange={(e) => currentCompressor.setThreshold(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-xs text-gray-400 mb-1">Ratio</label>
            <input
              type="range"
              min="1"
              max="20"
              step="0.1"
              defaultValue="4"
              onChange={(e) => currentCompressor.setRatio(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs text-gray-400 mb-1">Attack (ms)</label>
              <input
                type="range"
                min="0.1"
                max="100"
                step="0.1"
                defaultValue="3"
                onChange={(e) => currentCompressor.setAttack(parseFloat(e.target.value) / 1000)}
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-400 mb-1">Release (ms)</label>
              <input
                type="range"
                min="10"
                max="1000"
                step="1"
                defaultValue="100"
                onChange={(e) => currentCompressor.setRelease(parseFloat(e.target.value) / 1000)}
                className="w-full"
              />
            </div>
          </div>

          <div>
            <label className="block text-xs text-gray-400 mb-1">Makeup Gain (dB)</label>
            <input
              type="range"
              min="-10"
              max="20"
              step="0.1"
              defaultValue="0"
              onChange={(e) => currentCompressor.setMakeupGain(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>
        </div>
      </div>
    );
  };

  const renderReverbPanel = () => {
    return (
      <div className="space-y-4">
        <h4 className="text-white font-medium">Reverb Send</h4>
        
        <div className="space-y-3">
          <div>
            <label className="block text-xs text-gray-400 mb-1">Room Size</label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              defaultValue="0.7"
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-xs text-gray-400 mb-1">Decay Time (s)</label>
            <input
              type="range"
              min="0.1"
              max="10"
              step="0.1"
              defaultValue="2"
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-xs text-gray-400 mb-1">Wetness</label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              defaultValue="0.3"
              className="w-full"
            />
          </div>
        </div>

        <div className="mt-4 flex gap-2">
          <button className="px-3 py-1 bg-blue-700 text-white rounded text-sm hover:bg-blue-600">
            Hall
          </button>
          <button className="px-3 py-1 bg-gray-700 text-white rounded text-sm hover:bg-gray-600">
            Room
          </button>
          <button className="px-3 py-1 bg-gray-700 text-white rounded text-sm hover:bg-gray-600">
            Plate
          </button>
        </div>
      </div>
    );
  };

  const renderMasterPanel = () => {
    return (
      <div className="space-y-4">
        <h4 className="text-white font-medium">Master Chain</h4>
        
        {/* LUFS监控 */}
        <div className="p-3 bg-gray-800 rounded border border-gray-600">
          <div className="text-center">
            <div className="text-xs text-gray-400">LUFS</div>
            <div className="text-2xl text-green-400 font-mono font-bold">
              -18.5
            </div>
          </div>
        </div>

        {/* 主控制 */}
        <div className="space-y-3">
          <div>
            <label className="block text-xs text-gray-400 mb-1">Input Gain (dB)</label>
            <input
              type="range"
              min="-30"
              max="30"
              step="0.1"
              defaultValue="0"
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-xs text-gray-400 mb-1">Output Gain (dB)</label>
            <input
              type="range"
              min="-30"
              max="12"
              step="0.1"
              defaultValue="0"
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-xs text-gray-400 mb-1">Limiter Threshold (dB)</label>
            <input
              type="range"
              min="-12"
              max="-0.1"
              step="0.1"
              defaultValue="-0.1"
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-xs text-gray-400 mb-1">Stereo Width</label>
            <input
              type="range"
              min="0"
              max="2"
              step="0.01"
              defaultValue="1"
              className="w-full"
            />
          </div>
        </div>

        {/* 处理模块开关 */}
        <div className="space-y-2">
          <div className="text-sm text-gray-400">Processing Chain</div>
          <div className="grid grid-cols-2 gap-2">
            <label className="flex items-center gap-2 text-sm text-gray-300">
              <input type="checkbox" defaultChecked className="rounded" />
              EQ
            </label>
            <label className="flex items-center gap-2 text-sm text-gray-300">
              <input type="checkbox" defaultChecked className="rounded" />
              Compressor
            </label>
            <label className="flex items-center gap-2 text-sm text-gray-300">
              <input type="checkbox" className="rounded" />
              Stereo Enhance
            </label>
            <label className="flex items-center gap-2 text-sm text-gray-300">
              <input type="checkbox" defaultChecked className="rounded" />
              Limiter
            </label>
          </div>
        </div>

        {/* 预设 */}
        <div className="flex gap-2">
          <button className="px-2 py-1 bg-gray-700 text-white rounded text-xs hover:bg-gray-600">
            Transparent
          </button>
          <button className="px-2 py-1 bg-orange-700 text-white rounded text-xs hover:bg-orange-600">
            Warm
          </button>
          <button className="px-2 py-1 bg-red-700 text-white rounded text-xs hover:bg-red-600">
            Loud
          </button>
        </div>
      </div>
    );
  };

  const renderAnalyzerPanel = () => {
    return (
      <div className="space-y-4">
        <h4 className="text-white font-medium">Audio Analyzer</h4>
        
        <div className="p-4 bg-gray-800 rounded border border-gray-600">
          <div className="text-center text-gray-400">
            Real-time audio analysis visualization would appear here
          </div>
          
          <div className="mt-4 grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-xs text-gray-400">CPU</div>
              <div className="text-lg text-green-400 font-mono">
                {simpleProAudioManager.getPerformanceMetrics().cpuUsage.toFixed(0)}%
              </div>
            </div>
            <div>
              <div className="text-xs text-gray-400">Latency</div>
              <div className="text-lg text-blue-400 font-mono">
                {simpleProAudioManager.getPerformanceMetrics().latency.toFixed(1)}ms
              </div>
            </div>
            <div>
              <div className="text-xs text-gray-400">Dropped</div>
              <div className="text-lg text-purple-400 font-mono">
                {simpleProAudioManager.getPerformanceMetrics().droppedFrames}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderActivePanel = () => {
    if (!isInitialized) {
      return (
        <div className="flex items-center justify-center h-64 text-gray-400">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <div>Initializing Pro Audio System...</div>
          </div>
        </div>
      );
    }

    switch (activeView) {
      case 'eq':
        return selectedTrack ? renderEQPanel() : (
          <div className="text-center text-gray-400 py-8">
            Select a track to access EQ controls
          </div>
        );

      case 'compressor':
        return selectedTrack ? renderCompressorPanel() : (
          <div className="text-center text-gray-400 py-8">
            Select a track to access Compressor controls
          </div>
        );

      case 'reverb':
        return renderReverbPanel();

      case 'master':
        return renderMasterPanel();

      case 'analyzer':
        return renderAnalyzerPanel();

      default:
        return <div className="text-center text-gray-400 py-8">Select a panel</div>;
    }
  };

  return (
    <div className="simple-pro-audio-panel bg-gray-800 rounded-lg border border-gray-600">
      {/* 面板标签导航 */}
      <div className="flex border-b border-gray-600">
        {panelTabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveView(tab.id)}
            className={`flex-1 px-2 py-2 text-xs font-medium transition-colors ${
              activeView === tab.id
                ? 'bg-blue-600 text-white border-b-2 border-blue-400'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            <div className="flex flex-col items-center gap-1">
              <span className="text-sm">{tab.icon}</span>
              <span className="text-xs">{tab.label}</span>
            </div>
          </button>
        ))}
      </div>

      {/* 当前面板内容 */}
      <div className="p-4">
        {renderActivePanel()}
      </div>

      {/* 底部状态栏 */}
      <div className="px-4 py-2 bg-gray-900 border-t border-gray-600 rounded-b-lg">
        <div className="flex items-center justify-between text-xs text-gray-400">
          <div className="flex items-center gap-4">
            <span>
              Track: {selectedTrack ? tracks.find(t => t.id === selectedTrack)?.name : 'None'}
            </span>
            <span>
              Pro Audio: {isInitialized ? '✅ Active' : '⏳ Loading'}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${isInitialized ? 'bg-green-400' : 'bg-yellow-400'}`} />
            <span>{activeView.toUpperCase()}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleProAudioPanel;