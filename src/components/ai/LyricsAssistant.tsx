import React, { useState, useRef } from 'react';
import { DocumentPlusIcon, ArrowUpTrayIcon, BookmarkIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { AIApiService, LyricsGenerateRequest } from '../../services/aiApi';

const LyricsAssistant: React.FC = () => {
  const [lyrics, setLyrics] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const [rhythmType, setRhythmType] = useState('4/4');
  const [formatPreference, setFormatPreference] = useState('standard');
  const [isGenerating, setIsGenerating] = useState(false);
  const [theme, setTheme] = useState('');
  const [style, setStyle] = useState('流行');
  const [mood, setMood] = useState('温暖');
  const [selectedProvider, setSelectedProvider] = useState<'deepseek' | 'qwen' | 'chatgpt'>('deepseek');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = (files: FileList | null) => {
    if (files && files[0]) {
      const file = files[0];
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setLyrics(content);
      };
      reader.readAsText(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const handleNewLyrics = () => {
    setLyrics('');
  };

  const handleSaveLyrics = () => {
    const blob = new Blob([lyrics], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'lyrics.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleGenerateLyrics = async () => {
    if (!theme.trim()) {
      alert('请输入歌词主题');
      return;
    }

    setIsGenerating(true);
    try {
      const request: LyricsGenerateRequest = {
        theme,
        style,
        mood,
        provider: selectedProvider
      };
      
      const response = await AIApiService.generateLyrics(request);
      
      if (response.success) {
        setLyrics(response.content);
      } else {
        alert('生成歌词失败，请重试');
      }
    } catch (error) {
      console.error('生成歌词错误:', error);
      alert('生成歌词失败，请检查网络连接');
    } finally {
      setIsGenerating(false);
    }
  };

  const rhymeRecommendations = {
    characters: ['风', '中', '空', '红', '终', '蒙', '虹', '隆'],
    words: ['微风', '心中', '天空', '朦胧', '彩虹', '感动', '相逢', '梦想']
  };

  const referenceSentences = [
    '微风轻抚过脸庞，带来春天的芬芳',
    '夜空中最亮的星，照亮我前行的路',
    '时光荏苒不复返，唯有回忆永留存',
    '青春如歌正飞扬，梦想在心中绽放'
  ];

  return (
    <div className="flex gap-6 h-full">
      {/* Main Editor Area - 3/4 width */}
      <div className="flex-1 bg-white rounded-lg shadow-sm border">
        {/* Toolbar */}
        <div className="flex items-center gap-4 p-4 border-b bg-gray-50 rounded-t-lg">
          <button
            onClick={handleNewLyrics}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <DocumentPlusIcon className="w-4 h-4" />
            新建
          </button>
          
          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            <ArrowUpTrayIcon className="w-4 h-4" />
            导入
          </button>
          
          <button
            onClick={handleSaveLyrics}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            <BookmarkIcon className="w-4 h-4" />
            保存
          </button>

          <input
            ref={fileInputRef}
            type="file"
            accept=".txt,.lrc"
            onChange={(e) => handleFileUpload(e.target.files)}
            className="hidden"
          />
        </div>

        {/* Editor Area */}
        <div
          className={`relative h-96 ${isDragging ? 'bg-blue-50 border-blue-300' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <textarea
            value={lyrics}
            onChange={(e) => setLyrics(e.target.value)}
            className="w-full h-full p-4 font-mono text-sm border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="在此输入歌词，或拖拽文件到此区域..."
            style={{
              backgroundImage: 'linear-gradient(transparent 39px, #e5e7eb 40px)',
              backgroundSize: '100% 40px',
              lineHeight: '40px',
              paddingLeft: '60px'
            }}
          />
          
          {/* Line Numbers */}
          <div className="absolute left-0 top-0 p-4 text-gray-400 text-sm font-mono pointer-events-none select-none">
            {lyrics.split('\n').map((_, index) => (
              <div key={index} style={{ height: '40px', lineHeight: '40px' }}>
                {index + 1}
              </div>
            ))}
          </div>

          {isDragging && (
            <div className="absolute inset-0 flex items-center justify-center bg-blue-50 bg-opacity-75 border-2 border-dashed border-blue-300">
              <div className="text-blue-600 text-lg font-medium">
                拖拽文件到此处导入歌词
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Sidebar - 1/4 width */}
      <div className="w-80 space-y-6">
        {/* Rhyme Recommendations */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">韵脚推荐</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">字</h4>
              <div className="flex flex-wrap gap-2">
                {rhymeRecommendations.characters.map((char, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded cursor-pointer hover:bg-blue-200 transition-colors"
                    onClick={() => setLyrics(prev => prev + char)}
                  >
                    {char}
                  </span>
                ))}
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">词</h4>
              <div className="flex flex-wrap gap-2">
                {rhymeRecommendations.words.map((word, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-green-100 text-green-800 text-sm rounded cursor-pointer hover:bg-green-200 transition-colors"
                    onClick={() => setLyrics(prev => prev + word)}
                  >
                    {word}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Reference Suggestions */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">参考句建议</h3>
          <div className="space-y-2">
            {referenceSentences.map((sentence, index) => (
              <div
                key={index}
                className="p-3 bg-gray-50 rounded-md text-sm text-gray-700 cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => setLyrics(prev => prev + (prev ? '\n' : '') + sentence)}
              >
                {sentence}
              </div>
            ))}
          </div>
        </div>

        {/* AI Generation */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">AI 歌词生成</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                主题 *
              </label>
              <input
                type="text"
                value={theme}
                onChange={(e) => setTheme(e.target.value)}
                placeholder="例如：爱情、青春、友谊"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                风格
              </label>
              <select
                value={style}
                onChange={(e) => setStyle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="流行">流行</option>
                <option value="摇滚">摇滚</option>
                <option value="民谣">民谣</option>
                <option value="说唱">说唱</option>
                <option value="古风">古风</option>
                <option value="电子">电子</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                情绪
              </label>
              <select
                value={mood}
                onChange={(e) => setMood(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="温暖">温暖</option>
                <option value="忧伤">忧伤</option>
                <option value="激昂">激昂</option>
                <option value="宁静">宁静</option>
                <option value="兴奋">兴奋</option>
                <option value="浪漫">浪漫</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                AI 模型
              </label>
              <select
                value={selectedProvider}
                onChange={(e) => setSelectedProvider(e.target.value as 'deepseek' | 'qwen' | 'chatgpt')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="deepseek">DeepSeek</option>
                <option value="qwen">通义千问</option>
                <option value="chatgpt">ChatGPT</option>
              </select>
            </div>
            
            <button
              onClick={handleGenerateLyrics}
              disabled={isGenerating || !theme.trim()}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-md hover:from-purple-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  生成中...
                </>
              ) : (
                <>
                  <SparklesIcon className="w-5 h-5" />
                  生成歌词
                </>
              )}
            </button>
          </div>
        </div>

        {/* Settings */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">设置</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                韵律类型
              </label>
              <select
                value={rhythmType}
                onChange={(e) => setRhythmType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="4/4">4/4 拍</option>
                <option value="3/4">3/4 拍</option>
                <option value="2/4">2/4 拍</option>
                <option value="6/8">6/8 拍</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                分格倾向
              </label>
              <select
                value={formatPreference}
                onChange={(e) => setFormatPreference(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="standard">标准分格</option>
                <option value="loose">宽松分格</option>
                <option value="strict">严格分格</option>
                <option value="free">自由分格</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LyricsAssistant;