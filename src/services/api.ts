import axios from 'axios';
import { User, UserRole } from '../types/user';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 扩展用户接口 - 用于API返回的用户数据
export interface ApiUser extends Omit<User, 'id'> {
  id: number;
  username: string;
  nickname?: string;
  bio?: string;
}

export interface Space {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  memberCount: number;
}

export interface Work {
  id: number | string;
  title: string;
  description: string;
  coverUrl?: string;
  playCount: number;
  collaborators: string[];
  likes?: number;
  comments?: number;
  duration?: string;
  genre?: string;
  artist?: string;
  artistAvatar?: string;
  nft?: {
    id: string;
    tokenId: string;
    price?: number;
    currency?: string;
    isForSale?: boolean;
    createdAt: string;
  };
  isFromCollaboration?: boolean;
  collaborationSpaceId?: number;
}

export interface Activity {
  id: number;
  title: string;
  description: string;
  deadline: string;
  status: string;
}

export interface HomeContent {
  bannerData: any;
  featuredSpaces: Space[];
  featuredWorks: Work[];
  recentActivities: Activity[];
}

// 直播间相关接口
export interface LiveRoom {
  id: string;
  title: string;
  description: string;
  isLive: boolean;
  streamKey: string;
  rtmpUrl: string;
  webrtcUrl: string;
  viewerCount: number;
  hostId: string;
  hostName: string;
  createdAt: string;
}

export interface CreateLiveRoomRequest {
  title: string;
  description: string;
}

// 主播申请相关接口
export interface StreamerApplicationRequest {
  realName: string;
  idNumber: string;
  phone: string;
  email: string;
  experience: string;
  selfIntroduction: string;
  profilePhoto: File;
  idCardFront: File;
  idCardBack: File;
}

export interface StreamerApplication {
  id: string;
  userId: string;
  realName: string;
  idNumber: string;
  phone: string;
  email: string;
  experience: string;
  selfIntroduction: string;
  profilePhoto?: string;
  idCardFront?: string;
  idCardBack?: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  reviewedAt?: string;
  reviewNotes?: string;
}

export class ApiService {
  // 首页相关
  static async getHomeContent(): Promise<HomeContent> {
    const response = await apiClient.get('/home');
    return response.data;
  }

  // 用户认证相关
  static async register(userData: {
    username: string;
    email: string;
    password: string;
    phone?: string;
    role?: UserRole;
  }) {
    const response = await apiClient.post('/auth/register', userData);
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    return response.data;
  }

  static async login(credentials: { email: string; password: string }) {
    const response = await apiClient.post('/auth/login', credentials);
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    return response.data;
  }

  static async logout() {
    try {
      await apiClient.post('/auth/logout');
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    }
  }

  static async getCurrentUser(): Promise<User> {
    const response = await apiClient.get('/auth/me');
    return response.data;
  }

  static async getUserProfile(userId: number): Promise<ApiUser> {
    const response = await apiClient.get(`/users/${userId}`);
    return response.data;
  }

  static async updateUserProfile(userId: number, profileData: {
    nickname?: string;
    avatar?: string;
    bio?: string;
  }) {
    const response = await apiClient.put(`/users/${userId}`, profileData);
    return response.data;
  }

  static async changePassword(oldPassword: string, newPassword: string) {
    const response = await apiClient.put('/auth/change-password', {
      oldPassword,
      newPassword
    });
    return response.data;
  }

  // 协创空间相关
  static async getSpaces(keyword?: string): Promise<Space[]> {
    const params = keyword ? { keyword } : {};
    const response = await apiClient.get('/spaces', { params });
    return response.data;
  }

  static async getSpaceDetails(spaceId: number) {
    const response = await apiClient.get(`/spaces/${spaceId}`);
    return response.data;
  }

  static async createSpace(spaceData: { name: string; description: string }) {
    const response = await apiClient.post('/spaces', spaceData);
    return response.data;
  }

  // 作品相关
  static async getWorks(keyword?: string): Promise<Work[]> {
    const params = keyword ? { keyword } : {};
    const response = await apiClient.get('/works', { params });
    return response.data;
  }

  static async getWorkDetails(workId: number) {
    const response = await apiClient.get(`/works/${workId}`);
    return response.data;
  }

  // 音乐人相关
  static async getMusicians() {
    const response = await apiClient.get('/musicians');
    return response.data;
  }

  static async getMusicianProfile(musicianId: number) {
    const response = await apiClient.get(`/musicians/${musicianId}`);
    return response.data;
  }

  static async getMusicianWorks(musicianId: number) {
    const response = await apiClient.get(`/musicians/${musicianId}/works`);
    return response.data;
  }

  // 直播相关
  static async getLiveRooms(params?: {
    page?: number;
    limit?: number;
    status?: 'live' | 'offline';
  }): Promise<{ rooms: LiveRoom[]; total: number }> {
    const response = await apiClient.get('/live/rooms', { params });
    return response.data;
  }

  static async getMyLiveRooms(): Promise<LiveRoom[]> {
    try {
      const response = await apiClient.get('/streamer/rooms');
      return response.data;
    } catch (error) {
      console.warn('API not available, using mock data for live rooms');
      // 返回mock数据
      return [
        {
          id: 'mock_room_1',
          title: '示例直播间',
          description: '这是一个示例直播间',
          isLive: false,
          streamKey: 'mock_stream_key_123',
          rtmpUrl: 'rtmp://live.example.com/live',
          webrtcUrl: 'wss://webrtc.example.com/room1',
          viewerCount: 0,
          hostId: 'mock_user_1',
          hostName: 'Mock用户',
          createdAt: new Date().toISOString()
        }
      ];
    }
  }

  static async getLiveRoomById(roomId: string): Promise<LiveRoom> {
    const response = await apiClient.get(`/live/rooms/${roomId}`);
    return response.data;
  }

  static async createLiveRoom(roomData: CreateLiveRoomRequest): Promise<LiveRoom> {
    try {
      const response = await apiClient.post('/streamer/rooms', roomData);
      return response.data;
    } catch (error) {
      console.warn('API not available, creating mock live room');
      // 返回mock创建的直播间
      return {
        id: `mock_room_${Date.now()}`,
        title: roomData.title,
        description: roomData.description,
        isLive: false,
        streamKey: `mock_key_${Math.random().toString(36).substr(2, 9)}`,
        rtmpUrl: 'rtmp://live.example.com/live',
        webrtcUrl: `wss://webrtc.example.com/room${Date.now()}`,
        viewerCount: 0,
        hostId: 'mock_user_1',
        hostName: 'Mock用户',
        createdAt: new Date().toISOString()
      };
    }
  }

  static async updateLiveRoom(roomId: string, roomData: Partial<CreateLiveRoomRequest>): Promise<LiveRoom> {
    const response = await apiClient.put(`/streamer/rooms/${roomId}`, roomData);
    return response.data;
  }

  static async deleteLiveRoom(roomId: string): Promise<void> {
    await apiClient.delete(`/streamer/rooms/${roomId}`);
  }

  static async startStream(roomId: string): Promise<void> {
    try {
      await apiClient.post(`/streamer/rooms/${roomId}/start`);
    } catch (error) {
      console.warn('API not available, simulating stream start');
      // 模拟成功，实际不做任何操作
    }
  }

  static async stopStream(roomId: string): Promise<void> {
    try {
      await apiClient.post(`/streamer/rooms/${roomId}/stop`);
    } catch (error) {
      console.warn('API not available, simulating stream stop');
      // 模拟成功，实际不做任何操作
    }
  }

  static async getStreamKey(roomId: string): Promise<{ streamKey: string; rtmpUrl: string; webrtcUrl: string }> {
    const response = await apiClient.get(`/streamer/rooms/${roomId}/stream-key`);
    return response.data;
  }

  // 社交相关
  static async getPosts() {
    const response = await apiClient.get('/social/posts');
    return response.data;
  }

  static async createPost(postData: { content: string; mediaUrls?: string[] }) {
    const response = await apiClient.post('/social/posts', postData);
    return response.data;
  }

  static async likePost(postId: number) {
    const response = await apiClient.post(`/social/posts/${postId}/like`);
    return response.data;
  }

  static async followUser(userId: number) {
    const response = await apiClient.post('/social/follow', { userId });
    return response.data;
  }

  // 消息相关
  static async getMessages() {
    const response = await apiClient.get('/social/messages');
    return response.data;
  }

  static async sendMessage(messageData: { toUserId: number; content: string }) {
    const response = await apiClient.post('/social/messages', messageData);
    return response.data;
  }

  static async getUserMessages(userId: number) {
    const response = await apiClient.get(`/users/${userId}/messages`);
    return response.data;
  }

  // 主播申请相关
  static async submitStreamerApplication(applicationData: StreamerApplicationRequest): Promise<StreamerApplication> {
    const formData = new FormData();
    
    // 添加文本字段
    Object.entries(applicationData).forEach(([key, value]) => {
      if (value instanceof File) {
        formData.append(key, value);
      } else {
        formData.append(key, String(value));
      }
    });

    const response = await apiClient.post('/streamer/application', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  static async getMyStreamerApplication(): Promise<StreamerApplication | null> {
    try {
      const response = await apiClient.get('/streamer/application');
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  }

  static async updateStreamerApplication(applicationData: Partial<StreamerApplicationRequest>): Promise<StreamerApplication> {
    const formData = new FormData();
    
    Object.entries(applicationData).forEach(([key, value]) => {
      if (value instanceof File) {
        formData.append(key, value);
      } else if (value !== undefined) {
        formData.append(key, String(value));
      }
    });

    const response = await apiClient.put('/streamer/application', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // 推流相关
  static async startRTMPStream(streamConfig: {
    rtmpUrl: string;
    quality: string;
  }): Promise<{ success: boolean }> {
    const response = await apiClient.post('/streaming/start-rtmp', streamConfig);
    return response.data;
  }

  static async stopRTMPStream(): Promise<{ success: boolean }> {
    const response = await apiClient.post('/streaming/stop-rtmp');
    return response.data;
  }

  static async getStreamingStatus(): Promise<{
    isStreaming: boolean;
    viewerCount: number;
    duration: number;
  }> {
    const response = await apiClient.get('/streaming/status');
    return response.data;
  }

  // 文件上传
  static async uploadFile(file: File, type: 'avatar' | 'work' | 'cover' | 'document'): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    const response = await apiClient.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // 搜索
  static async search(query: string, type?: 'users' | 'works' | 'rooms'): Promise<{
    users: ApiUser[];
    works: Work[];
    rooms: LiveRoom[];
  }> {
    const response = await apiClient.get('/search', { 
      params: { q: query, type } 
    });
    return response.data;
  }

  // 通知相关
  static async getNotifications(params?: {
    page?: number;
    limit?: number;
    unread?: boolean;
  }): Promise<{ notifications: any[]; total: number }> {
    const response = await apiClient.get('/notifications', { params });
    return response.data;
  }

  static async markNotificationAsRead(notificationId: string): Promise<void> {
    await apiClient.put(`/notifications/${notificationId}/read`);
  }

  static async markAllNotificationsAsRead(): Promise<void> {
    await apiClient.put('/notifications/read-all');
  }
}

export default ApiService;