import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  PlayIcon, 
  VideoCameraIcon, 
  EyeIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  ShareIcon,
  UserIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

interface LiveRoom {
  id: string;
  title: string;
  streamer: string;
  streamerAvatar: string;
  thumbnail: string;
  viewerCount: number;
  status: 'live' | 'offline';
  category: string;
  startTime?: string;
}

const LiveStream: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(false);

  const mockLiveRooms: LiveRoom[] = [
    {
      id: '1',
      title: '午夜音乐电台',
      streamer: '音乐主播小王',
      streamerAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop',
      viewerCount: 1250,
      status: 'live',
      category: '流行音乐',
      startTime: '20:30'
    },
    {
      id: '2',
      title: '民谣之夜',
      streamer: '吉他手李明',
      streamerAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      thumbnail: 'https://images.unsplash.com/photo-1520166012956-add9ba0835cb?w=400&h=300&fit=crop',
      viewerCount: 890,
      status: 'live',
      category: '民谣',
      startTime: '19:00'
    },
    {
      id: '3',
      title: '电子音乐实验室',
      streamer: 'DJ_Alex',
      streamerAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      thumbnail: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=400&h=300&fit=crop',
      viewerCount: 2100,
      status: 'live',
      category: '电子音乐',
      startTime: '21:00'
    },
    {
      id: '4',
      title: '古典音乐赏析',
      streamer: '音乐老师张华',
      streamerAvatar: 'https://images.unsplash.com/photo-1494790108755-2616c3584643?w=150&h=150&fit=crop&crop=face',
      thumbnail: 'https://images.unsplash.com/photo-1507838153414-b4b713384a76?w=400&h=300&fit=crop',
      viewerCount: 650,
      status: 'offline',
      category: '古典音乐',
      startTime: '15:30'
    },
    {
      id: '5',
      title: '翻唱好声音',
      streamer: '歌手小雨',
      streamerAvatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
      thumbnail: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?w=400&h=300&fit=crop',
      viewerCount: 1780,
      status: 'live',
      category: '翻唱',
      startTime: '18:45'
    },
    {
      id: '6',
      title: '嘻哈文化分享',
      streamer: 'Rapper孙浩',
      streamerAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      thumbnail: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=400&h=300&fit=crop',
      viewerCount: 420,
      status: 'offline',
      category: '嘻哈',
      startTime: '16:20'
    }
  ];

  const categories = [
    { value: 'all', label: '全部' },
    { value: 'live', label: '正在直播' },
    { value: '流行音乐', label: '流行音乐' },
    { value: '民谣', label: '民谣' },
    { value: '电子音乐', label: '电子音乐' },
    { value: '古典音乐', label: '古典音乐' },
    { value: '翻唱', label: '翻唱' },
    { value: '嘻哈', label: '嘻哈' }
  ];

  const filteredRooms = mockLiveRooms.filter(room => {
    const matchesSearch = room.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         room.streamer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || 
                           selectedCategory === 'live' && room.status === 'live' ||
                           selectedCategory === room.category;
    return matchesSearch && matchesCategory;
  });

  const handleRoomClick = (roomId: string) => {
    // Navigate to individual live room
    navigate(`/live/${roomId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">直播间</h1>
          <p className="text-lg text-gray-600">发现精彩的音乐直播</p>
        </div>

        <div className="flex gap-8">
          {/* Left Sidebar - Search and Categories */}
          <div className="w-80 flex-shrink-0">
            {/* Search */}
            <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索直播间或主播..."
                  className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Categories */}
            <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">类别</h3>
              <div className="space-y-2">
                {categories.map(category => (
                  <button
                    key={category.value}
                    onClick={() => setSelectedCategory(category.value)}
                    className={`w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category.value
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {category.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Settings */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <button className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <FunnelIcon className="h-5 w-5" />
                <span>过滤设置</span>
              </button>
            </div>
          </div>

          {/* Main Content - Live Rooms Grid */}
          <div className="flex-1">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRooms.map(room => (
                <div 
                  key={room.id} 
                  className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                  onClick={() => handleRoomClick(room.id)}
                >
                  {/* Thumbnail */}
                  <div className="relative h-48 bg-gradient-to-br from-purple-400 to-pink-400 overflow-hidden">
                    <img 
                      src={room.thumbnail} 
                      alt={room.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    
                    {/* Live Status */}
                    {room.status === 'live' && (
                      <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                        <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                        直播中
                      </div>
                    )}

                    {/* Viewer Count */}
                    <div className="absolute top-4 right-4 bg-black bg-opacity-60 text-white px-3 py-1 rounded-full text-sm flex items-center">
                      <EyeIcon className="h-4 w-4 mr-1" />
                      {room.viewerCount > 1000 ? `${(room.viewerCount / 1000).toFixed(1)}k` : room.viewerCount}
                    </div>

                    {/* Category Tag */}
                    <div className="absolute bottom-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {room.category}
                    </div>

                    {/* Play Button Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                      <button className="opacity-0 group-hover:opacity-100 transform scale-75 group-hover:scale-100 transition-all duration-300">
                        <div className="bg-white rounded-full p-4 shadow-lg">
                          <PlayIcon className="h-8 w-8 text-blue-600" />
                        </div>
                      </button>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    {/* Title */}
                    <h3 className="text-xl font-bold text-gray-900 mb-2 truncate">{room.title}</h3>
                    
                    {/* Streamer Info */}
                    <div className="flex items-center space-x-3 mb-4">
                      <img 
                        src={room.streamerAvatar}
                        alt={room.streamer}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <div>
                        <p className="text-gray-900 font-medium">{room.streamer}</p>
                        {room.startTime && (
                          <p className="text-sm text-gray-500">开始时间：{room.startTime}</p>
                        )}
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <EyeIcon className="h-4 w-4" />
                          <span>{room.viewerCount > 1000 ? `${(room.viewerCount / 1000).toFixed(1)}k` : room.viewerCount} 观看</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span className={`w-2 h-2 rounded-full ${room.status === 'live' ? 'bg-red-500' : 'bg-gray-400'}`}></span>
                          <span>{room.status === 'live' ? '正在直播' : '已下线'}</span>
                        </div>
                      </div>
                    </div>

                    {/* Action Button */}
                    <button 
                      className={`w-full py-3 px-4 rounded-full font-medium transition-colors ${
                        room.status === 'live' 
                          ? 'bg-red-600 text-white hover:bg-red-700' 
                          : 'bg-gray-100 text-gray-500 cursor-not-allowed'
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        if (room.status === 'live') {
                          handleRoomClick(room.id);
                        }
                      }}
                      disabled={room.status !== 'live'}
                    >
                      {room.status === 'live' ? '进入直播间' : '主播已下线'}
                    </button>
                  </div>
                </div>
              ))}
              
              {/* Empty State */}
              {filteredRooms.length === 0 && (
                <div className="col-span-full text-center py-16">
                  <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
                    <VideoCameraIcon className="h-full w-full" />
                  </div>
                  <h3 className="text-xl font-medium text-gray-900 mb-2">没有找到直播间</h3>
                  <p className="text-gray-500">尝试调整搜索条件或浏览其他分类</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveStream;