import React, { useRef, useEffect, useState, useCallback } from 'react';

interface AutomationPoint {
  id: string;
  time: number; // in seconds
  value: number; // 0-1 normalized value
  curve?: 'linear' | 'exponential' | 'logarithmic';
}

interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
}

interface AutomationLane {
  id: string;
  trackId: string;
  parameter: string; // 'volume', 'pan', 'cutoff', etc.
  points: AutomationPoint[];
  visible: boolean;
  color: string;
}

interface AutomationEditorProps {
  tracks: Track[];
  selectedTrack: string | null;
  currentTime: number;
  zoom: number;
  onUpdateAutomation?: (laneId: string, points: AutomationPoint[]) => void;
}

const AutomationEditor: React.FC<AutomationEditorProps> = ({
  tracks,
  selectedTrack,
  currentTime,
  zoom,
  onUpdateAutomation
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [automationLanes, setAutomationLanes] = useState<AutomationLane[]>([]);
  const [selectedPoint, setSelectedPoint] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [showAutomation, setShowAutomation] = useState(true);
  const [laneHeights, setLaneHeights] = useState<number[]>([]);
  const [isDraggingDivider, setIsDraggingDivider] = useState(false);
  const [draggingDividerIndex, setDraggingDividerIndex] = useState<number | null>(null);
  const [dragStartY, setDragStartY] = useState(0);
  const [initialHeights, setInitialHeights] = useState<number[]>([]);

  // Constants
  const defaultLaneHeight = 80;
  const pixelsPerSecond = 100 * zoom;
  const totalSeconds = 60;
  const svgWidth = totalSeconds * pixelsPerSecond;
  const minLaneHeight = 40;
  const maxLaneHeight = 200;
  
  // Calculate total height from individual lane heights
  const getTotalHeight = () => {
    if (laneHeights.length === 0) return defaultLaneHeight * automationLanes.length;
    return laneHeights.reduce((sum, height) => sum + height, 0);
  };
  
  const svgHeight = getTotalHeight();

  // Initialize automation lanes for selected track
  useEffect(() => {
    if (selectedTrack) {
      const track = tracks.find(t => t.id === selectedTrack);
      if (track) {
        const lanes: AutomationLane[] = [
          {
            id: `${track.id}-volume`,
            trackId: track.id,
            parameter: 'volume',
            visible: true,
            color: '#00d4ff',
            points: [
              { id: '1', time: 0, value: track.volume, curve: 'linear' },
              { id: '2', time: 10, value: 0.5, curve: 'exponential' },
              { id: '3', time: 20, value: 0.8, curve: 'linear' },
              { id: '4', time: 30, value: 0.3, curve: 'logarithmic' },
            ]
          },
          {
            id: `${track.id}-pan`,
            trackId: track.id,
            parameter: 'pan',
            visible: true,
            color: '#ff6b6b',
            points: [
              { id: '1', time: 0, value: (track.pan + 1) / 2, curve: 'linear' },
              { id: '2', time: 15, value: 0.2, curve: 'linear' },
              { id: '3', time: 25, value: 0.8, curve: 'exponential' },
            ]
          }
        ];
        setAutomationLanes(lanes);
        // Initialize lane heights if not set
        if (laneHeights.length !== lanes.length) {
          setLaneHeights(new Array(lanes.length).fill(defaultLaneHeight));
        }
      }
    } else {
      setAutomationLanes([]);
      setLaneHeights([]);
    }
  }, [selectedTrack, tracks, laneHeights.length, defaultLaneHeight]);

  // Convert time to X coordinate
  const timeToX = useCallback((time: number) => {
    return time * pixelsPerSecond;
  }, [pixelsPerSecond]);

  // Get Y offset for a lane
  const getLaneYOffset = useCallback((laneIndex: number) => {
    if (laneHeights.length === 0) return laneIndex * defaultLaneHeight;
    return laneHeights.slice(0, laneIndex).reduce((sum, height) => sum + height, 0);
  }, [laneHeights, defaultLaneHeight]);
  
  // Get height for a specific lane
  const getLaneHeight = useCallback((laneIndex: number) => {
    if (laneHeights.length === 0) return defaultLaneHeight;
    return laneHeights[laneIndex] || defaultLaneHeight;
  }, [laneHeights, defaultLaneHeight]);
  
  // Convert value to Y coordinate
  const valueToY = useCallback((value: number, laneIndex: number) => {
    const laneYOffset = getLaneYOffset(laneIndex);
    const laneHeight = getLaneHeight(laneIndex);
    return laneYOffset + (1 - value) * (laneHeight - 20) + 10;
  }, [getLaneYOffset, getLaneHeight]);

  // Convert X coordinate to time
  const xToTime = useCallback((x: number) => {
    return x / pixelsPerSecond;
  }, [pixelsPerSecond]);

  // Convert Y coordinate to value
  const yToValue = useCallback((y: number, laneIndex: number) => {
    const laneYOffset = getLaneYOffset(laneIndex);
    const laneHeight = getLaneHeight(laneIndex);
    const laneY = y - laneYOffset;
    return 1 - ((laneY - 10) / (laneHeight - 20));
  }, [getLaneYOffset, getLaneHeight]);

  // Generate curve path between two points
  const generateCurvePath = useCallback((
    p1: AutomationPoint,
    p2: AutomationPoint,
    laneIndex: number
  ): string => {
    const x1 = timeToX(p1.time);
    const y1 = valueToY(p1.value, laneIndex);
    const x2 = timeToX(p2.time);
    const y2 = valueToY(p2.value, laneIndex);

    switch (p1.curve || 'linear') {
      case 'exponential':
        const cp1x = x1 + (x2 - x1) * 0.3;
        const cp1y = y1;
        const cp2x = x2 - (x2 - x1) * 0.3;
        const cp2y = y2;
        return `M ${x1} ${y1} C ${cp1x} ${cp1y} ${cp2x} ${cp2y} ${x2} ${y2}`;
      
      case 'logarithmic':
        const cp3x = x1 + (x2 - x1) * 0.7;
        const cp3y = y1;
        const cp4x = x2 - (x2 - x1) * 0.7;
        const cp4y = y2;
        return `M ${x1} ${y1} C ${cp3x} ${cp3y} ${cp4x} ${cp4y} ${x2} ${y2}`;
      
      default: // linear
        return `M ${x1} ${y1} L ${x2} ${y2}`;
    }
  }, [timeToX, valueToY]);

  // Handle mouse down on automation point
  const handlePointMouseDown = useCallback((
    e: React.MouseEvent,
    point: AutomationPoint,
    laneIndex: number
  ) => {
    e.preventDefault();
    e.stopPropagation();
    
    setSelectedPoint(point.id);
    setIsDragging(true);
    
    const rect = svgRef.current?.getBoundingClientRect();
    if (rect) {
      const x = timeToX(point.time);
      const y = valueToY(point.value, laneIndex);
      setDragOffset({
        x: e.clientX - rect.left - x,
        y: e.clientY - rect.top - y
      });
    }
  }, [timeToX, valueToY]);

  // Handle mouse move for dragging
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !selectedPoint || !svgRef.current) return;

      const rect = svgRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left - dragOffset.x;
      const y = e.clientY - rect.top - dragOffset.y;

      // Find the lane and point
      let laneIndex = -1;
      let currentY = 0;
      for (let i = 0; i < automationLanes.length; i++) {
        const laneHeight = getLaneHeight(i);
        if (y >= currentY && y < currentY + laneHeight) {
          laneIndex = i;
          break;
        }
        currentY += laneHeight;
      }
      if (laneIndex === -1 || laneIndex >= automationLanes.length) return;

      const newTime = Math.max(0, Math.min(totalSeconds, xToTime(x)));
      const newValue = Math.max(0, Math.min(1, yToValue(y, laneIndex)));

      // Update the point
      setAutomationLanes(prevLanes => {
        const newLanes = [...prevLanes];
        const lane = newLanes[laneIndex];
        const pointIndex = lane.points.findIndex(p => p.id === selectedPoint);
        
        if (pointIndex !== -1) {
          const updatedPoints = [...lane.points];
          updatedPoints[pointIndex] = {
            ...updatedPoints[pointIndex],
            time: newTime,
            value: newValue
          };
          
          // Sort points by time
          updatedPoints.sort((a, b) => a.time - b.time);
          
          newLanes[laneIndex] = { ...lane, points: updatedPoints };
          
          // Notify parent component
          if (onUpdateAutomation) {
            onUpdateAutomation(lane.id, updatedPoints);
          }
        }
        
        return newLanes;
      });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setSelectedPoint(null);
      setDragOffset({ x: 0, y: 0 });
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, selectedPoint, dragOffset, automationLanes, laneHeights, totalSeconds, xToTime, yToValue, onUpdateAutomation]);

  // Handle double-click to add new point
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    if (!svgRef.current) return;

    const rect = svgRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Find the lane based on Y position
    let laneIndex = -1;
    let currentY = 0;
    for (let i = 0; i < automationLanes.length; i++) {
      const laneHeight = getLaneHeight(i);
      if (y >= currentY && y < currentY + laneHeight) {
        laneIndex = i;
        break;
      }
      currentY += laneHeight;
    }
    if (laneIndex === -1 || laneIndex >= automationLanes.length) return;

    const time = xToTime(x);
    const value = yToValue(y, laneIndex);

    const newPoint: AutomationPoint = {
      id: `point-${Date.now()}`,
      time: Math.max(0, Math.min(totalSeconds, time)),
      value: Math.max(0, Math.min(1, value)),
      curve: 'linear'
    };

    setAutomationLanes(prevLanes => {
      const newLanes = [...prevLanes];
      const lane = newLanes[laneIndex];
      const updatedPoints = [...lane.points, newPoint].sort((a, b) => a.time - b.time);
      
      newLanes[laneIndex] = { ...lane, points: updatedPoints };
      
      if (onUpdateAutomation) {
        onUpdateAutomation(lane.id, updatedPoints);
      }
      
      return newLanes;
    });
  }, [automationLanes, laneHeights, xToTime, yToValue, totalSeconds, onUpdateAutomation]);

  // Handle right-click to delete point
  const handleRightClick = useCallback((e: React.MouseEvent, pointId: string, laneIndex: number) => {
    e.preventDefault();
    
    setAutomationLanes(prevLanes => {
      const newLanes = [...prevLanes];
      const lane = newLanes[laneIndex];
      const updatedPoints = lane.points.filter(p => p.id !== pointId);
      
      newLanes[laneIndex] = { ...lane, points: updatedPoints };
      
      if (onUpdateAutomation) {
        onUpdateAutomation(lane.id, updatedPoints);
      }
      
      return newLanes;
    });
  }, [onUpdateAutomation]);
  
  // Handle divider drag
  const handleDividerMouseDown = useCallback((e: React.MouseEvent, dividerIndex: number) => {
    e.preventDefault();
    e.stopPropagation();
    
    console.log('Divider mousedown:', dividerIndex, 'Current heights:', laneHeights);
    
    const rect = svgRef.current?.getBoundingClientRect();
    if (rect) {
      setIsDraggingDivider(true);
      setDraggingDividerIndex(dividerIndex);
      setDragStartY(e.clientY);
      setInitialHeights([...laneHeights]);
    }
  }, [laneHeights]);
  
  // Handle divider dragging
  useEffect(() => {
    const handleDividerMouseMove = (e: MouseEvent) => {
      if (!isDraggingDivider || draggingDividerIndex === null || initialHeights.length === 0) return;
      
      const deltaY = e.clientY - dragStartY;
      const dividerIndex = draggingDividerIndex;
      
      console.log('Dragging divider:', dividerIndex, 'Delta Y:', deltaY);
      
      setLaneHeights(prevHeights => {
        const newHeights = [...initialHeights];
        
        if (dividerIndex < newHeights.length - 1) {
          // Adjust the heights of the two lanes around the divider
          const originalHeight1 = initialHeights[dividerIndex];
          const originalHeight2 = initialHeights[dividerIndex + 1];
          
          const newHeight1 = Math.max(minLaneHeight, Math.min(maxLaneHeight, originalHeight1 + deltaY));
          const newHeight2 = Math.max(minLaneHeight, Math.min(maxLaneHeight, originalHeight2 - deltaY));
          
          // Only update if both heights are within valid range
          if (newHeight1 >= minLaneHeight && newHeight2 >= minLaneHeight &&
              newHeight1 <= maxLaneHeight && newHeight2 <= maxLaneHeight) {
            newHeights[dividerIndex] = newHeight1;
            newHeights[dividerIndex + 1] = newHeight2;
          }
        }
        
        return newHeights;
      });
    };
    
    const handleDividerMouseUp = () => {
      setIsDraggingDivider(false);
      setDraggingDividerIndex(null);
      setDragStartY(0);
      setInitialHeights([]);
    };
    
    if (isDraggingDivider) {
      document.addEventListener('mousemove', handleDividerMouseMove);
      document.addEventListener('mouseup', handleDividerMouseUp);
    }
    
    return () => {
      document.removeEventListener('mousemove', handleDividerMouseMove);
      document.removeEventListener('mouseup', handleDividerMouseUp);
    };
  }, [isDraggingDivider, draggingDividerIndex, dragStartY, initialHeights, minLaneHeight, maxLaneHeight]);

  if (!showAutomation || !selectedTrack || automationLanes.length === 0) {
    return (
      <div className="automation-editor-placeholder">
        <div className="flex items-center justify-between p-3 bg-gray-800">
          <h3 className="text-sm font-bold text-blue-400">Automation</h3>
          <button
            className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
            onClick={() => setShowAutomation(true)}
          >
            Show Automation
          </button>
        </div>
        <div className="flex-1 flex items-center justify-center text-gray-500 text-sm">
          {selectedTrack ? 'Click "Show Automation" to edit automation curves' : 'Select a track to view automation'}
        </div>
      </div>
    );
  }

  return (
    <div className="automation-editor">
      <div className="automation-header">
        <div className="flex items-center justify-between p-3 bg-gray-800">
          <h3 className="text-sm font-bold text-blue-400">
            Automation - {tracks.find(t => t.id === selectedTrack)?.name}
          </h3>
          <div className="flex items-center gap-2">
            <button
              className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500"
              onClick={() => setShowAutomation(false)}
            >
              Hide
            </button>
            <div className="text-xs text-gray-400">
              Double-click to add point • Right-click to delete • Drag dividers to resize lanes
            </div>
          </div>
        </div>
      </div>

      <div className="automation-content overflow-auto">
        <svg
          ref={svgRef}
          width={svgWidth}
          height={Math.max(svgHeight, 200)}
          className="automation-svg"
          onDoubleClick={handleDoubleClick}
        >
          {/* Background grid */}
          <defs>
            <pattern id="grid" width={pixelsPerSecond} height="20" patternUnits="userSpaceOnUse">
              <path d={`M ${pixelsPerSecond} 0 L 0 0 0 20`} fill="none" stroke="#333" strokeWidth="0.5"/>
            </pattern>
          </defs>
          <rect width={svgWidth} height={svgHeight} fill="url(#grid)" />

          {/* Automation lanes */}
          {automationLanes.map((lane, laneIndex) => {
            if (!lane.visible) return null;

            const laneY = getLaneYOffset(laneIndex);
            const currentLaneHeight = getLaneHeight(laneIndex);

            return (
              <g key={lane.id}>
                {/* Lane background */}
                <rect
                  x="0"
                  y={laneY}
                  width={svgWidth}
                  height={currentLaneHeight}
                  fill={laneIndex % 2 === 0 ? '#1a1a1a' : '#1e1e1e'}
                  stroke="#333"
                  strokeWidth="1"
                />
                
                {/* Lane divider (except for last lane) */}
                {laneIndex < automationLanes.length - 1 && (
                  <rect
                    x="0"
                    y={laneY + currentLaneHeight - 4}
                    width={svgWidth}
                    height="8"
                    fill="#555"
                    className="automation-lane-divider"
                    style={{ cursor: 'ns-resize', pointerEvents: 'auto' }}
                    onMouseDown={(e) => handleDividerMouseDown(e, laneIndex)}
                  />
                )}

                {/* Lane label */}
                <text
                  x="10"
                  y={laneY + 20}
                  fill={lane.color}
                  fontSize="12"
                  fontWeight="bold"
                >
                  {lane.parameter.toUpperCase()}
                </text>

                {/* Value scale */}
                {[0, 0.25, 0.5, 0.75, 1].map(value => (
                  <g key={value}>
                    <line
                      x1="0"
                      y1={valueToY(value, laneIndex)}
                      x2={svgWidth}
                      y2={valueToY(value, laneIndex)}
                      stroke="#444"
                      strokeWidth="0.5"
                      strokeDasharray="2,2"
                    />
                    <text
                      x="5"
                      y={valueToY(value, laneIndex) - 2}
                      fill="#666"
                      fontSize="10"
                    >
                      {Math.round(value * 100)}%
                    </text>
                  </g>
                ))}

                {/* Enhanced Automation curves with realistic waveform overlay */}
                {(() => {
                  // Generate complex waveform that responds to automation values
                  const waveformPoints = [];
                  const waveformPointsTop = [];
                  const waveformPointsBottom = [];
                  const samples = Math.floor(svgWidth / 2); // One sample every 2 pixels
                  
                  for (let i = 0; i < samples; i++) {
                    const time = (i / samples) * totalSeconds;
                    const x = timeToX(time);
                    
                    // Get current automation value at this time
                    let currentAutomationValue = 0.5; // default
                    for (let j = 0; j < lane.points.length - 1; j++) {
                      const p1 = lane.points[j];
                      const p2 = lane.points[j + 1];
                      if (time >= p1.time && time <= p2.time) {
                        const progress = (time - p1.time) / (p2.time - p1.time);
                        // Simple linear interpolation (could be enhanced with curve types)
                        currentAutomationValue = p1.value + (p2.value - p1.value) * progress;
                        break;
                      }
                    }
                    
                    // Generate waveform based on parameter type and current automation value
                    let amplitude = 0;
                    let baseFreq = 1;
                    
                    if (lane.parameter === 'volume') {
                      // Volume automation: amplitude follows the automation curve
                      baseFreq = 8 + currentAutomationValue * 12;
                      amplitude = currentAutomationValue * 0.15 * 
                                 Math.sin(time * baseFreq + Math.sin(time * 2)) * 
                                 (0.7 + 0.3 * Math.sin(time * 0.5));
                      
                      // Add harmonics based on volume level
                      amplitude += currentAutomationValue * 0.05 * Math.sin(time * baseFreq * 2);
                      amplitude += currentAutomationValue * 0.025 * Math.sin(time * baseFreq * 3);
                      
                    } else if (lane.parameter === 'pan') {
                      // Pan automation: stereo field visualization
                      const panPosition = (currentAutomationValue - 0.5) * 2; // -1 to 1
                      amplitude = 0.08 * Math.sin(time * 15) * 
                                 (1 + Math.abs(panPosition) * 0.5) * 
                                 (1 + 0.3 * Math.sin(time * 0.3));
                      
                      // Add stereo width visualization
                      amplitude *= (1 + Math.abs(panPosition) * 0.3);
                      
                    } else {
                      // Generic parameter: complex modulation that responds to automation
                      const modDepth = currentAutomationValue;
                      amplitude = modDepth * 0.1 * 
                                 (Math.sin(time * (6 + modDepth * 6)) + 
                                  0.5 * Math.sin(time * (18 + modDepth * 12)) + 
                                  0.25 * Math.sin(time * (30 + modDepth * 18))) / 1.75;
                    }
                    
                    // Add subtle noise for realism
                    amplitude += (Math.random() - 0.5) * 0.01 * currentAutomationValue;
                    
                    // Calculate Y positions for top and bottom waveforms
                    const centerY = valueToY(currentAutomationValue, laneIndex);
                    const yTop = centerY - Math.abs(amplitude) * currentLaneHeight * 0.4;
                    const yBottom = centerY + Math.abs(amplitude) * currentLaneHeight * 0.4;
                    
                    waveformPoints.push(`${x},${centerY}`);
                    waveformPointsTop.push(`${x},${yTop}`);
                    waveformPointsBottom.push(`${x},${yBottom}`);
                  }
                  
                  return (
                    <g>
                      {/* Waveform envelope (top and bottom) */}
                      <polyline
                        points={waveformPointsTop.join(' ')}
                        fill="none"
                        stroke={lane.color}
                        strokeWidth="0.6"
                        opacity="0.4"
                        className="automation-waveform-top"
                      />
                      <polyline
                        points={waveformPointsBottom.join(' ')}
                        fill="none"
                        stroke={lane.color}
                        strokeWidth="0.6"
                        opacity="0.4"
                        className="automation-waveform-bottom"
                      />
                      
                      {/* Main waveform centerline */}
                      <polyline
                        points={waveformPoints.join(' ')}
                        fill="none"
                        stroke={lane.color}
                        strokeWidth="1"
                        opacity="0.2"
                        className="automation-waveform-center"
                        strokeDasharray="2,1"
                      />
                      
                      {/* Main automation curves */}
                      {lane.points.map((point, pointIndex) => {
                        const nextPoint = lane.points[pointIndex + 1];
                        if (!nextPoint) return null;

                        return (
                          <g key={`${point.id}-${nextPoint.id}`}>
                            {/* Shadow/glow effect */}
                            <path
                              d={generateCurvePath(point, nextPoint, laneIndex)}
                              fill="none"
                              stroke={lane.color}
                              strokeWidth="6"
                              opacity="0.2"
                              className="automation-curve-glow"
                            />
                            {/* Main curve */}
                            <path
                              d={generateCurvePath(point, nextPoint, laneIndex)}
                              fill="none"
                              stroke={lane.color}
                              strokeWidth="2.5"
                              className="automation-curve"
                              style={{
                                filter: 'drop-shadow(0 0 2px rgba(255,255,255,0.3))'
                              }}
                            />
                            {/* Curve highlight */}
                            <path
                              d={generateCurvePath(point, nextPoint, laneIndex)}
                              fill="none"
                              stroke="#ffffff"
                              strokeWidth="1"
                              opacity="0.4"
                              className="automation-curve-highlight"
                            />
                          </g>
                        );
                      })}
                    </g>
                  );
                })()}

                {/* Enhanced Automation points with better visual feedback */}
                {lane.points.map(point => {
                  const isSelected = selectedPoint === point.id;
                  const x = timeToX(point.time);
                  const y = valueToY(point.value, laneIndex);
                  
                  return (
                    <g key={point.id}>
                      {/* Point glow effect */}
                      <circle
                        cx={x}
                        cy={y}
                        r={isSelected ? "10" : "8"}
                        fill={lane.color}
                        opacity="0.2"
                        className="automation-point-glow"
                      />
                      {/* Point shadow */}
                      <circle
                        cx={x + 1}
                        cy={y + 1}
                        r={isSelected ? "6" : "4"}
                        fill="#000"
                        opacity="0.3"
                        className="automation-point-shadow"
                      />
                      {/* Main point */}
                      <circle
                        cx={x}
                        cy={y}
                        r={isSelected ? "6" : "4"}
                        fill={lane.color}
                        stroke="#fff"
                        strokeWidth={isSelected ? "2" : "1"}
                        className="automation-point cursor-pointer"
                        onMouseDown={(e) => handlePointMouseDown(e, point, laneIndex)}
                        onContextMenu={(e) => handleRightClick(e, point.id, laneIndex)}
                        style={{
                          filter: isSelected ? 'drop-shadow(0 0 4px rgba(255,255,255,0.8))' : 'none'
                        }}
                      />
                      {/* Point inner highlight */}
                      <circle
                        cx={x - 1}
                        cy={y - 1}
                        r={isSelected ? "2" : "1.5"}
                        fill="#ffffff"
                        opacity="0.6"
                        className="automation-point-highlight"
                        style={{ pointerEvents: 'none' }}
                      />
                      
                      {/* Value display on hover/selection */}
                      {isSelected && (
                        <g>
                          <rect
                            x={x + 8}
                            y={y - 10}
                            width="40"
                            height="16"
                            fill="#000"
                            opacity="0.8"
                            rx="2"
                          />
                          <text
                            x={x + 12}
                            y={y - 2}
                            fill="#fff"
                            fontSize="10"
                            fontFamily="monospace"
                          >
                            {Math.round(point.value * 100)}%
                          </text>
                        </g>
                      )}
                    </g>
                  );
                })}
              </g>
            );
          })}

          {/* Enhanced Playhead with glow effect */}
          <g className="playhead-group">
            {/* Playhead glow */}
            <line
              x1={timeToX(currentTime)}
              y1="0"
              x2={timeToX(currentTime)}
              y2={svgHeight}
              stroke="#ff4444"
              strokeWidth="6"
              opacity="0.3"
              className="playhead-glow"
            />
            {/* Main playhead */}
            <line
              x1={timeToX(currentTime)}
              y1="0"
              x2={timeToX(currentTime)}
              y2={svgHeight}
              stroke="#ff4444"
              strokeWidth="2"
              className="playhead"
              style={{
                filter: 'drop-shadow(0 0 3px rgba(255, 68, 68, 0.8))'
              }}
            />
            {/* Playhead triangle indicator */}
            <polygon
              points={`${timeToX(currentTime) - 6},0 ${timeToX(currentTime) + 6},0 ${timeToX(currentTime)},10`}
              fill="#ff4444"
              stroke="#fff"
              strokeWidth="1"
              className="playhead-indicator"
            />
          </g>
        </svg>
      </div>
    </div>
  );
};

export default AutomationEditor;