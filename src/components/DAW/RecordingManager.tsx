import React, { useRef, useState, useEffect, useCallback } from 'react';
import * as Tone from 'tone';

interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
  audioBuffer?: AudioBuffer;
  recordedChunks?: Blob[];
}

interface RecordingManagerProps {
  tracks: Track[];
  selectedTrack: string | null;
  isRecording: boolean;
  onUpdateTrack: (trackId: string, updates: Partial<Track>) => void;
  onRecordingStateChange: (isRecording: boolean) => void;
}

const RecordingManager: React.FC<RecordingManagerProps> = ({
  tracks,
  selectedTrack,
  isRecording,
  onUpdateTrack,
  onRecordingStateChange
}) => {
  const recorderRef = useRef<Tone.Recorder | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const micNodeRef = useRef<Tone.UserMedia | null>(null);
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>('');
  const [recordingLevel, setRecordingLevel] = useState(0);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  // Initialize audio devices
  useEffect(() => {
    const getAudioDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioInputs = devices.filter(device => device.kind === 'audioinput');
        setAudioDevices(audioInputs);
        
        if (audioInputs.length > 0 && !selectedDevice) {
          setSelectedDevice(audioInputs[0].deviceId);
        }
      } catch (error) {
        console.error('Failed to enumerate audio devices:', error);
      }
    };

    getAudioDevices();
  }, [selectedDevice]);

  // Request microphone permission
  const requestMicrophonePermission = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          deviceId: selectedDevice ? { exact: selectedDevice } : undefined,
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          sampleRate: 44100,
          sampleSize: 16
        }
      });
      
      streamRef.current = stream;
      setHasPermission(true);
      
      // Create Tone.js UserMedia node
      if (!micNodeRef.current) {
        micNodeRef.current = new Tone.UserMedia();
        await micNodeRef.current.open();
      }
      
      return true;
    } catch (error) {
      console.error('Microphone permission denied or failed:', error);
      setHasPermission(false);
      return false;
    }
  }, [selectedDevice]);

  // Initialize recording system
  const initializeRecording = useCallback(async () => {
    if (isInitialized) return true;

    try {
      // Start audio context
      if (Tone.context.state !== 'running') {
        await Tone.start();
      }

      // Request microphone permission
      const hasAccess = await requestMicrophonePermission();
      if (!hasAccess) return false;

      // Create Tone.Recorder
      recorderRef.current = new Tone.Recorder();
      
      // Connect microphone to recorder
      if (micNodeRef.current && recorderRef.current) {
        micNodeRef.current.connect(recorderRef.current);
      }

      setIsInitialized(true);
      return true;
    } catch (error) {
      console.error('Failed to initialize recording:', error);
      return false;
    }
  }, [isInitialized, requestMicrophonePermission]);

  // Start recording
  const startRecording = useCallback(async () => {
    if (!selectedTrack) {
      alert('Please select a track to record to');
      return;
    }

    const track = tracks.find(t => t.id === selectedTrack);
    if (!track || !track.armed) {
      alert('Please arm the selected track for recording');
      return;
    }

    if (!isInitialized) {
      const initialized = await initializeRecording();
      if (!initialized) return;
    }

    try {
      // Start Tone.Recorder
      if (recorderRef.current) {
        recorderRef.current.start();
      }

      // Start MediaRecorder for backup/alternative recording
      if (streamRef.current) {
        const mediaRecorder = new MediaRecorder(streamRef.current, {
          mimeType: 'audio/webm;codecs=opus'
        });
        
        const chunks: BlobPart[] = [];
        
        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            chunks.push(event.data);
          }
        };

        mediaRecorder.onstop = async () => {
          const blob = new Blob(chunks, { type: 'audio/webm;codecs=opus' });
          
          // Convert blob to audio buffer
          try {
            const arrayBuffer = await blob.arrayBuffer();
            const audioBuffer = await Tone.context.decodeAudioData(arrayBuffer);
            
            // Update track with recorded audio
            onUpdateTrack(selectedTrack, {
              audioBuffer: audioBuffer,
              recordedChunks: [...(track.recordedChunks || []), blob]
            });
          } catch (error) {
            console.error('Failed to process recorded audio:', error);
          }
        };

        mediaRecorder.start(100); // Collect data every 100ms
        mediaRecorderRef.current = mediaRecorder;
      }

      onRecordingStateChange(true);
      
      // Start recording duration timer
      const startTime = Date.now();
      const updateDuration = () => {
        if (isRecording) {
          setRecordingDuration((Date.now() - startTime) / 1000);
          requestAnimationFrame(updateDuration);
        }
      };
      updateDuration();

    } catch (error) {
      console.error('Failed to start recording:', error);
      alert('Failed to start recording. Please check your microphone permissions.');
    }
  }, [selectedTrack, tracks, isInitialized, initializeRecording, onUpdateTrack, onRecordingStateChange, isRecording]);

  // Stop recording
  const stopRecording = useCallback(async () => {
    try {
      // Stop Tone.Recorder
      if (recorderRef.current) {
        const recording = await recorderRef.current.stop();
        
        if (selectedTrack && recording) {
          // Convert recording to audio buffer and update track
          const blob = recording;
          const arrayBuffer = await blob.arrayBuffer();
          const audioBuffer = await Tone.context.decodeAudioData(arrayBuffer);
          
          const track = tracks.find(t => t.id === selectedTrack);
          onUpdateTrack(selectedTrack, {
            audioBuffer: audioBuffer,
            recordedChunks: [...(track?.recordedChunks || []), blob]
          });
        }
      }

      // Stop MediaRecorder
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }

      onRecordingStateChange(false);
      setRecordingDuration(0);

    } catch (error) {
      console.error('Failed to stop recording:', error);
    }
  }, [selectedTrack, tracks, onUpdateTrack, onRecordingStateChange]);

  // Monitor recording level
  useEffect(() => {
    if (!isRecording || !micNodeRef.current) return;

    const analyser = new Tone.Analyser('waveform', 256);
    micNodeRef.current.connect(analyser);

    const updateLevel = () => {
      if (isRecording && analyser) {
        const values = analyser.getValue() as Float32Array;
        const rms = Math.sqrt(values.reduce((sum, val) => sum + val * val, 0) / values.length);
        setRecordingLevel(rms * 100);
        requestAnimationFrame(updateLevel);
      }
    };
    updateLevel();

    return () => {
      analyser.dispose();
    };
  }, [isRecording]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      if (recorderRef.current) {
        recorderRef.current.dispose();
      }
      if (micNodeRef.current) {
        micNodeRef.current.dispose();
      }
    };
  }, []);

  // Format duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="recording-manager">
      <div className="recording-header">
        <div className="flex items-center justify-between p-3 bg-gray-800">
          <h3 className="text-sm font-bold text-blue-400">Recording</h3>
          <div className="flex items-center gap-2">
            {hasPermission === false && (
              <button
                className="px-2 py-1 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700"
                onClick={requestMicrophonePermission}
              >
                Grant Microphone Access
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="recording-controls p-3 bg-gray-700">
        {/* Device Selection */}
        <div className="mb-3">
          <label className="block text-xs text-gray-400 mb-1">Audio Input Device:</label>
          <select
            value={selectedDevice}
            onChange={(e) => setSelectedDevice(e.target.value)}
            className="w-full px-2 py-1 text-xs bg-gray-600 text-white rounded border border-gray-500"
            disabled={isRecording}
          >
            {audioDevices.map(device => (
              <option key={device.deviceId} value={device.deviceId}>
                {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
              </option>
            ))}
          </select>
        </div>

        {/* Recording Controls */}
        <div className="flex items-center gap-3 mb-3">
          <button
            className={`px-3 py-2 text-sm font-bold rounded transition-all duration-200 ${
              isRecording
                ? 'bg-red-600 text-white animate-pulse'
                : hasPermission
                ? 'bg-gray-600 text-white hover:bg-red-600'
                : 'bg-gray-500 text-gray-300 cursor-not-allowed'
            }`}
            onClick={isRecording ? stopRecording : startRecording}
            disabled={!hasPermission || (!selectedTrack || !tracks.find(t => t.id === selectedTrack)?.armed)}
          >
            {isRecording ? 'STOP REC' : 'RECORD'}
          </button>

          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-400">Duration:</span>
            <span className="text-sm font-mono text-white">
              {formatDuration(recordingDuration)}
            </span>
          </div>
        </div>

        {/* Recording Level Meter */}
        <div className="mb-3">
          <label className="block text-xs text-gray-400 mb-1">Input Level:</label>
          <div className="flex items-center gap-2">
            <div className="flex-1 h-4 bg-gray-900 rounded-sm overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 transition-all duration-100"
                style={{ width: `${Math.min(recordingLevel * 2, 100)}%` }}
              />
            </div>
            <span className="text-xs text-gray-400 w-8">
              {Math.round(recordingLevel)}%
            </span>
          </div>
        </div>

        {/* Status */}
        <div className="text-xs text-gray-400">
          Status: {
            !hasPermission ? 'No microphone access' :
            !isInitialized ? 'Not initialized' :
            isRecording ? 'Recording...' :
            selectedTrack && tracks.find(t => t.id === selectedTrack)?.armed ? 'Ready to record' :
            'Select and arm a track to record'
          }
        </div>
      </div>
    </div>
  );
};

export default RecordingManager;