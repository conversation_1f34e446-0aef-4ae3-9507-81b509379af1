import React, { useState } from 'react';
import { HeartIcon, ChatBubbleLeftIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { useLanguage } from '../../contexts/LanguageContext';

const ViewAllSpaces: React.FC = () => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('全部');

  const categories = [
    { name: t('spaces.category.all'), icon: '📁', color: 'bg-blue-100 text-blue-700' },
    { name: t('spaces.category.lyricist'), icon: '✍️', color: 'bg-blue-100 text-blue-700' },
    { name: t('spaces.category.composer'), icon: '🎵', color: 'bg-blue-100 text-blue-700' },
    { name: t('spaces.category.vocalist'), icon: '🎤', color: 'bg-blue-100 text-blue-700' },
    { name: t('spaces.category.band'), icon: '🎸', color: 'bg-blue-100 text-blue-700' },
    { name: t('spaces.category.producer'), icon: '🎧', color: 'bg-blue-100 text-blue-700' }
  ];

  const hotSpaces = [
    {
      id: 1,
      title: 'The Sarcastic Girl',
      subtitle: 'Rising Phoenix',
      memberCount: '6天',
      content: 'Only on a Monday 😭',
      likes: 56700,
      comments: 2063,
      isLiked: false,
      hasVideo: false,
      engagement: '查看翻译'
    },
    {
      id: 2,
      title: 'For all of us',
      subtitle: 'Inspiration',
      memberCount: '1天',
      content: 'Thinking about stepping down from being an adult. I\'m just not in the right headspace for this position right now. I really appreciate the opportunity though.',
      likes: 0,
      comments: 0,
      isLiked: false,
      hasVideo: true,
      engagement: '查看翻译'
    }
  ];

  const featuredSpaces = [
    {
      id: 3,
      title: '古典音乐鉴赏与创作',
      memberCount: '8.8万位成员',
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=200&fit=crop',
      description: '古典音乐爱好者的专业交流平台'
    },
    {
      id: 4,
      title: '电子音乐制作人联盟',
      memberCount: '6.2万位成员',
      postCount: '年均发表：12篇',
      image: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=300&h=200&fit=crop',
      description: '电子音乐制作技术与创意分享'
    }
  ];

  const handleJoinSpace = (spaceId: number) => {
    console.log('加入空间:', spaceId);
  };

  const handleLike = (spaceId: number) => {
    console.log('点赞:', spaceId);
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* 热门空间标题 */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('spaces.hot.title')}</h1>
        <div className="relative">
          <input
            type="text"
            placeholder={t('spaces.search.placeholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
          />
        </div>
      </div>

      <div className="flex gap-8">
        {/* 左侧分类筛选 */}
        <div className="w-48 flex-shrink-0">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('spaces.category.title')}</h3>
          <div className="space-y-2">
            {categories.map((category) => (
              <button
                key={category.name}
                onClick={() => setSelectedCategory(category.name)}
                className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                  selectedCategory === category.name
                    ? category.color
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <span className="mr-3 text-lg">{category.icon}</span>
                <span className="font-medium">{category.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="flex-1">
          {/* 热门动态 */}
          <div className="space-y-6 mb-12">
            {hotSpaces.map((space) => (
              <div key={space.id} className="bg-white border border-gray-200 rounded-lg p-6">
                {/* 帖子头部 */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <img
                      src="/api/placeholder/40/40"
                      alt={space.title}
                      className="w-10 h-10 rounded-full"
                    />
                    <div className="ml-3">
                      <div className="flex items-center">
                        <h3 className="font-semibold text-gray-900">{space.title}</h3>
                        <button className="ml-2 text-blue-600 hover:text-blue-800 text-sm">
                          {t('spaces.join')}
                        </button>
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <span>{space.subtitle}</span>
                        <span className="mx-1">·</span>
                        <span>{space.memberCount}</span>
                        <span className="mx-1">·</span>
                        <button className="text-blue-600 hover:text-blue-800">
                          <svg className="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                  <button className="text-gray-400 hover:text-gray-600">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                  </button>
                </div>

                {/* 帖子内容 */}
                <div className="mb-4">
                  <p className="text-gray-900 mb-2">{space.content}</p>
                  <button className="text-blue-600 hover:text-blue-800 text-sm">
                    {t('spaces.view.translation')}
                  </button>
                </div>

                {/* 视频内容（如果有） */}
                {space.hasVideo && (
                  <div className="mb-4 bg-black rounded-lg aspect-video flex items-center justify-center">
                    <button className="text-white text-4xl">▶</button>
                  </div>
                )}

                {/* 互动统计和按钮 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => handleLike(space.id)}
                      className="flex items-center text-gray-600 hover:text-blue-600"
                    >
                      {space.isLiked ? (
                        <HeartSolidIcon className="w-5 h-5 text-red-500 mr-1" />
                      ) : (
                        <HeartIcon className="w-5 h-5 mr-1" />
                      )}
                      <span className="text-sm">{t('spaces.like')}</span>
                    </button>
                    <button className="flex items-center text-gray-600 hover:text-blue-600">
                      <ChatBubbleLeftIcon className="w-5 h-5 mr-1" />
                      <span className="text-sm">{t('spaces.comment')}</span>
                    </button>
                  </div>
                  
                  {space.likes > 0 && (
                    <div className="flex items-center text-sm text-gray-500">
                      <div className="flex items-center mr-4">
                        <span className="mr-1">👍💚</span>
                        <span>{space.likes.toLocaleString()}</span>
                      </div>
                      {space.comments > 0 && (
                        <span>{space.comments.toLocaleString()}{t('spaces.comments.count')}</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* 推荐空间 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {featuredSpaces.map((space) => (
              <div key={space.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <div className="relative h-48">
                  <img
                    src={space.image}
                    alt={space.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                </div>
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    {space.title}
                  </h3>
                  <div className="text-sm text-gray-600 mb-3">
                    <div>{space.memberCount}</div>
                    {space.postCount && <div>{space.postCount}</div>}
                  </div>
                  <button
                    onClick={() => handleJoinSpace(space.id)}
                    className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {t('spaces.join.space')}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewAllSpaces;