import React, { useState, useMemo } from 'react';
import { 
  InboxIcon, 
  ChatBubbleLeftRightIcon, 
  AtSymbolIcon, 
  HeartIcon,
  BellIcon,
  EnvelopeIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

const MessageCenter: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const { t } = useLanguage();

  const categories = [
    { id: 'all', name: t('message.my.messages'), icon: InboxIcon, count: 12 },
    { id: 'replies', name: t('message.replies'), icon: ChatBubbleLeftRightIcon, count: 3 },
    { id: 'mentions', name: t('message.mentions'), icon: AtSymbolIcon, count: 2 },
    { id: 'likes', name: t('message.likes'), icon: HeartIcon, count: 8 },
    { id: 'system', name: t('message.system'), icon: Bell<PERSON><PERSON>, count: 1 },
    { id: 'private', name: t('message.private'), icon: EnvelopeIcon, count: 5 }
  ];

  const messages = useMemo(() => ({
    all: [
      {
        id: 1,
        type: 'like',
        user: t('message.user.music.lover'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.liked.work'),
        time: t('message.time.2min.ago'),
        read: false
      },
      {
        id: 2,
        type: 'reply',
        user: t('message.user.creator'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.replied.comment'),
        time: t('message.time.15min.ago'),
        read: false
      },
      {
        id: 3,
        type: 'mention',
        user: t('message.user.producer'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.mentioned'),
        time: t('message.time.1hour.ago'),
        read: true
      },
      {
        id: 4,
        type: 'system',
        user: t('message.user.system'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.work.approved'),
        time: t('message.time.2hours.ago'),
        read: true
      },
      {
        id: 5,
        type: 'private',
        user: t('message.user.partner'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.collaboration.discuss'),
        time: t('message.time.3hours.ago'),
        read: false
      }
    ],
    replies: [
      {
        id: 2,
        type: 'reply',
        user: t('message.user.creator'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.replied.comment'),
        time: t('message.time.15min.ago'),
        read: false
      },
      {
        id: 6,
        type: 'reply',
        user: t('message.user.student'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.replied.thanks'),
        time: t('message.time.1day.ago'),
        read: true
      }
    ],
    mentions: [
      {
        id: 3,
        type: 'mention',
        user: t('message.user.producer'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.mentioned'),
        time: t('message.time.1hour.ago'),
        read: true
      },
      {
        id: 7,
        type: 'mention',
        user: t('message.user.band.leader'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.mentioned.arrangement'),
        time: t('message.time.2days.ago'),
        read: true
      }
    ],
    likes: [
      {
        id: 1,
        type: 'like',
        user: t('message.user.music.lover'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.liked.work'),
        time: t('message.time.2min.ago'),
        read: false
      },
      {
        id: 8,
        type: 'like',
        user: t('message.user.fan'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.liked.post'),
        time: t('message.time.30min.ago'),
        read: false
      }
    ],
    system: [
      {
        id: 4,
        type: 'system',
        user: t('message.user.system'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.work.approved'),
        time: t('message.time.2hours.ago'),
        read: true
      }
    ],
    private: [
      {
        id: 5,
        type: 'private',
        user: t('message.user.partner'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.collaboration.discuss'),
        time: t('message.time.3hours.ago'),
        read: false
      },
      {
        id: 9,
        type: 'private',
        user: t('message.user.company'),
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b2e29870?w=40&h=40&fit=crop&crop=face',
        content: t('message.content.collaboration.proposal'),
        time: t('message.time.1day.ago'),
        read: false
      }
    ]
  }), [t]);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'like':
        return 'text-red-500';
      case 'reply':
        return 'text-blue-500';
      case 'mention':
        return 'text-green-500';
      case 'system':
        return 'text-gray-500';
      case 'private':
        return 'text-purple-500';
      default:
        return 'text-gray-500';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'like':
        return <HeartIcon className="h-4 w-4" />;
      case 'reply':
        return <ChatBubbleLeftRightIcon className="h-4 w-4" />;
      case 'mention':
        return <AtSymbolIcon className="h-4 w-4" />;
      case 'system':
        return <BellIcon className="h-4 w-4" />;
      case 'private':
        return <EnvelopeIcon className="h-4 w-4" />;
      default:
        return <InboxIcon className="h-4 w-4" />;
    }
  };

  const currentMessages = messages[activeCategory as keyof typeof messages] || messages.all;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="flex h-[600px]">
        {/* 左侧分类导航 */}
        <div className="w-64 border-r border-gray-200 p-4">
          <div className="mb-4">
            <h3 className="font-semibold text-gray-900 mb-2">{t('message.center')}</h3>
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder={t('message.search.placeholder')}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>
          </div>
          
          <nav className="space-y-2">
            {categories.map((category) => {
              const Icon = category.icon;
              const isActive = activeCategory === category.id;
              
              return (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-left transition-colors ${
                    isActive
                      ? 'bg-indigo-50 text-indigo-700'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <Icon className={`h-5 w-5 ${isActive ? 'text-indigo-500' : 'text-gray-400'}`} />
                    <span className="font-medium">{category.name}</span>
                  </div>
                  {category.count > 0 && (
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      isActive
                        ? 'bg-indigo-100 text-indigo-700'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {category.count}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>
        
        {/* 右侧消息列表 */}
        <div className="flex-1 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">
              {categories.find(c => c.id === activeCategory)?.name}
            </h3>
            <div className="flex items-center space-x-2">
              <button className="px-3 py-1 text-sm text-indigo-600 hover:text-indigo-700">
                {t('message.mark.all.read')}
              </button>
              <button className="px-3 py-1 text-sm text-gray-600 hover:text-gray-700">
                {t('message.clear')}
              </button>
            </div>
          </div>
          
          <div className="space-y-2 max-h-[500px] overflow-y-auto">
            {currentMessages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start space-x-3 p-3 rounded-lg transition-colors cursor-pointer ${
                  message.read
                    ? 'bg-gray-50 hover:bg-gray-100'
                    : 'bg-blue-50 hover:bg-blue-100'
                }`}
              >
                <img
                  src={message.avatar}
                  alt={message.user}
                  className="w-10 h-10 rounded-full"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">{message.user}</span>
                    <div className={`flex items-center space-x-1 ${getTypeColor(message.type)}`}>
                      {getTypeIcon(message.type)}
                    </div>
                  </div>
                  <p className="text-gray-700 text-sm mt-1">{message.content}</p>
                  <p className="text-gray-500 text-xs mt-1">{message.time}</p>
                </div>
                {!message.read && (
                  <div className="w-2 h-2 bg-indigo-500 rounded-full mt-2"></div>
                )}
              </div>
            ))}
            
            {currentMessages.length === 0 && (
              <div className="text-center py-12">
                <InboxIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">{t('message.no.messages')}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageCenter;