import React, { useRef, useState, useEffect } from 'react';
import * as Tone from 'tone';
import { Piano, KeyboardShortcuts, MidiNumbers } from 'react-piano';
import 'react-piano/dist/styles.css';

interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  instrument?: any;
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
}

interface Note {
  id: string;
  pitch: number; // MIDI note number
  start: number; // in beats
  duration: number; // in beats
  velocity: number; // 0-127
}

interface PianoRollProps {
  track: Track;
  onUpdateTrack: (trackId: string, updates: Partial<Track>) => void;
  onUpdateNotes?: (trackId: string, notes: Note[]) => void;
}

const PianoRoll: React.FC<PianoRollProps> = ({ track, onUpdateTrack, onUpdateNotes }) => {
  const [trackNotes, setTrackNotes] = useState<{ [trackId: string]: Note[] }>({});
  const [selectedNotes, setSelectedNotes] = useState<Set<string>>(new Set());
  const [zoom, setZoom] = useState(1);
  const [isDrawing, setIsDrawing] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const keysRef = useRef<HTMLDivElement>(null);
  
  // 获取当前轨道的notes
  const notes = trackNotes[track.id] || [];
  
  const noteHeight = 20;
  const keyWidth = 80;
  const keyboardHeight = 60; // 横排钢琴键盘的高度
  const beatsPerMeasure = 4;
  const pixelsPerBeat = 60 * zoom;
  const totalBeats = 32;
  const visibleOctaves = 3; // 显示的八度数量
  const startOctave = 3; // 从C3开始显示
  const whiteKeyWidth = 40; // 白键宽度
  const blackKeyWidth = 25; // 黑键宽度
  const noteWidth = whiteKeyWidth; // 每个音符的宽度基于白键
  
  // MIDI note names
  const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
  const midiNoteToName = (midiNote: number) => {
    const octave = Math.floor(midiNote / 12) - 1;
    const noteName = noteNames[midiNote % 12];
    return `${noteName}${octave}`;
  };

  const isBlackKey = (midiNote: number) => {
    const noteInOctave = midiNote % 12;
    return [1, 3, 6, 8, 10].includes(noteInOctave);
  };

  // 初始化轨道的示例notes
  useEffect(() => {
    if (!trackNotes[track.id]) {
      // 为不同轨道创建不同的示例notes
      const trackIndex = track.name.includes('MIDI Track 1') ? 0 : 
                        track.name.includes('MIDI Track 2') ? 1 : 
                        track.name.includes('MIDI Track 3') ? 2 : 0;
      
      let exampleNotes: Note[] = [];
      
      switch (trackIndex) {
        case 0: // Bass track
          exampleNotes = [
            { id: `note-${track.id}-1`, pitch: 36, start: 0, duration: 1, velocity: 100 }, // C2
            { id: `note-${track.id}-2`, pitch: 36, start: 2, duration: 1, velocity: 90 },  // C2
            { id: `note-${track.id}-3`, pitch: 41, start: 4, duration: 1, velocity: 80 },  // F2
            { id: `note-${track.id}-4`, pitch: 43, start: 6, duration: 1, velocity: 110 }, // G2
          ];
          break;
        case 1: // Lead track
          exampleNotes = [
            { id: `note-${track.id}-1`, pitch: 60, start: 0, duration: 2, velocity: 100 }, // C4
            { id: `note-${track.id}-2`, pitch: 64, start: 2, duration: 2, velocity: 90 },  // E4
            { id: `note-${track.id}-3`, pitch: 67, start: 4, duration: 2, velocity: 80 },  // G4
            { id: `note-${track.id}-4`, pitch: 72, start: 6, duration: 2, velocity: 110 }, // C5
          ];
          break;
        case 2: // Chord track
          exampleNotes = [
            { id: `note-${track.id}-1`, pitch: 48, start: 0, duration: 4, velocity: 70 }, // C3
            { id: `note-${track.id}-2`, pitch: 52, start: 0, duration: 4, velocity: 70 }, // E3
            { id: `note-${track.id}-3`, pitch: 55, start: 0, duration: 4, velocity: 70 }, // G3
            { id: `note-${track.id}-4`, pitch: 53, start: 4, duration: 4, velocity: 70 }, // F3
            { id: `note-${track.id}-5`, pitch: 57, start: 4, duration: 4, velocity: 70 }, // A3
            { id: `note-${track.id}-6`, pitch: 60, start: 4, duration: 4, velocity: 70 }, // C4
          ];
          break;
        default:
          exampleNotes = [
            { id: `note-${track.id}-1`, pitch: 60, start: 0, duration: 1, velocity: 100 },
            { id: `note-${track.id}-2`, pitch: 64, start: 1, duration: 1, velocity: 90 },
            { id: `note-${track.id}-3`, pitch: 67, start: 2, duration: 2, velocity: 80 },
            { id: `note-${track.id}-4`, pitch: 72, start: 4, duration: 1, velocity: 110 },
          ];
      }
      
      setTrackNotes(prev => ({
        ...prev,
        [track.id]: exampleNotes
      }));
    }
  }, [track.id, track.name, trackNotes]);

  const playNote = (midiNote: number) => {
    if (track.instrument && !track.muted && Tone.context.state === 'running') {
      try {
        const noteName = Tone.Frequency(midiNote, "midi").toNote();
        track.instrument.triggerAttackRelease(noteName, "8n");
      } catch (error) {
        console.error('Failed to play note:', error);
      }
    }
  };

  const addNote = (pitch: number, start: number) => {
    const newNote: Note = {
      id: `note-${track.id}-${Date.now()}`,
      pitch,
      start: Math.round(start * 4) / 4, // Quantize to 16th notes
      duration: 0.25,
      velocity: 100
    };
    const updatedNotes = [...notes, newNote];
    setTrackNotes(prev => ({
      ...prev,
      [track.id]: updatedNotes
    }));
    
    // 通知父组件notes的变化
    if (onUpdateNotes) {
      onUpdateNotes(track.id, updatedNotes);
    }
  };

  const deleteSelectedNotes = () => {
    const updatedNotes = notes.filter(note => !selectedNotes.has(note.id));
    setTrackNotes(prev => ({
      ...prev,
      [track.id]: updatedNotes
    }));
    setSelectedNotes(new Set());
    
    // 通知父组件notes的变化
    if (onUpdateNotes) {
      onUpdateNotes(track.id, updatedNotes);
    }
  };

  const handleKeyClick = (midiNote: number) => {
    playNote(midiNote);
  };

  const handleGridClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top - keyboardHeight;
    
    const beat = y / pixelsPerBeat;
    const whiteKeyIndex = Math.floor(x / whiteKeyWidth);
    
    // 将白键索引转换为MIDI音符号码
    const octave = Math.floor(whiteKeyIndex / 7);
    const keyInOctave = whiteKeyIndex % 7;
    const whiteKeyMap = [0, 2, 4, 5, 7, 9, 11]; // C, D, E, F, G, A, B
    const pitch = (startOctave + octave) * 12 + whiteKeyMap[keyInOctave];
    
    if (pitch >= 0 && pitch <= 127 && beat >= 0) {
      addNote(pitch, beat);
    }
  };

  const renderPianoKeys = () => {
    const keys = [];
    for (let midiNote = 127; midiNote >= 0; midiNote--) {
      const isBlack = isBlackKey(midiNote);
      keys.push(
        <div
          key={midiNote}
          className={`piano-key ${isBlack ? 'black' : 'white'}`}
          style={{ height: noteHeight }}
          onClick={() => handleKeyClick(midiNote)}
        >
          <span className="text-xs">{midiNoteToName(midiNote)}</span>
        </div>
      );
    }
    return keys;
  };

  const renderReactPiano = () => {
    const firstNote = MidiNumbers.fromNote('C3');
    const lastNote = MidiNumbers.fromNote('C6');
    
    const keyboardShortcuts = KeyboardShortcuts.create({
      firstNote: firstNote,
      lastNote: lastNote,
      keyboardConfig: KeyboardShortcuts.HOME_ROW,
    });

    return (
      <Piano
        noteRange={{ first: firstNote, last: lastNote }}
        playNote={(midiNumber) => {
          playNote(midiNumber);
        }}
        stopNote={(midiNumber) => {
          // Note stop handled by Tone.js
        }}
        width={visibleOctaves * whiteKeyWidth * 7}
        keyboardShortcuts={keyboardShortcuts}
        renderNoteLabel={({ keyboardShortcut, midiNumber, isActive, isAccidental }) => (
          <div className={`text-xs ${isAccidental ? 'text-white' : 'text-black'}`}>
            {midiNoteToName(midiNumber)}
          </div>
        )}
      />
    );
  };

  const renderGrid = () => {
    const gridLines = [];
    const totalWidth = visibleOctaves * whiteKeyWidth * 7; // 7 white keys per octave
    
    // Horizontal beat lines (时间轴)
    for (let beat = 0; beat <= totalBeats; beat++) {
      const y = beat * pixelsPerBeat;
      const isMeasureLine = beat % beatsPerMeasure === 0;
      gridLines.push(
        <line
          key={`hline-${beat}`}
          x1={0}
          y1={y}
          x2={totalWidth}
          y2={y}
          stroke={isMeasureLine ? '#666' : '#333'}
          strokeWidth={isMeasureLine ? 2 : 1}
        />
      );
    }
    
    // Vertical note lines (白键分隔线)
    for (let octave = 0; octave <= visibleOctaves; octave++) {
      for (let whiteKey = 0; whiteKey <= 7; whiteKey++) {
        const x = (octave * 7 + whiteKey) * whiteKeyWidth;
        gridLines.push(
          <line
            key={`vline-${octave}-${whiteKey}`}
            x1={x}
            y1={0}
            x2={x}
            y2={totalBeats * pixelsPerBeat}
            stroke='#333'
            strokeWidth={1}
          />
        );
      }
    }
    
    return gridLines;
  };

  const renderNotes = () => {
    const whiteKeyMap = [0, 2, 4, 5, 7, 9, 11]; // C, D, E, F, G, A, B
    const noteToWhiteKeyIndex = (midiNote: number) => {
      const octave = Math.floor(midiNote / 12) - startOctave;
      const noteInOctave = midiNote % 12;
      const whiteKeyIndex = whiteKeyMap.indexOf(noteInOctave);
      
      if (whiteKeyIndex === -1) {
        // 黑键，找到相邻的白键位置
        const blackKeyMap: { [key: number]: number } = {
          1: 0.5,   // C# -> between C and D
          3: 1.5,   // D# -> between D and E
          6: 3.5,   // F# -> between F and G
          8: 4.5,   // G# -> between G and A
          10: 5.5   // A# -> between A and B
        };
        return octave * 7 + (blackKeyMap[noteInOctave] || 0);
      }
      
      return octave * 7 + whiteKeyIndex;
    };
    
    return notes.map(note => {
      const octave = Math.floor(note.pitch / 12) - startOctave;
      if (octave < 0 || octave >= visibleOctaves) return null; // 不在可见范围内
      
      const whiteKeyIndex = noteToWhiteKeyIndex(note.pitch);
      const x = whiteKeyIndex * whiteKeyWidth + 2;
      const y = note.start * pixelsPerBeat;
      const width = whiteKeyWidth - 4;
      const height = note.duration * pixelsPerBeat - 2;
      const isSelected = selectedNotes.has(note.id);
      
      return (
        <rect
          key={note.id}
          x={x}
          y={y}
          width={width}
          height={Math.max(height, 10)}
          fill={isSelected ? '#ff6b6b' : '#00d4ff'}
          stroke={isSelected ? '#ff4444' : '#0099cc'}
          strokeWidth={1}
          rx={2}
          style={{ cursor: 'pointer' }}
          onClick={(e) => {
            e.stopPropagation();
            const newSelection = new Set(selectedNotes);
            if (e.ctrlKey || e.metaKey) {
              if (selectedNotes.has(note.id)) {
                newSelection.delete(note.id);
              } else {
                newSelection.add(note.id);
              }
            } else {
              newSelection.clear();
              newSelection.add(note.id);
            }
            setSelectedNotes(newSelection);
          }}
        />
      );
    }).filter(note => note !== null);
  };

  // 清除选中的notes当切换轨道时
  useEffect(() => {
    setSelectedNotes(new Set());
  }, [track.id]);
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        deleteSelectedNotes();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedNotes, notes, track.id]);

  return (
    <div className="pianoroll-container">
      <div className="pianoroll-header">
        <div className="flex items-center justify-between p-3 bg-gray-800">
          <h3 className="text-sm font-bold text-blue-400">Piano Roll - {track.name}</h3>
          <div className="flex items-center gap-2">
            <label className="text-xs text-gray-400">Zoom:</label>
            <input
              type="range"
              min="0.5"
              max="3"
              step="0.1"
              value={zoom}
              onChange={(e) => setZoom(parseFloat(e.target.value))}
              className="w-20"
            />
            <button
              className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
              onClick={deleteSelectedNotes}
              disabled={selectedNotes.size === 0}
            >
              Delete ({selectedNotes.size})
            </button>
          </div>
        </div>
      </div>
      
      <div className="flex flex-col h-80">
        {/* React Piano 键盘 */}
        <div className="pianoroll-keys-horizontal" style={{ height: keyboardHeight }}>
          {renderReactPiano()}
        </div>
        
        {/* 横排Piano Roll网格 */}
        <div 
          className="pianoroll-grid-horizontal flex-1 relative overflow-auto"
          onClick={handleGridClick}
        >
          <svg
            width={visibleOctaves * whiteKeyWidth * 7}
            height={totalBeats * pixelsPerBeat}
            className="absolute inset-0"
          >
            {renderGrid()}
            {renderNotes()}
          </svg>
        </div>
      </div>
    </div>
  );
};

export default PianoRoll;