import React, { useState } from 'react';
import { MusicalNoteIcon, PlayIcon, PauseIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

const SmartComposition: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [genre, setGenre] = useState('pop');
  const [mood, setMood] = useState('upbeat');
  const [tempo, setTempo] = useState(120);
  const [key, setKey] = useState('C');

  const handleGenerate = () => {
    setIsGenerating(true);
    // Simulate generation process
    setTimeout(() => {
      setIsGenerating(false);
    }, 3000);
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center gap-3 mb-6">
          <MusicalNoteIcon className="w-8 h-8 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">智能编曲</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Settings Panel */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                音乐风格
              </label>
              <select
                value={genre}
                onChange={(e) => setGenre(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="pop">流行</option>
                <option value="rock">摇滚</option>
                <option value="jazz">爵士</option>
                <option value="classical">古典</option>
                <option value="electronic">电子</option>
                <option value="folk">民谣</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                情绪氛围
              </label>
              <select
                value={mood}
                onChange={(e) => setMood(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="upbeat">欢快</option>
                <option value="melancholy">忧郁</option>
                <option value="romantic">浪漫</option>
                <option value="energetic">激昂</option>
                <option value="peaceful">宁静</option>
                <option value="mysterious">神秘</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                速度 (BPM): {tempo}
              </label>
              <input
                type="range"
                min="60"
                max="180"
                value={tempo}
                onChange={(e) => setTempo(Number(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                调性
              </label>
              <select
                value={key}
                onChange={(e) => setKey(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="C">C大调</option>
                <option value="G">G大调</option>
                <option value="D">D大调</option>
                <option value="A">A大调</option>
                <option value="E">E大调</option>
                <option value="Am">A小调</option>
                <option value="Em">E小调</option>
                <option value="Bm">B小调</option>
              </select>
            </div>
          </div>

          {/* Preview Area */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">预览</h3>
            
            {isGenerating ? (
              <div className="flex flex-col items-center justify-center h-40">
                <ArrowPathIcon className="w-8 h-8 text-blue-600 animate-spin mb-2" />
                <p className="text-gray-600">正在生成编曲...</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 border">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">主旋律</span>
                    <button
                      onClick={togglePlayback}
                      className="p-1 text-gray-500 hover:text-blue-600"
                    >
                      {isPlaying ? (
                        <PauseIcon className="w-5 h-5" />
                      ) : (
                        <PlayIcon className="w-5 h-5" />
                      )}
                    </button>
                  </div>
                  <div className="h-16 bg-gradient-to-r from-blue-100 to-purple-100 rounded flex items-center justify-center">
                    <div className="w-full h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-60"></div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 border">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">和声进行</span>
                  </div>
                  <div className="h-12 bg-gradient-to-r from-green-100 to-yellow-100 rounded flex items-center justify-center">
                    <div className="w-3/4 h-6 bg-gradient-to-r from-green-400 to-yellow-400 rounded-full opacity-60"></div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 border">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">节奏模式</span>
                  </div>
                  <div className="h-12 bg-gradient-to-r from-red-100 to-orange-100 rounded flex items-center justify-center">
                    <div className="w-4/5 h-6 bg-gradient-to-r from-red-400 to-orange-400 rounded-full opacity-60"></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex justify-center gap-4">
          <button
            onClick={handleGenerate}
            disabled={isGenerating}
            className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <MusicalNoteIcon className="w-5 h-5" />
            {isGenerating ? '生成中...' : '生成编曲'}
          </button>
          
          <button
            className="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <ArrowPathIcon className="w-5 h-5" />
            重新生成
          </button>
        </div>

        {/* Generated Composition Info */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">当前设置</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-blue-700 font-medium">风格:</span>
              <span className="ml-1 text-blue-800">{genre}</span>
            </div>
            <div>
              <span className="text-blue-700 font-medium">情绪:</span>
              <span className="ml-1 text-blue-800">{mood}</span>
            </div>
            <div>
              <span className="text-blue-700 font-medium">速度:</span>
              <span className="ml-1 text-blue-800">{tempo} BPM</span>
            </div>
            <div>
              <span className="text-blue-700 font-medium">调性:</span>
              <span className="ml-1 text-blue-800">{key}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmartComposition;