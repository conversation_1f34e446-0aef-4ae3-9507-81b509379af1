import React, { useState, useEffect } from 'react';
import { UsersIcon, PlusIcon, MagnifyingGlassIcon, XMarkIcon, ChevronDownIcon, ChevronRightIcon, RocketLaunchIcon } from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';
import ApiService, { Space } from '../services/api';
import { useLanguage } from '../contexts/LanguageContext';

const Collaboration: React.FC = () => {
  const navigate = useNavigate();
  const [spaces, setSpaces] = useState<Space[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [showJoinDialog, setShowJoinDialog] = useState(false);
  const [selectedSpace, setSelectedSpace] = useState<Space | null>(null);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [customShares, setCustomShares] = useState<{ [key: string]: number }>({});
  const [message, setMessage] = useState('');
  const { t, language } = useLanguage();

  // Filter states
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedSubcategory, setSelectedSubcategory] = useState('all');
  const [expandedCategories, setExpandedCategories] = useState<{ [key: string]: boolean }>({
    recruiting: true,
    inProgress: false,
    completed: false
  });

  // Right sidebar menu state
  const [selectedRightMenuItem, setSelectedRightMenuItem] = useState('my-created-spaces');

  // Filter categories and subcategories
  const filterCategories = [
    {
      key: 'recruiting',
      label: language === 'zh' ? '招募中' : 'Recruiting',
      subcategories: [
        { key: 'lyrics', label: language === 'zh' ? '找作词' : 'Looking for Lyricist' },
        { key: 'composition', label: language === 'zh' ? '找作曲' : 'Looking for Composer' },
        { key: 'vocalist', label: language === 'zh' ? '找歌手' : 'Looking for Vocalist' },
        { key: 'mixing', label: language === 'zh' ? '找混音' : 'Looking for Mixing' },
        { key: 'production', label: language === 'zh' ? '找制作发行' : 'Looking for Production' }
      ]
    },
    {
      key: 'inProgress',
      label: language === 'zh' ? '进行中' : 'In Progress',
      subcategories: [
        { key: 'lyrics', label: language === 'zh' ? '找作词' : 'Looking for Lyricist' },
        { key: 'composition', label: language === 'zh' ? '找作曲' : 'Looking for Composer' },
        { key: 'vocalist', label: language === 'zh' ? '找歌手' : 'Looking for Vocalist' },
        { key: 'mixing', label: language === 'zh' ? '找混音' : 'Looking for Mixing' },
        { key: 'production', label: language === 'zh' ? '找制作发行' : 'Looking for Production' }
      ]
    },
    {
      key: 'completed',
      label: language === 'zh' ? '已完成' : 'Completed',
      subcategories: [
        { key: 'lyrics', label: language === 'zh' ? '找作词' : 'Looking for Lyricist' },
        { key: 'composition', label: language === 'zh' ? '找作曲' : 'Looking for Composer' },
        { key: 'vocalist', label: language === 'zh' ? '找歌手' : 'Looking for Vocalist' },
        { key: 'mixing', label: language === 'zh' ? '找混音' : 'Looking for Mixing' },
        { key: 'production', label: language === 'zh' ? '找制作发行' : 'Looking for Production' }
      ]
    }
  ];

  // Right sidebar menu items
  const rightMenuItems = [
    { key: 'my-created-spaces', label: language === 'zh' ? '我创建的空间' : 'My Created Spaces' },
    { key: 'my-joined-spaces', label: language === 'zh' ? '我加入的空间' : 'My Joined Spaces' },
    { key: 'pending-applications', label: language === 'zh' ? '待处理申请' : 'Pending Applications' },
    { key: 'my-contracts', label: language === 'zh' ? '我的合约' : 'My Contracts' },
    { key: 'my-crowdfunding', label: language === 'zh' ? '我的众筹' : 'My Crowdfunding' }
  ];
  
  // Mock data for available roles and default shares
  const availableRoles = [
    { id: 'vocalist', name: language === 'en' ? 'Vocalist' : '主唱', defaultShare: 30 },
    { id: 'guitarist', name: language === 'en' ? 'Guitarist' : '吉他手', defaultShare: 25 },
    { id: 'drummer', name: language === 'en' ? 'Drummer' : '鼓手', defaultShare: 20 },
    { id: 'bassist', name: language === 'en' ? 'Bassist' : '贝斯手', defaultShare: 15 },
    { id: 'keyboardist', name: language === 'en' ? 'Keyboardist' : '键盘手', defaultShare: 10 }
  ];

  const toggleCategory = (categoryKey: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryKey]: !prev[categoryKey]
    }));
  };

  useEffect(() => {
    const fetchSpaces = async () => {
      try {
        const data = await ApiService.getSpaces(searchKeyword);
        setSpaces(data);
      } catch (error) {
        console.error('Failed to fetch spaces:', error);
        // Mock data for demonstration
        setSpaces([
          {
            id: 1,
            name: t('collab.demo.pop.title'),
            description: t('collab.demo.pop.desc'),
            createdAt: '2024-01-15',
            memberCount: 12
          },
          {
            id: 2,
            name: t('collab.demo.electronic.title'),
            description: t('collab.demo.electronic.desc'),
            createdAt: '2024-02-01',
            memberCount: 8
          },
          {
            id: 3,
            name: t('collab.demo.rock.title'),
            description: t('collab.demo.rock.desc'),
            createdAt: '2024-01-20',
            memberCount: 5
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchSpaces();
  }, [searchKeyword]);

  const handleSearch = () => {
    setLoading(true);
    // Trigger useEffect by updating dependency
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-gray-600">{t('common.loading')}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{t('collab.spaces.title')}</h1>
            <p className="text-lg text-gray-600">{t('collab.spaces.subtitle')}</p>
          </div>
        </div>
      </div>

      {/* Three Column Layout */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-6">
          {/* Left Sidebar - Filters */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border p-4 sticky top-4">
              <h3 className="font-semibold text-gray-900 mb-4">
                {language === 'zh' ? '筛选条件' : 'Filters'}
              </h3>
              
              {/* All Option */}
              <div className="mb-4">
                <button
                  onClick={() => {
                    setSelectedCategory('all');
                    setSelectedSubcategory('all');
                  }}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                    selectedCategory === 'all' 
                      ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  {language === 'zh' ? '全部' : 'All'}
                </button>
              </div>

              {/* Filter Categories */}
              <div className="space-y-2">
                {filterCategories.map((category) => (
                  <div key={category.key}>
                    <button
                      onClick={() => {
                        toggleCategory(category.key);
                        setSelectedCategory(category.key);
                        setSelectedSubcategory('all');
                      }}
                      className={`w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors ${
                        selectedCategory === category.key 
                          ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <span className="font-medium">{category.label}</span>
                      {expandedCategories[category.key] ? (
                        <ChevronDownIcon className="h-4 w-4" />
                      ) : (
                        <ChevronRightIcon className="h-4 w-4" />
                      )}
                    </button>
                    
                    {expandedCategories[category.key] && (
                      <div className="ml-4 mt-2 space-y-1">
                        {category.subcategories.map((subcategory) => (
                          <button
                            key={subcategory.key}
                            onClick={() => {
                              setSelectedCategory(category.key);
                              setSelectedSubcategory(subcategory.key);
                            }}
                            className={`w-full text-left px-3 py-2 text-sm rounded-lg transition-colors ${
                              selectedCategory === category.key && selectedSubcategory === subcategory.key
                                ? 'bg-blue-100 text-blue-800'
                                : 'text-gray-600 hover:bg-gray-50'
                            }`}
                          >
                            {subcategory.label}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Center - Main Content */}
          <div className="flex-1">
            {/* Search Bar */}
            <div className="mb-6">
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder={t('collab.search.placeholder')}
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <button
                  onClick={handleSearch}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {t('collab.search.button')}
                </button>
              </div>
            </div>

            {/* Spaces Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {spaces.map((space) => (
                <div key={space.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className="bg-blue-100 p-2 rounded-lg mr-3">
                        <UsersIcon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{space.name}</h3>
                        <p className="text-sm text-gray-500">{space.memberCount} {t('collab.members.count')}</p>
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                    {space.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {t('collab.created.at')} {new Date(space.createdAt).toLocaleDateString('zh-CN')}
                    </span>
                    <button 
                      onClick={() => {
                        setSelectedSpace(space);
                        setShowJoinDialog(true);
                        setSelectedRoles([]);
                        setCustomShares({});
                        setMessage('');
                      }}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                    >
                      {t('collab.join.collaboration')}
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {spaces.length === 0 && (
              <div className="text-center py-12">
                <UsersIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('collab.no.spaces')}</h3>
                <p className="text-gray-600 mb-6">{t('collab.no.spaces.desc')}</p>
                <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                  {t('collab.create.now')}
                </button>
              </div>
            )}
          </div>

          {/* Right Sidebar - My Spaces Menu */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border p-4 sticky top-4">
              <h3 className="font-semibold text-gray-900 mb-4">
                {language === 'zh' ? '我的协创' : 'My Collaboration'}
              </h3>
              
              <div className="space-y-2">
                {rightMenuItems.map((item) => (
                  <button
                    key={item.key}
                    onClick={() => {
                      setSelectedRightMenuItem(item.key);
                      if (item.key === 'my-created-spaces') {
                        navigate('/collaboration/my-spaces');
                      } else if (item.key === 'my-crowdfunding') {
                        navigate('/crowdfunding');
                      } else {
                        // Handle other menu items
                        console.log('Navigate to:', item.key);
                      }
                    }}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                      selectedRightMenuItem === item.key 
                        ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    {item.key === 'my-crowdfunding' && <RocketLaunchIcon className="h-4 w-4" />}
                    {item.key !== 'my-crowdfunding' && <UsersIcon className="h-4 w-4" />}
                    <span className="text-sm">{item.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Join Dialog */}
      {showJoinDialog && selectedSpace && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">
                  {language === 'en' ? 'Apply to Join' : '申请加入'}
                </h3>
                <button 
                  onClick={() => setShowJoinDialog(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
              
              {/* Space Info */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-2">{selectedSpace.name}</h4>
                <p className="text-sm text-gray-600">{selectedSpace.description}</p>
              </div>
              
              {/* Available Roles */}
              <div className="mb-6">
                <h5 className="font-medium text-gray-900 mb-3">
                  {language === 'en' ? 'Available Roles' : '空缺角色'}
                </h5>
                <div className="space-y-3">
                  {availableRoles.map((role) => (
                    <div key={role.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={role.id}
                          checked={selectedRoles.includes(role.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedRoles([...selectedRoles, role.id]);
                              setCustomShares({ ...customShares, [role.id]: role.defaultShare });
                            } else {
                              setSelectedRoles(selectedRoles.filter(r => r !== role.id));
                              const newShares = { ...customShares };
                              delete newShares[role.id];
                              setCustomShares(newShares);
                            }
                          }}
                          className="mr-3 h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
                        />
                        <label htmlFor={role.id} className="font-medium text-gray-700">
                          {role.name}
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={customShares[role.id] || role.defaultShare}
                          onChange={(e) => setCustomShares({ ...customShares, [role.id]: parseInt(e.target.value) || 0 })}
                          disabled={!selectedRoles.includes(role.id)}
                          className="w-16 px-2 py-1 text-sm border rounded focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                        />
                        <span className="text-sm text-gray-500">%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Message Area */}
              <div className="mb-6">
                <label className="block font-medium text-gray-900 mb-2">
                  {language === 'en' ? 'Message (Optional)' : '留言（可选）'}
                </label>
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder={language === 'en' ? 'Tell them why you want to join...' : '告诉他们你为什么想加入...'}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>
              
              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowJoinDialog(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {language === 'en' ? 'Cancel' : '取消'}
                </button>
                <button
                  onClick={() => {
                    // Handle join application
                    console.log('Apply to join:', {
                      space: selectedSpace,
                      roles: selectedRoles,
                      shares: customShares,
                      message
                    });
                    setShowJoinDialog(false);
                  }}
                  disabled={selectedRoles.length === 0}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  {language === 'en' ? 'Submit Application' : '提交申请'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Collaboration;