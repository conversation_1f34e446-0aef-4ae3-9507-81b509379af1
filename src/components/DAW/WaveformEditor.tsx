import React, { useRef, useEffect, useState } from 'react';
import WaveSurfer from 'wavesurfer.js';
import * as MidiFile from 'midi-file';

interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
  audioBuffer?: AudioBuffer;
  audioFile?: File;
}

interface WaveformEditorProps {
  track: Track;
  isPlaying: boolean;
  currentTime: number;
  onUpdateTrack: (trackId: string, updates: Partial<Track>) => void;
  onCurrentTimeUpdate?: (time: number) => void;
}

const WaveformEditor: React.FC<WaveformEditorProps> = ({
  track,
  isPlaying,
  currentTime,
  onUpdateTrack,
  onCurrentTimeUpdate
}) => {
  const waveformRef = useRef<HTMLDivElement>(null);
  const wavesurferRef = useRef<WaveSurfer | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [duration, setDuration] = useState(0);
  const [zoom, setZoom] = useState(1);
  const [isAudioLoaded, setIsAudioLoaded] = useState(false);
  const [fileType, setFileType] = useState<'audio' | 'midi'>('audio');
  const [isMidiPlaying, setIsMidiPlaying] = useState(false);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [midiInstrument, setMidiInstrument] = useState<any>(null);
  const [parsedMidiData, setParsedMidiData] = useState<Array<{note: string, time: number, duration: number, velocity: number}> | null>(null);
  const [midiStartTime, setMidiStartTime] = useState(0);
  const midiPlayingRef = useRef(false);

  // Initialize WaveSurfer
  useEffect(() => {
    if (waveformRef.current && !wavesurferRef.current) {
      wavesurferRef.current = WaveSurfer.create({
        container: waveformRef.current,
        waveColor: '#00d4ff',
        progressColor: '#0099cc',
        cursorColor: '#ff4444',
        barWidth: 1,
        barGap: 0,
        height: 120,
        normalize: true,
        fillParent: true,
        interact: true,
        minPxPerSec: 50
      });

      // Event listeners
      wavesurferRef.current.on('ready', () => {
        setIsLoading(false);
        setIsAudioLoaded(true);
        setDuration(wavesurferRef.current?.getDuration() || 0);
      });

      wavesurferRef.current.on('loading', (percent) => {
        setIsLoading(true);
        setIsAudioLoaded(false);
      });

      wavesurferRef.current.on('play', () => {
        setIsAudioPlaying(true);
      });

      wavesurferRef.current.on('pause', () => {
        setIsAudioPlaying(false);
      });

      wavesurferRef.current.on('finish', () => {
        setIsAudioPlaying(false);
      });

      // Load recorded audio or generate a demo waveform
      if (track.audioBuffer) {
        loadAudioBuffer(track.audioBuffer);
      } else {
        generateDemoWaveform();
      }
    }

    return () => {
      if (wavesurferRef.current) {
        wavesurferRef.current.destroy();
        wavesurferRef.current = null;
      }
      
      // 清理MIDI播放器
      if (midiInstrument && !midiInstrument.disposed) {
        midiInstrument.dispose();
      }
    };
  }, []);

  // Update zoom
  useEffect(() => {
    if (wavesurferRef.current && isAudioLoaded) {
      try {
        wavesurferRef.current.zoom(50 * zoom);
      } catch (error) {
        // Silently handle the error when no audio is loaded
        console.debug('Zoom called before audio loaded, skipping:', error instanceof Error ? error.message : String(error));
      }
    }
  }, [zoom, isAudioLoaded]);

  // Sync playback position
  useEffect(() => {
    console.log(`🔍 同步检查: currentTime=${currentTime.toFixed(2)}s, duration=${duration.toFixed(2)}s, fileType=${fileType}, isPlaying=${isPlaying}, isMidiPlaying=${isMidiPlaying}`);
    
    if (wavesurferRef.current && duration > 0 && isFinite(currentTime)) {
      // 对于MIDI播放，总是同步位置（无论顶部播放状态如何）
      // 对于音频播放，只在不播放时同步（避免冲突）
      const shouldSync = fileType === 'midi' || (fileType === 'audio' && !isPlaying);
      
      console.log(`🔍 shouldSync=${shouldSync}, 条件: fileType=${fileType}, isPlaying=${isPlaying}`);
      
      if (shouldSync) {
        const seekPosition = currentTime / duration;
        if (isFinite(seekPosition) && seekPosition >= 0 && seekPosition <= 1) {
          console.log(`🔄 同步WaveSurfer位置: ${(seekPosition * 100).toFixed(1)}%, currentTime=${currentTime.toFixed(2)}s, duration=${duration.toFixed(2)}s`);
          wavesurferRef.current.seekTo(seekPosition);
        } else {
          console.warn(`⚠️ seekPosition异常: ${seekPosition}, currentTime=${currentTime}, duration=${duration}`);
        }
      } else {
        console.log(`❌ 跳过同步: shouldSync=${shouldSync}`);
      }
    } else {
      console.log(`❌ 同步条件不满足: wavesurfer=${!!wavesurferRef.current}, duration=${duration}, currentTime=${currentTime}`);
    }
  }, [currentTime, duration, isPlaying, fileType, isMidiPlaying]);

  const loadAudioBuffer = (audioBuffer: AudioBuffer) => {
    if (!wavesurferRef.current) return;
    
    try {
      const blob = bufferToBlob(audioBuffer);
      wavesurferRef.current.loadBlob(blob);
    } catch (error) {
      console.error('Failed to load audio buffer:', error);
      generateDemoWaveform();
    }
  };

  const generateDemoWaveform = () => {
    if (!wavesurferRef.current) return;

    // Generate a realistic demo waveform for visualization
    const length = 44100 * 8; // 8 seconds at 44.1kHz
    const buffer = new AudioBuffer({
      numberOfChannels: 1,
      length: length,
      sampleRate: 44100
    });

    const channelData = buffer.getChannelData(0);
    
    // Generate a more complex, realistic waveform with multiple frequencies and noise
    for (let i = 0; i < length; i++) {
      const t = i / 44100;
      
      // Base melody with varying frequency
      const baseFreq = 220 + Math.sin(t * 0.3) * 50;
      const baseTone = 0.4 * Math.sin(2 * Math.PI * baseFreq * t);
      
      // Harmonic overtones
      const harmonic1 = 0.2 * Math.sin(2 * Math.PI * baseFreq * 2 * t);
      const harmonic2 = 0.1 * Math.sin(2 * Math.PI * baseFreq * 3 * t);
      
      // Add rhythmic elements (kick-like pattern)
      const kickPattern = Math.sin(t * 4) > 0.7 ? 0.6 * Math.exp(-((t % 0.25) * 20)) : 0;
      
      // Add high-frequency content (hi-hat like)
      const hihat = (Math.random() - 0.5) * 0.1 * (Math.sin(t * 16) > 0.5 ? 1 : 0);
      
      // Envelope for natural decay
      const envelope = Math.exp(-t * 0.05) * (0.7 + 0.3 * Math.sin(t * 0.5));
      
      // Filter effect simulation
      const cutoff = 0.5 + 0.3 * Math.sin(t * 0.7);
      const filteredNoise = (Math.random() - 0.5) * 0.05 * cutoff;
      
      // Combine all elements
      let sample = (baseTone + harmonic1 + harmonic2 + kickPattern + hihat + filteredNoise) * envelope;
      
      // Add some compression-like effect
      if (Math.abs(sample) > 0.7) {
        sample = sample > 0 ? 0.7 + (sample - 0.7) * 0.3 : -0.7 + (sample + 0.7) * 0.3;
      }
      
      // Final limiting
      channelData[i] = Math.max(-1, Math.min(1, sample));
    }

    wavesurferRef.current.loadBlob(bufferToBlob(buffer));
  };

  const bufferToBlob = (buffer: AudioBuffer): Blob => {
    const numberOfChannels = buffer.numberOfChannels;
    const length = buffer.length;
    const sampleRate = buffer.sampleRate;
    
    // Create WAV file
    const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
    const view = new DataView(arrayBuffer);
    
    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * 2, true);
    view.setUint16(32, numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * 2, true);
    
    // Convert audio data
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const channelData = buffer.getChannelData(channel);
        const sample = Math.max(-1, Math.min(1, channelData[i]));
        view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
        offset += 2;
      }
    }
    
    return new Blob([arrayBuffer], { type: 'audio/wav' });
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const fileName = file.name.toLowerCase();
    const fileExtension = fileName.split('.').pop() || '';
    
    // 检测文件类型
    if (fileExtension === 'mid' || fileExtension === 'midi') {
      handleMidiFile(file);
    } else {
      handleAudioFile(file);
    }
  };

  const handleAudioFile = (file: File) => {
    if (wavesurferRef.current) {
      // 停止MIDI播放并清理资源
      if (fileType === 'midi' && isMidiPlaying) {
        stopMidiPlayback();
      }
      
      setIsLoading(true);
      setFileType('audio');
      setIsMidiPlaying(false);
      setIsAudioPlaying(false);
      setParsedMidiData(null); // 清理MIDI数据
      wavesurferRef.current.loadBlob(file);
      
      // 更新轨道名称为文件名
      const fileName = file.name.replace(/\.[^/.]+$/, ""); // 移除文件扩展名
      onUpdateTrack(track.id, { audioFile: file, name: fileName });
    }
  };

  const handleMidiFile = async (file: File) => {
    try {
      // 停止音频播放
      if (fileType === 'audio' && isAudioPlaying && wavesurferRef.current) {
        wavesurferRef.current.pause();
        setIsAudioPlaying(false);
      }
      
      setIsLoading(true);
      setFileType('midi');
      setIsMidiPlaying(false);
      
      // 读取并解析MIDI文件
      const arrayBuffer = await file.arrayBuffer();
      const midiData = new Uint8Array(arrayBuffer);
      
      // 解析MIDI数据并存储
      const parsedNotes = parseMidiFile(midiData);
      setParsedMidiData(parsedNotes);
      
      console.log(`MIDI file loaded: ${file.name}, parsed ${parsedNotes.length} notes`);
      
      // 存储文件引用并更新轨道名称
      const fileName = file.name.replace(/\.[^/.]+$/, ""); // 移除文件扩展名
      onUpdateTrack(track.id, { audioFile: file, name: fileName });
      
      // 保持演示波形显示
      generateDemoWaveform();
      
      // 等待波形生成完成后再检查状态
      setTimeout(() => {
        if (wavesurferRef.current) {
          const wsDuration = wavesurferRef.current.getDuration();
          const midiDuration = parsedNotes.length > 0 ? Math.max(...parsedNotes.map(note => note.time + note.duration)) : 0;
          console.log(`🌊 演示波形生成完成, duration: ${wsDuration.toFixed(2)}s`);
          console.log(`🎼 MIDI文件总时长: ${midiDuration.toFixed(2)}s`);
          
          // 设置为MIDI的实际时长
          setDuration(midiDuration);
        }
        setIsLoading(false);
      }, 100);
    } catch (error) {
      console.error('Error loading MIDI file:', error);
      setIsLoading(false);
      setParsedMidiData(null);
    }
  };

  const handlePlay = () => {
    if (fileType === 'midi' && track.audioFile) {
      // MIDI 播放逻辑
      if (isMidiPlaying) {
        stopMidiPlayback();
      } else {
        playMidiFile();
      }
    } else {
      // 音频文件播放逻辑
      if (wavesurferRef.current) {
        if (isAudioPlaying) {
          wavesurferRef.current.pause();
        } else {
          wavesurferRef.current.play();
        }
      }
    }
  };

  const playMidiFile = async () => {
    if (!parsedMidiData || parsedMidiData.length === 0) {
      console.warn('No MIDI data available for playbook');
      return;
    }
    
    try {
      const Tone = await import('tone');
      
      if (Tone.context.state !== 'running') {
        await Tone.start();
      }

      // 清理之前的乐器
      if (midiInstrument && !midiInstrument.disposed) {
        midiInstrument.dispose();
      }

      // 创建更高质量的乐器 - 简化配置避免TypeScript错误
      const instrument = new Tone.PolySynth(Tone.Synth, {
        oscillator: {
          type: "fatsawtooth"
        },
        envelope: {
          attack: 0.02,
          decay: 0.1,
          sustain: 0.9,
          release: 0.4
        }
      }).toDestination();
      
      // 添加滤波器作为独立效果器
      const filter = new Tone.Filter({
        frequency: 800,
        Q: 6,
        type: "lowpass"
      }).toDestination();
      
      instrument.connect(filter);
      
      // 添加混响效果提升音质
      const reverb = new Tone.Reverb({
        decay: 1.5,
        preDelay: 0.01
      }).toDestination();
      
      // 添加压缩器提升音质
      const compressor = new Tone.Compressor(-30, 3).toDestination();
      
      instrument.connect(reverb);
      instrument.connect(compressor);
      
      instrument.volume.value = -3; // 提高音量
      setMidiInstrument(instrument);
      setIsMidiPlaying(true);
      midiPlayingRef.current = true;

      // 计算MIDI总时长
      const maxTime = Math.max(...parsedMidiData.map(note => note.time + note.duration));
      setMidiStartTime(Date.now());
      
      // 设置MIDI的实际时长并保持WaveSurfer暂停状态
      if (wavesurferRef.current) {
        console.log(`🎵 MIDI总时长: ${maxTime.toFixed(2)}s`);
        setDuration(maxTime); // 使用MIDI的实际时长
        
        // 不要让WaveSurfer播放，只是确保它处于可seek状态
        try {
          wavesurferRef.current.setVolume(0); // 静音
          // 不调用play()，避免WaveSurfer自己的播放进度干扰
          console.log(`✅ WaveSurfer已设为静音状态，准备手动控制进度`);
        } catch (error) {
          console.warn('⚠️ 无法设置WaveSurfer状态:', error);
        }
      }

      // 使用预解析的MIDI数据
      const midiNotes = parsedMidiData;
      console.log(`Playing MIDI with ${midiNotes.length} notes from parsed data`);
      
      // 播放MIDI音符
      const now = Tone.now();
      let totalDuration = 0;
      
      midiNotes.forEach(note => {
        const startTime = now + note.time;
        const endTime = startTime + note.duration;
        totalDuration = Math.max(totalDuration, endTime);
        
        instrument.triggerAttackRelease(
          note.note,
          note.duration,
          startTime,
          note.velocity
        );
      });

      // 启动进度更新 - 让WaveSurfer跟随MIDI播放进度
      console.log(`🚀 启动MIDI进度更新间隔，初始midiPlayingRef=${midiPlayingRef.current}`);
      const progressInterval = setInterval(() => {
        console.log(`⏰ 进度更新检查: midiPlayingRef=${midiPlayingRef.current}`);
        if (!midiPlayingRef.current) {
          console.log(`🛑 停止进度更新：midiPlayingRef=${midiPlayingRef.current}`);
          clearInterval(progressInterval);
          return;
        }
        
        const elapsed = (Date.now() - midiStartTime) / 1000;
        const progress = Math.min(elapsed / maxTime, 1);
        
        console.log(`🎵 MIDI Progress: ${(progress * 100).toFixed(1)}%, elapsed: ${elapsed.toFixed(2)}s, maxTime: ${maxTime.toFixed(2)}s`);
        
        // 通知父组件更新currentTime，让useEffect处理WaveSurfer同步
        if (onCurrentTimeUpdate) {
          console.log(`🔄 调用onCurrentTimeUpdate: ${elapsed.toFixed(2)}s`);
          onCurrentTimeUpdate(elapsed);
        } else {
          console.warn(`⚠️ onCurrentTimeUpdate回调不存在！`);
        }
        
        if (progress >= 1) {
          console.log('✅ MIDI播放完成');
          clearInterval(progressInterval);
          stopMidiPlayback();
        }
      }, 100); // 降低更新频率到100ms，避免过于频繁的更新

      // 播放结束后清理
      setTimeout(() => {
        stopMidiPlayback();
      }, (totalDuration - now + 1) * 1000);

    } catch (error) {
      console.error('Error playing MIDI:', error);
      setIsMidiPlaying(false);
    }
  };

  const stopMidiPlayback = () => {
    console.log('🛑 停止MIDI播放');
    
    if (midiInstrument && !midiInstrument.disposed) {
      midiInstrument.releaseAll();
      midiInstrument.dispose();
      setMidiInstrument(null);
    }
    setIsMidiPlaying(false);
    midiPlayingRef.current = false;
    
    // 重置WaveSurfer状态
    if (wavesurferRef.current) {
      try {
        console.log('🔇 重置WaveSurfer状态');
        wavesurferRef.current.pause(); // 确保停止播放
        wavesurferRef.current.setVolume(1); // 恢复音量，以备音频文件播放
        wavesurferRef.current.seekTo(0); // 重置到开始位置
        console.log('✅ WaveSurfer已重置');
      } catch (error) {
        console.debug('WaveSurfer reset error:', error);
      }
    }
    
    // 重置显示的时间
    if (onCurrentTimeUpdate) {
      onCurrentTimeUpdate(0);
    }
  };

  // 使用专业MIDI解析库的函数
  const parseMidiFile = (midiData: Uint8Array) => {
    const notes: Array<{note: string, time: number, duration: number, velocity: number}> = [];
    const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];

    try {
      // 使用midi-file库解析MIDI文件
      console.log('MIDI data length:', midiData.length);
      console.log('First 20 bytes:', Array.from(midiData.slice(0, 20)));
      
      const parsed = MidiFile.parseMidi(midiData);
      
      console.log('Parsed MIDI structure:', parsed);
      console.log('Number of tracks:', parsed.tracks?.length || 0);
      console.log('Time division:', parsed.timeDivision);
      
      if (!parsed.tracks || parsed.tracks.length === 0) {
        throw new Error('No tracks found in MIDI file');
      }

      const activeNotes = new Map(); // 跟踪正在播放的音符
      const ticksPerBeat = parsed.timeDivision || 480;
      let microsecondsPerBeat = 500000; // 默认120 BPM
      const ticksToSeconds = (ticks: number) => {
        return (ticks / ticksPerBeat) * (microsecondsPerBeat / 1000000);
      };

      // 处理所有轨道
      parsed.tracks.forEach((track: any, trackIndex: number) => {
        console.log(`Processing track ${trackIndex}, events:`, track?.length || 0);
        let currentTick = 0;
        
        if (!track || track.length === 0) {
          console.log(`Track ${trackIndex} has no events`);
          return;
        }
        
        track.forEach((event: any, eventIndex: number) => {
          currentTick += event.deltaTime || 0;
          const currentTime = ticksToSeconds(currentTick);
          
          // 处理Tempo变化事件
          if (event.type === 'setTempo' && event.microsecondsPerBeat) {
            microsecondsPerBeat = event.microsecondsPerBeat;
            console.log(`Tempo change detected: ${60000000 / microsecondsPerBeat} BPM`);
          }
          
          // 只记录前几个事件来调试
          if (eventIndex < 10) {
            console.log(`Track ${trackIndex} Event ${eventIndex}:`, {
              type: event.type,
              deltaTime: event.deltaTime,
              noteNumber: event.noteNumber,
              velocity: event.velocity,
              currentTime: currentTime
            });
          }
          
          // midi-file库的事件格式
          if (event.type === 'noteOn' && event.velocity > 0) {
            // Note On事件
            const midiNote = event.noteNumber;
            const velocity = event.velocity;
            
            console.log(`Note On detected: note=${midiNote}, velocity=${velocity}, time=${currentTime}`);
            
            const octave = Math.floor(midiNote / 12) - 1;
            const noteIndex = midiNote % 12;
            const noteName = noteNames[noteIndex] + octave;
            
            // 使用音符号和轨道创建唯一键
            const noteKey = `${midiNote}-${trackIndex}-${currentTick}`;
            activeNotes.set(noteKey, {
              note: noteName,
              startTime: currentTime,
              velocity: velocity / 127,
              midiNote: midiNote,
              trackIndex: trackIndex
            });
            
            console.log(`Added active note: ${noteName} at ${currentTime}s`);
            
          } else if (event.type === 'noteOn' && event.velocity === 0) {
            // Note On with velocity 0 = Note Off
            const midiNote = event.noteNumber;
            const noteOffKey = `${midiNote}-${trackIndex}`;
            let foundKey: string | null = null;
            
            // 找到对应的活跃音符
            activeNotes.forEach((noteInfo, key) => {
              if (!foundKey && key.startsWith(noteOffKey)) {
                foundKey = key;
              }
            });
            
            if (foundKey) {
              const noteInfo = activeNotes.get(foundKey);
              const duration = Math.max(0.1, currentTime - noteInfo.startTime);
              
              notes.push({
                note: noteInfo.note,
                time: noteInfo.startTime,
                duration: duration,
                velocity: noteInfo.velocity
              });
              
              console.log(`Note Off (velocity 0): ${noteInfo.note}, duration=${duration}`);
              activeNotes.delete(foundKey);
            }
            
          } else if (event.type === 'noteOff') {
            // Note Off事件
            const midiNote = event.noteNumber;
            const noteOffKey = `${midiNote}-${trackIndex}`;
            let foundKey: string | null = null;
            
            // 找到对应的活跃音符
            activeNotes.forEach((noteInfo, key) => {
              if (!foundKey && key.startsWith(noteOffKey)) {
                foundKey = key;
              }
            });
            
            if (foundKey) {
              const noteInfo = activeNotes.get(foundKey);
              const duration = Math.max(0.1, currentTime - noteInfo.startTime);
              
              notes.push({
                note: noteInfo.note,
                time: noteInfo.startTime,
                duration: duration,
                velocity: noteInfo.velocity
              });
              
              console.log(`Note Off: ${noteInfo.note}, duration=${duration}`);
              activeNotes.delete(foundKey);
            }
          }
        });
      });

      // 处理仍在播放的音符
      activeNotes.forEach(noteInfo => {
        notes.push({
          note: noteInfo.note,
          time: noteInfo.startTime,
          duration: 1.0, // 默认时长
          velocity: noteInfo.velocity
        });
        console.log(`Unclosed note: ${noteInfo.note}`);
      });

      console.log(`✅ Successfully parsed ${notes.length} notes from MIDI file`);
      if (notes.length > 0) {
        console.log('First few notes:', notes.slice(0, 5));
      }
      
    } catch (error) {
      console.error('❌ MIDI parsing failed:', error);
      console.warn('Using fallback notes due to parsing error');
    }

    // 如果没有解析到音符，返回默认音符序列
    if (notes.length === 0) {
      const defaultNotes = [
        { note: 'C4', time: 0, duration: 0.5, velocity: 0.7 },
        { note: 'E4', time: 0.5, duration: 0.5, velocity: 0.7 },
        { note: 'G4', time: 1.0, duration: 0.5, velocity: 0.7 },
        { note: 'C5', time: 1.5, duration: 1.0, velocity: 0.8 }
      ];
      return defaultNotes;
    }

    // 按时间排序
    notes.sort((a, b) => a.time - b.time);
    return notes;
  };

  const handleSeek = (event: React.MouseEvent<HTMLDivElement>) => {
    if (wavesurferRef.current && waveformRef.current) {
      const rect = waveformRef.current.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const progress = x / rect.width;
      wavesurferRef.current.seekTo(progress);
    }
  };

  return (
    <div className="waveform-editor">
      <div className="waveform-header">
        <div className="flex items-center justify-between p-3 bg-gray-800">
          <h3 className="text-sm font-bold text-blue-400">
            Waveform Editor - {track.name}
          </h3>
          <div className="flex items-center gap-2">
            <label className="text-xs text-gray-400">
              Load Audio:
            </label>
            <input
              type="file"
              accept=".wav,.mp3,.ogg,.m4a,.aac,.mid,.midi,audio/*"
              onChange={handleFileUpload}
              className="text-xs text-gray-400 file:mr-2 file:py-1 file:px-2 file:rounded file:border-0 file:text-xs file:bg-blue-600 file:text-white hover:file:bg-blue-700"
            />
            <label className="text-xs text-gray-400">Zoom:</label>
            <input
              type="range"
              min="0.5"
              max="5"
              step="0.1"
              value={zoom}
              onChange={(e) => setZoom(parseFloat(e.target.value))}
              className="w-20"
            />
            <button
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
              onClick={handlePlay}
            >
              {(fileType === 'midi' ? isMidiPlaying : isAudioPlaying) ? 'Pause' : 'Play'}
            </button>
          </div>
        </div>
      </div>
      
      <div className="waveform-content">
        <div className="flex items-center justify-between p-2 bg-gray-700 text-xs">
          <span className="text-gray-400">
            Duration: {isFinite(duration) ? duration.toFixed(2) : '0.00'}s
          </span>
          <span className="text-gray-400">
            Position: {isFinite(currentTime) ? currentTime.toFixed(2) : '0.00'}s
          </span>
        </div>
        
        <div className="relative">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-800 bg-opacity-75 z-10">
              <div className="text-blue-400">Loading waveform...</div>
            </div>
          )}
          
          <div 
            ref={waveformRef}
            className="waveform-display cursor-pointer"
            onClick={handleSeek}
          />
        </div>
        
        <div className="waveform-controls flex items-center gap-2 p-2 bg-gray-700">
          <button className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500">
            Trim
          </button>
          <button className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500">
            Fade In
          </button>
          <button className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500">
            Fade Out
          </button>
          <button className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500">
            Normalize
          </button>
          <button className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500">
            Reverse
          </button>
        </div>
      </div>
    </div>
  );
};

export default WaveformEditor;