import axios from 'axios';

const DAW_API_BASE = 'http://localhost:8080';

export interface Project {
  _id?: string;
  name: string;
  userId: string;
  tempo: number;
  timeSignature: number;
  tracks: Track[];
  clips: Clip[];
  collaborators: string[];
  visibility: 'private' | 'public' | 'collaborative';
  lastModified: Date;
}

export interface Track {
  id: string;
  name: string;
  type: 'audio' | 'midi';
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  armed: boolean;
  effects: string[];
}

export interface Clip {
  id: string;
  trackId: string;
  start: number;
  duration: number;
  name: string;
  color: string;
  notes: Note[];
}

export interface Note {
  id: string;
  pitch: number;
  start: number;
  duration: number;
  velocity: number;
}

class DAWApiService {
  private api = axios.create({
    baseURL: DAW_API_BASE,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  private isConnected = false;
  private connectionListeners: ((connected: boolean) => void)[] = [];
  private connectionChecked = false;

  // 检查API连接状态
  async checkConnection(): Promise<boolean> {
    try {
      await this.api.get('/health', {
        timeout: 2000 // 2秒超时
      });
      this.setConnectionStatus(true);
      return true;
    } catch (error) {
      this.setConnectionStatus(false);
      return false;
    }
  }

  private setConnectionStatus(connected: boolean) {
    if (this.isConnected !== connected) {
      this.isConnected = connected;
      this.connectionListeners.forEach(listener => listener(connected));
    }
  }

  onConnectionChange(listener: (connected: boolean) => void) {
    this.connectionListeners.push(listener);
    return () => {
      const index = this.connectionListeners.indexOf(listener);
      if (index > -1) {
        this.connectionListeners.splice(index, 1);
      }
    };
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  // 初始化连接检查（仅在首次调用时执行）
  async initialize(): Promise<boolean> {
    if (!this.connectionChecked) {
      this.connectionChecked = true;
      await this.checkConnection();
    }
    return this.isConnected;
  }

  // Project operations
  async createProject(projectData: Partial<Project>): Promise<Project> {
    // 首先检查连接状态，如果离线则直接使用mock
    if (!this.isConnected) {
      return this.createMockProject(projectData);
    }

    try {
      const response = await this.api.post('/projects', projectData, {
        timeout: 3000 // 3秒超时
      });
      return response.data.data;
    } catch (error) {
      // 静默处理错误，不显示警告
      return this.createMockProject(projectData);
    }
  }

  private createMockProject(projectData: Partial<Project>): Project {
    const mockProject: Project = {
      _id: `mock_project_${Date.now()}`,
      name: projectData.name || 'New Project',
      userId: projectData.userId || 'demo-user',
      tempo: projectData.tempo || 120,
      timeSignature: projectData.timeSignature || 4,
      tracks: projectData.tracks || [],
      clips: projectData.clips || [],
      collaborators: projectData.collaborators || [],
      visibility: projectData.visibility || 'private',
      lastModified: new Date(),
    };
    
    // 保存到localStorage作为mock存储
    const existingProjects = JSON.parse(localStorage.getItem('dawProjects') || '[]');
    existingProjects.push(mockProject);
    localStorage.setItem('dawProjects', JSON.stringify(existingProjects));
    
    return mockProject;
  }

  async getProjects(userId: string): Promise<Project[]> {
    // 首先检查连接状态，如果离线则直接使用mock
    if (!this.isConnected) {
      return this.getMockProjects(userId);
    }

    try {
      // 快速超时设置，避免卡住
      const response = await this.api.get(`/projects?userId=${userId}`, {
        timeout: 3000 // 3秒超时
      });
      return response.data.data;
    } catch (error) {
      // 静默处理错误，不显示警告
      return this.getMockProjects(userId);
    }
  }

  private getMockProjects(userId: string): Project[] {
    // 从localStorage获取mock项目
    const existingProjects = JSON.parse(localStorage.getItem('dawProjects') || '[]');
    return existingProjects.filter((project: Project) => project.userId === userId);
  }

  async getProject(projectId: string): Promise<Project> {
    const response = await this.api.get(`/projects/${projectId}`);
    return response.data.data;
  }

  async updateProject(projectId: string, updates: Partial<Project>): Promise<Project> {
    // 首先检查连接状态，如果离线则直接使用mock
    if (!this.isConnected) {
      return this.updateMockProject(projectId, updates);
    }

    try {
      const response = await this.api.patch(`/projects/${projectId}`, updates);
      return response.data.data;
    } catch (error) {
      // 静默处理错误，不显示警告
      return this.updateMockProject(projectId, updates);
    }
  }

  private updateMockProject(projectId: string, updates: Partial<Project>): Project {
    // 从localStorage更新mock项目
    const existingProjects = JSON.parse(localStorage.getItem('dawProjects') || '[]');
    const projectIndex = existingProjects.findIndex((p: Project) => p._id === projectId);
    
    if (projectIndex !== -1) {
      existingProjects[projectIndex] = { 
        ...existingProjects[projectIndex], 
        ...updates, 
        lastModified: new Date() 
      };
      localStorage.setItem('dawProjects', JSON.stringify(existingProjects));
      return existingProjects[projectIndex];
    }
    
    throw new Error('Project not found');
  }

  async deleteProject(projectId: string): Promise<void> {
    await this.api.delete(`/projects/${projectId}`);
  }

  async saveProject(projectId: string, projectData: any): Promise<Project> {
    // 首先检查连接状态，如果离线则直接使用mock
    if (!this.isConnected) {
      return this.saveMockProject(projectId, projectData);
    }

    try {
      const response = await this.api.post(`/projects/${projectId}/save`, projectData);
      return response.data.data;
    } catch (error) {
      // 静默处理错误，不显示警告
      return this.saveMockProject(projectId, projectData);
    }
  }

  private saveMockProject(projectId: string, projectData: any): Project {
    // 如果API不可用，保存到localStorage
    const existingProjects = JSON.parse(localStorage.getItem('dawProjects') || '[]');
    const projectIndex = existingProjects.findIndex((p: Project) => p._id === projectId);
    
    if (projectIndex !== -1) {
      const updatedProject = {
        ...existingProjects[projectIndex],
        ...projectData,
        lastModified: new Date()
      };
      existingProjects[projectIndex] = updatedProject;
      localStorage.setItem('dawProjects', JSON.stringify(existingProjects));
      return updatedProject;
    }
    
    throw new Error('Project not found');
  }

  // Collaboration operations
  async addCollaborator(projectId: string, userId: string): Promise<Project> {
    const response = await this.api.post(`/projects/${projectId}/collaborators`, { userId });
    return response.data.data;
  }

  async removeCollaborator(projectId: string, userId: string): Promise<Project> {
    const response = await this.api.delete(`/projects/${projectId}/collaborators/${userId}`);
    return response.data.data;
  }

  // Template and preset operations
  async getProjectTemplates(): Promise<Project[]> {
    // Mock templates for now
    return [
      {
        name: 'Empty Project',
        userId: 'template',
        tempo: 120,
        timeSignature: 4,
        tracks: [],
        clips: [],
        collaborators: [],
        visibility: 'public',
        lastModified: new Date(),
      },
      {
        name: 'Basic Song Template',
        userId: 'template',
        tempo: 120,
        timeSignature: 4,
        tracks: [
          {
            id: 'template-track-1',
            name: 'Lead Synth',
            type: 'midi',
            volume: 0.8,
            pan: 0,
            muted: false,
            solo: false,
            armed: false,
            effects: [],
          },
          {
            id: 'template-track-2',
            name: 'Bass',
            type: 'midi',
            volume: 0.7,
            pan: 0,
            muted: false,
            solo: false,
            armed: false,
            effects: [],
          },
          {
            id: 'template-track-3',
            name: 'Drums',
            type: 'midi',
            volume: 0.9,
            pan: 0,
            muted: false,
            solo: false,
            armed: false,
            effects: [],
          },
        ],
        clips: [],
        collaborators: [],
        visibility: 'public',
        lastModified: new Date(),
      },
      {
        name: 'Electronic Template',
        userId: 'template',
        tempo: 128,
        timeSignature: 4,
        tracks: [
          {
            id: 'template-track-4',
            name: 'Kick',
            type: 'midi',
            volume: 1.0,
            pan: 0,
            muted: false,
            solo: false,
            armed: false,
            effects: ['compressor'],
          },
          {
            id: 'template-track-5',
            name: 'Bass Synth',
            type: 'midi',
            volume: 0.8,
            pan: 0,
            muted: false,
            solo: false,
            armed: false,
            effects: ['filter', 'distortion'],
          },
          {
            id: 'template-track-6',
            name: 'Lead Arp',
            type: 'midi',
            volume: 0.6,
            pan: 0.3,
            muted: false,
            solo: false,
            armed: false,
            effects: ['delay', 'reverb'],
          },
        ],
        clips: [],
        collaborators: [],
        visibility: 'public',
        lastModified: new Date(),
      },
    ];
  }

  // Export operations
  async exportProject(projectId: string, format: 'wav' | 'mp3' | 'midi'): Promise<Blob> {
    const response = await this.api.get(`/projects/${projectId}/export/${format}`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // Audio file operations
  async uploadAudioFile(file: File, projectId: string): Promise<string> {
    const formData = new FormData();
    formData.append('audio', file);
    formData.append('projectId', projectId);
    
    const response = await this.api.post('/audio/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data.data.fileUrl;
  }

  // Preset operations
  async getInstrumentPresets(instrumentType: string): Promise<any[]> {
    // Mock presets for now
    const presets: { [key: string]: any[] } = {
      synth: [
        { name: 'Classic Lead', params: { oscillator: 'sawtooth', filter: 'lowpass' } },
        { name: 'Warm Pad', params: { oscillator: 'sine', filter: 'lowpass' } },
        { name: 'Bass Drop', params: { oscillator: 'square', filter: 'lowpass' } },
      ],
      drum: [
        { name: 'Acoustic Kit', params: { kick: 'acoustic', snare: 'acoustic' } },
        { name: 'Electronic Kit', params: { kick: 'electronic', snare: 'electronic' } },
        { name: 'Vintage Kit', params: { kick: 'vintage', snare: 'vintage' } },
      ],
    };
    
    return presets[instrumentType] || [];
  }

  async getEffectPresets(effectType: string): Promise<any[]> {
    // Mock effect presets
    const presets: { [key: string]: any[] } = {
      reverb: [
        { name: 'Small Room', params: { roomSize: 0.3, dampening: 3000 } },
        { name: 'Large Hall', params: { roomSize: 0.9, dampening: 1000 } },
        { name: 'Plate Reverb', params: { roomSize: 0.6, dampening: 2000 } },
      ],
      delay: [
        { name: 'Short Delay', params: { delayTime: '8n', feedback: 0.2 } },
        { name: 'Echo', params: { delayTime: '4n', feedback: 0.4 } },
        { name: 'Long Delay', params: { delayTime: '2n', feedback: 0.6 } },
      ],
    };
    
    return presets[effectType] || [];
  }
}

export const dawApiService = new DAWApiService();