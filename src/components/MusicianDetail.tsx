import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { 
  PlayIcon, 
  HeartIcon, 
  ShareIcon, 
  ChatBubbleLeftIcon,
  UserPlusIcon,
  ArrowLeftIcon,
  MusicalNoteIcon,
  ShoppingCartIcon,
  BookmarkIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { useLanguage } from '../contexts/LanguageContext';

interface Song {
  id: number;
  title: string;
  duration: string;
  plays: number;
  likes: number;
  coverUrl?: string;
}

interface Work {
  id: number;
  title: string;
  lyricist: string;
  composer: string;
  tags: string[];
  rating: number;
  ratingCount: number;
  trialDuration: string;
  trialCount: number;
  price: number;
  currency: string;
  coverUrl: string;
  previewUrl?: string;
}

interface Demo {
  id: number;
  title: string;
  lyricist: string;
  composer: string;
  tags: string[];
  rating: number;
  ratingCount: number;
  trialDuration: string;
  trialCount: number;
  price: number;
  currency: string;
  coverUrl: string;
  participants: Array<{
    role: string;
    name: string;
  }>;
}

interface MusicianDetailData {
  id: number;
  name: string;
  avatar: string;
  coverImage: string;
  genre: string;
  location: string;
  followers: number;
  following: number;
  plays: number;
  rating: number;
  verified: boolean;
  description: string;
  tags: string[];
  songs: Song[];
  works: Work[];
  demos: Demo[];
  relatedMusicians: Array<{
    id: number;
    name: string;
    avatar: string;
    genre: string;
    followers: number;
  }>;
}

const MusicianDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [musician, setMusician] = useState<MusicianDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'works' | 'demos'>('works');
  const [isFollowing, setIsFollowing] = useState(false);
  const { t, language } = useLanguage();

  useEffect(() => {
    const fetchMusicianDetail = async () => {
      try {
        // Mock data for demonstration
        setMusician({
          id: parseInt(id || '1'),
          name: t('musician.detail.name'),
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
          coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
          genre: t('musician.detail.genre'),
          location: t('musician.detail.location'),
          followers: 2.8,
          following: 328,
          plays: 896,
          rating: 4.2,
          verified: true,
          description: t('musician.detail.description'),
          tags: [t('musician.detail.tag.pop'), t('musician.detail.tag.rock'), t('musician.detail.tag.creative'), t('musician.detail.tag.vocal')],
          songs: [
            {
              id: 1,
              title: t('song.1.title'),
              duration: '04:31',
              plays: 24000,
              likes: 856,
              coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 2,
              title: t('song.2.title'),
              duration: '04:18',
              plays: 18000,
              likes: 623,
              coverUrl: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 3,
              title: t('song.3.title'),
              duration: '03:43',
              plays: 32000,
              likes: 1200,
              coverUrl: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 4,
              title: t('song.4.title'),
              duration: '04:45',
              plays: 15000,
              likes: 432,
              coverUrl: 'https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 5,
              title: t('song.5.title'),
              duration: '03:53',
              plays: 28000,
              likes: 987,
              coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 6,
              title: t('song.6.title'),
              duration: '04:19',
              plays: 45000,
              likes: 1500,
              coverUrl: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 7,
              title: t('song.7.title'),
              duration: '03:25',
              plays: 22000,
              likes: 678,
              coverUrl: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 8,
              title: t('song.8.title'),
              duration: '04:45',
              plays: 19000,
              likes: 534,
              coverUrl: 'https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 9,
              title: t('song.9.title'),
              duration: '03:52',
              plays: 35000,
              likes: 1100,
              coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 10,
              title: t('song.10.title'),
              duration: '04:10',
              plays: 41000,
              likes: 1350,
              coverUrl: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            }
          ],
          works: [
            {
              id: 1,
              title: language === 'zh' ? '为梦而飞' : 'Dreams in Flight',
              lyricist: language === 'zh' ? '张丹' : 'Zhang Dan',
              composer: language === 'zh' ? '张丹' : 'Zhang Dan',
              tags: language === 'zh' ? ['流行', '创新', '有人声', '90BPM', '4/4', '安静'] : ['Pop', 'Creative', 'Vocal', '90BPM', '4/4', 'Serene'],
              rating: 10.1,
              ratingCount: 856,
              trialDuration: '24:0k',
              trialCount: 856,
              price: 500,
              currency: 'HK$',
              coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
              previewUrl: '/audio/preview1.mp3'
            },
            {
              id: 2,
              title: language === 'zh' ? '朝阳暮岁都是你' : 'Morning Sunshine',
              lyricist: language === 'zh' ? '秦立南' : 'Qin Linan',
              composer: language === 'zh' ? '秦立南' : 'Qin Linan',
              tags: language === 'zh' ? ['流行', '抒情', '有人声', '68BPM', '4/4', '功课'] : ['Pop', 'Lyrical', 'Vocal', '68BPM', '4/4', 'Meritorious'],
              rating: 47.0,
              ratingCount: 856,
              trialDuration: '24:0k',
              trialCount: 856,
              price: 0,
              currency: 'HK$',
              coverUrl: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
              previewUrl: '/audio/preview2.mp3'
            },
            {
              id: 3,
              title: language === 'zh' ? '午夜蓝调' : 'Midnight Blues',
              lyricist: language === 'zh' ? '陈阿力' : 'Alex Chen',
              composer: language === 'zh' ? '陈阿力' : 'Alex Chen',
              tags: language === 'zh' ? ['蓝调', '爵士', '吉他', '85BPM', '4/4', '忧郁'] : ['Blues', 'Jazz', 'Guitar', '85BPM', '4/4', 'Moody'],
              rating: 8.9,
              ratingCount: 642,
              trialDuration: '30:0k',
              trialCount: 1200,
              price: 350,
              currency: 'HK$',
              coverUrl: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
              previewUrl: '/audio/preview3.mp3'
            },
            {
              id: 4,
              title: language === 'zh' ? '电子梦境' : 'Electric Dreams',
              lyricist: language === 'zh' ? '刘萨拉' : 'Sara Liu',
              composer: language === 'zh' ? '刘萨拉' : 'Sara Liu',
              tags: language === 'zh' ? ['电子', '合成器', '舞曲', '128BPM', '4/4', '充满活力'] : ['Electronic', 'Synth', 'Dance', '128BPM', '4/4', 'Energetic'],
              rating: 9.5,
              ratingCount: 1024,
              trialDuration: '45:0k',
              trialCount: 2100,
              price: 800,
              currency: 'HK$',
              coverUrl: 'https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
              previewUrl: '/audio/preview4.mp3'
            },
            {
              id: 5,
              title: language === 'zh' ? '原声之心' : 'Acoustic Heart',
              lyricist: language === 'zh' ? '黄大卫' : 'David Wong',
              composer: language === 'zh' ? '黄大卫' : 'David Wong',
              tags: language === 'zh' ? ['民谣', '原声', '吉他', '75BPM', '3/4', '温柔'] : ['Folk', 'Acoustic', 'Guitar', '75BPM', '3/4', 'Gentle'],
              rating: 7.8,
              ratingCount: 445,
              trialDuration: '28:0k',
              trialCount: 890,
              price: 250,
              currency: 'HK$',
              coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
              previewUrl: '/audio/preview5.mp3'
            }
          ],
          demos: [
            {
              id: 1,
              title: language === 'zh' ? '未命名演示 1' : 'Untitled Demo 1',
              lyricist: language === 'zh' ? '张丹' : 'Zhang Dan',
              composer: language === 'zh' ? '李伟' : 'Li Wei',
              tags: language === 'zh' ? ['演示', '流行', '人声'] : ['Demo', 'Pop', 'Vocal'],
              rating: 7.5,
              ratingCount: 324,
              trialDuration: '15:0k',
              trialCount: 456,
              price: 0,
              currency: '',
              coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
              participants: [
                { role: language === 'zh' ? '主唱' : 'Vocalist', name: language === 'zh' ? '张丹' : 'Zhang Dan' },
                { role: language === 'zh' ? '制作人' : 'Producer', name: language === 'zh' ? '李伟' : 'Li Wei' }
              ]
            },
            {
              id: 2,
              title: language === 'zh' ? '实验音轨' : 'Experimental Track',
              lyricist: language === 'zh' ? '王明' : 'Wang Ming',
              composer: language === 'zh' ? '陈宇' : 'Chen Yu',
              tags: language === 'zh' ? ['演示', '摇滚', '吉他'] : ['Demo', 'Rock', 'Guitar'],
              rating: 8.2,
              ratingCount: 567,
              trialDuration: '20:0k',
              trialCount: 789,
              price: 0,
              currency: '',
              coverUrl: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
              participants: [
                { role: language === 'zh' ? '作曲' : 'Composer', name: language === 'zh' ? '张丹' : 'Zhang Dan' },
                { role: language === 'zh' ? '吉他手' : 'Guitarist', name: language === 'zh' ? '王明' : 'Wang Ming' },
                { role: language === 'zh' ? '鼓手' : 'Drummer', name: language === 'zh' ? '陈宇' : 'Chen Yu' }
              ]
            },
            {
              id: 3,
              title: language === 'zh' ? '爵士融合演示' : 'Jazz Fusion Demo',
              lyricist: language === 'zh' ? '陈萨拉' : 'Sarah Chen',
              composer: language === 'zh' ? '刘迈克' : 'Mike Liu',
              tags: language === 'zh' ? ['演示', '爵士', '融合'] : ['Demo', 'Jazz', 'Fusion'],
              rating: 9.1,
              ratingCount: 234,
              trialDuration: '18:0k',
              trialCount: 345,
              price: 0,
              currency: '',
              coverUrl: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
              participants: [
                { role: language === 'zh' ? '萨克斯风' : 'Saxophone', name: language === 'zh' ? '陈萨拉' : 'Sarah Chen' },
                { role: language === 'zh' ? '钢琴' : 'Piano', name: language === 'zh' ? '刘迈克' : 'Mike Liu' }
              ]
            },
            {
              id: 4,
              title: language === 'zh' ? '环境音乐草图' : 'Ambient Sketch',
              lyricist: language === 'zh' ? '黄亚历克斯' : 'Alex Wong',
              composer: language === 'zh' ? '黄亚历克斯' : 'Alex Wong',
              tags: language === 'zh' ? ['演示', '环境音乐', '电子'] : ['Demo', 'Ambient', 'Electronic'],
              rating: 6.8,
              ratingCount: 123,
              trialDuration: '25:0k',
              trialCount: 234,
              price: 0,
              currency: '',
              coverUrl: 'https://images.unsplash.com/photo-1511735111819-9a3f7709049c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
              participants: [
                { role: language === 'zh' ? '制作人' : 'Producer', name: language === 'zh' ? '黄亚历克斯' : 'Alex Wong' },
                { role: language === 'zh' ? '音效设计师' : 'Sound Designer', name: language === 'zh' ? '杨丽莎' : 'Lisa Yang' }
              ]
            }
          ],
          relatedMusicians: [
            {
              id: 2,
              name: t('musician.detail.related.musician.1'),
              avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
              genre: t('musician.detail.genre'),
              followers: 15400
            },
            {
              id: 3,
              name: t('musician.detail.related.musician.2'),
              avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
              genre: t('musician.detail.genre'),
              followers: 25600
            },
            {
              id: 4,
              name: t('musician.detail.related.musician.3'),
              avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
              genre: t('musician.detail.genre'),
              followers: 32100
            }
          ]
        });
      } catch (error) {
        console.error('Failed to fetch musician detail:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMusicianDetail();
  }, [id, language]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!musician) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{t('musician.detail.not.found')}</h2>
          <button
            onClick={() => navigate('/musicians')}
            className="text-blue-600 hover:text-blue-800"
          >
{t('musician.detail.back.to.list')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative h-[500px] bg-gradient-to-b from-transparent to-black">
        <img 
          src={musician.coverImage} 
          alt={musician.name}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/50 to-black/80"></div>
        
        {/* Back Button */}
        <button
          onClick={() => navigate('/musicians')}
          className="absolute top-8 left-8 bg-black bg-opacity-50 backdrop-blur-sm text-white p-3 rounded-full hover:bg-opacity-70 transition-all"
        >
          <ArrowLeftIcon className="h-6 w-6" />
        </button>

        {/* Musician Info */}
        <div className="absolute bottom-0 left-0 right-0 p-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-end space-x-6">
              <img 
                src={musician.avatar}
                alt={musician.name}
                className="w-32 h-32 rounded-full border-4 border-white shadow-lg"
              />
              <div className="flex-1 text-white">
                <div className="flex items-center space-x-3 mb-2">
                  <h1 className="text-5xl font-bold">{musician.name}</h1>
                  {musician.verified && (
                    <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
{t('musician.detail.verified')}
                    </div>
                  )}
                  <div className="flex items-center space-x-1 bg-black bg-opacity-50 backdrop-blur-sm rounded-full px-3 py-1">
                    <StarIconSolid className="h-5 w-5 text-yellow-400" />
                    <span className="text-white font-medium">{musician.rating}</span>
                  </div>
                </div>
                <p className="text-xl text-gray-200 mb-4">{musician.location}</p>
                <div className="flex items-center space-x-8 text-sm">
                  <div>
                    <span className="text-2xl font-bold">{musician.followers}M</span>
                    <span className="text-gray-300 ml-1">{t('musician.detail.followers')}</span>
                  </div>
                  <div>
                    <span className="text-2xl font-bold">{musician.following}</span>
                    <span className="text-gray-300 ml-1">{t('musician.detail.following')}</span>
                  </div>
                  <div>
                    <span className="text-2xl font-bold">{musician.plays}</span>
                    <span className="text-gray-300 ml-1">{t('musician.detail.works')}</span>
                  </div>
                </div>
              </div>
              
              {/* Action Buttons */}
              <div className="flex space-x-4">
                <button 
                  onClick={() => setIsFollowing(!isFollowing)}
                  className={`flex items-center space-x-2 px-8 py-3 rounded-full font-medium transition-all ${
                    isFollowing 
                      ? 'bg-gray-600 text-white hover:bg-gray-700' 
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  <UserPlusIcon className="h-5 w-5" />
                  <span>{isFollowing ? t('musician.detail.followed') : t('musician.detail.follow')}</span>
                </button>
                <button className="flex items-center space-x-2 bg-white bg-opacity-20 backdrop-blur-sm text-white px-6 py-3 rounded-full hover:bg-opacity-30 transition-all">
                  <ChatBubbleLeftIcon className="h-5 w-5" />
                  <span>{t('musician.detail.message')}</span>
                </button>
                <button className="flex items-center space-x-2 bg-white bg-opacity-20 backdrop-blur-sm text-white px-6 py-3 rounded-full hover:bg-opacity-30 transition-all">
                  <ShareIcon className="h-5 w-5" />
                  <span>{t('musician.detail.share')}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Tabs */}
            <div className="mb-8">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  {[
                    { key: 'works', label: language === 'zh' ? '作品' : 'Works', icon: MusicalNoteIcon },
                    { key: 'demos', label: language === 'zh' ? '演示' : 'Demos', icon: PlayIcon }
                  ].map(({ key, label, icon: Icon }) => (
                    <button
                      key={key}
                      onClick={() => setActiveTab(key as any)}
                      className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === key
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span>{label}</span>
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === 'works' && (
              <div className="bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100">
                {/* Table Header */}
                <div className="bg-gray-50 px-6 py-4">
                  <div className="grid grid-cols-11 gap-4 text-sm font-medium text-gray-700">
                    <div className="col-span-3">{language === 'zh' ? '歌曲作品' : 'Songs'}</div>
                    <div className="col-span-2">{language === 'zh' ? '标签' : 'Tags'}</div>
                    <div className="col-span-1">{language === 'zh' ? '评分' : 'Rating'}</div>
                    <div className="col-span-1">{language === 'zh' ? '试唱' : 'Audition'}</div>
                    <div className="col-span-1">{language === 'zh' ? '意向价格' : 'Price'}</div>
                    <div className="col-span-3">{language === 'zh' ? '操作' : 'Actions'}</div>
                  </div>
                </div>
                
                {/* Works List */}
                <div className="divide-y divide-gray-200">
                  {musician.works.map((work) => (
                    <div key={work.id} className="px-6 py-6 hover:bg-gray-50 transition-colors border-b border-gray-100">
                      <div className="grid grid-cols-11 gap-4 items-center">
                        {/* Song Info */}
                        <div className="col-span-3 flex items-center space-x-3">
                          <div className="relative flex-shrink-0">
                            <img 
                              src={work.coverUrl}
                              alt={work.title}
                              className="w-16 h-16 rounded-lg object-cover shadow-md"
                              onError={(e) => {
                                e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAyNkg0MFYzOEgyNFYyNloiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                              }}
                            />
                            <button 
                              className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg opacity-0 hover:opacity-100 transition-opacity"
                              onClick={() => alert(`Playing ${work.title}`)}
                            >
                              <PlayIcon className="h-6 w-6 text-white" />
                            </button>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer text-lg truncate">{work.title}</h4>
                            <p className="text-sm text-gray-500 truncate">{language === 'zh' ? '词作者：' : 'Lyricist: '}{work.lyricist}</p>
                            <p className="text-sm text-gray-500 truncate">{language === 'zh' ? '曲作者：' : 'Composer: '}{work.composer}</p>
                            <div className="flex space-x-2 mt-2 flex-wrap">
                              <button 
                                className="px-3 py-1 bg-blue-500 text-white text-xs rounded-full hover:bg-blue-600 transition-colors"
                                onClick={() => alert(language === 'zh' ? `播放完整版 ${work.title}` : `Playing full version of ${work.title}`)}
                              >
                                {language === 'zh' ? '听完整版' : 'Full Play'}
                              </button>
                              <button 
                                className="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded-full hover:bg-purple-200 transition-colors"
                                onClick={() => alert(language === 'zh' ? `播放副歌 ${work.title}` : `Playing chorus of ${work.title}`)}
                              >
                                {language === 'zh' ? '听副歌' : 'Chorus'}
                              </button>
                              <button 
                                className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full hover:bg-green-200 transition-colors"
                                onClick={() => alert(language === 'zh' ? `查看歌词 ${work.title}` : `Showing lyrics of ${work.title}`)}
                              >
                                {language === 'zh' ? '有歌词' : 'Lyrics'}
                              </button>
                            </div>
                          </div>
                        </div>
                        
                        {/* Tags */}
                        <div className="col-span-2">
                          <div className="flex flex-wrap gap-1">
                            {work.tags.slice(0, 3).map((tag, index) => (
                              <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                                {tag}
                              </span>
                            ))}
                            {work.tags.length > 3 && (
                              <span className="text-xs text-gray-400">+{work.tags.length - 3}</span>
                            )}
                          </div>
                        </div>
                        
                        {/* Rating */}
                        <div className="col-span-1">
                          <div className="flex items-center space-x-1 mb-1">
                            <StarIcon className="h-4 w-4 text-yellow-400" />
                            <span className="text-sm font-medium">{work.rating}</span>
                          </div>
                          <div className="flex items-center space-x-1 text-xs text-gray-500 mb-1">
                            <HeartIcon className="h-3 w-3" />
                            <span>{work.ratingCount}</span>
                          </div>
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <PlayIcon className="h-3 w-3" />
                            <span>{work.trialDuration}</span>
                          </div>
                        </div>
                        
                        {/* Audition */}
                        <div className="col-span-1">
                          <button 
                            className="px-3 py-1 bg-orange-100 text-orange-600 text-xs rounded-full hover:bg-orange-200 transition-colors"
                            onClick={() => navigate(`/karaoke/${work.id}`)}
                          >
                            {language === 'zh' ? '试唱' : 'Tryout'}
                          </button>
                        </div>
                        
                        {/* Price */}
                        <div className="col-span-1">
                          {work.price === 0 ? (
                            <span className="text-sm text-green-600 font-medium">{language === 'zh' ? '可协商报价' : 'Negotiable'}</span>
                          ) : (
                            <span className="text-sm font-medium">{work.currency} {work.price}</span>
                          )}
                        </div>
                        
                        {/* Actions */}
                        <div className="col-span-3 flex space-x-2">
                          <button 
                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded-full hover:bg-blue-700 transition-colors flex items-center space-x-1"
                            onClick={() => alert(language === 'zh' ? `购买 ${work.title} 价格 ${work.currency}${work.price}` : `Buying ${work.title} for ${work.currency}${work.price}`)}
                          >
                            <ShoppingCartIcon className="h-3 w-3" />
                            <span>{language === 'zh' ? '购买' : 'Buy'}</span>
                          </button>
                          <button 
                            className="px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full hover:bg-gray-200 transition-colors flex items-center space-x-1"
                            onClick={() => alert(language === 'zh' ? `收藏了 ${work.title}` : `Collected ${work.title}`)}
                          >
                            <BookmarkIcon className="h-3 w-3" />
                            <span>{language === 'zh' ? '收藏' : 'Collect'}</span>
                          </button>
                          <button 
                            className="px-3 py-1 bg-red-100 text-red-600 text-sm rounded-full hover:bg-red-200 transition-colors flex items-center space-x-1"
                            onClick={() => alert(language === 'zh' ? `点赞了 ${work.title}` : `Liked ${work.title}`)}
                          >
                            <HeartIcon className="h-3 w-3" />
                            <span>{language === 'zh' ? '点赞' : 'Like'}</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'demos' && (
              <div className="bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100">
                {/* Table Header */}
                <div className="bg-gray-50 px-6 py-4">
                  <div className="grid grid-cols-11 gap-4 text-sm font-medium text-gray-700">
                    <div className="col-span-3">{language === 'zh' ? '歌曲作品' : 'Songs'}</div>
                    <div className="col-span-2">{language === 'zh' ? '标签' : 'Tags'}</div>
                    <div className="col-span-1">{language === 'zh' ? '评分' : 'Rating'}</div>
                    <div className="col-span-1">{language === 'zh' ? '试唱' : 'Audition'}</div>
                    <div className="col-span-1">{language === 'zh' ? '意向价格' : 'Price'}</div>
                    <div className="col-span-3">{language === 'zh' ? '操作' : 'Actions'}</div>
                  </div>
                </div>
                
                {/* Demos List */}
                <div className="divide-y divide-gray-200">
                  {musician.demos.map((demo) => (
                    <div key={demo.id} className="px-6 py-6 hover:bg-gray-50 transition-colors border-b border-gray-100">
                      <div className="grid grid-cols-11 gap-4 items-center">
                        {/* Song Info - No buttons for demos */}
                        <div className="col-span-3 flex items-center space-x-3">
                          <div className="relative flex-shrink-0">
                            <img 
                              src={demo.coverUrl}
                              alt={demo.title}
                              className="w-16 h-16 rounded-lg object-cover shadow-md"
                              onError={(e) => {
                                e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAyNkg0MFYzOEgyNFYyNloiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                              }}
                            />
                            <button 
                              className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg opacity-0 hover:opacity-100 transition-opacity"
                              onClick={() => alert(`Playing demo ${demo.title}`)}
                            >
                              <PlayIcon className="h-6 w-6 text-white" />
                            </button>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer text-lg truncate">{demo.title}</h4>
                            <p className="text-sm text-gray-500 truncate">{language === 'zh' ? '词作者：' : 'Lyricist: '}{demo.lyricist}</p>
                            <p className="text-sm text-gray-500 truncate">{language === 'zh' ? '曲作者：' : 'Composer: '}{demo.composer}</p>
                            <div className="mt-2">
                              <span className="px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded-full">{language === 'zh' ? 'Demo版本' : 'Demo Version'}</span>
                            </div>
                          </div>
                        </div>
                        
                        {/* Tags */}
                        <div className="col-span-2">
                          <div className="flex flex-wrap gap-1">
                            {demo.tags.slice(0, 3).map((tag, index) => (
                              <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                                {tag}
                              </span>
                            ))}
                            {demo.tags.length > 3 && (
                              <span className="text-xs text-gray-400">+{demo.tags.length - 3}</span>
                            )}
                          </div>
                        </div>
                        
                        {/* Rating */}
                        <div className="col-span-1">
                          <div className="flex items-center space-x-1 mb-1">
                            <StarIcon className="h-4 w-4 text-yellow-400" />
                            <span className="text-sm font-medium">{demo.rating}</span>
                          </div>
                          <div className="flex items-center space-x-1 text-xs text-gray-500 mb-1">
                            <HeartIcon className="h-3 w-3" />
                            <span>{demo.ratingCount}</span>
                          </div>
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <PlayIcon className="h-3 w-3" />
                            <span>{demo.trialDuration}</span>
                          </div>
                        </div>
                        
                        {/* Audition */}
                        <div className="col-span-1">
                          <button 
                            className="px-3 py-1 bg-orange-100 text-orange-600 text-xs rounded-full hover:bg-orange-200 transition-colors"
                            onClick={() => navigate(`/karaoke/${demo.id}`)}
                          >
                            {language === 'zh' ? '试唱' : 'Tryout'}
                          </button>
                        </div>
                        
                        {/* Price */}
                        <div className="col-span-1">
                          <span className="text-sm text-green-600 font-medium">{language === 'zh' ? 'Demo作品' : 'Demo'}</span>
                        </div>
                        
                        {/* Actions */}
                        <div className="col-span-3 flex space-x-2">
                          <button 
                            className="px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full hover:bg-gray-200 transition-colors flex items-center space-x-1"
                            onClick={() => alert(language === 'zh' ? `收藏了演示 ${demo.title}` : `Collected demo ${demo.title}`)}
                          >
                            <BookmarkIcon className="h-3 w-3" />
                            <span>{language === 'zh' ? '收藏' : 'Collect'}</span>
                          </button>
                          <button 
                            className="px-3 py-1 bg-red-100 text-red-600 text-sm rounded-full hover:bg-red-200 transition-colors flex items-center space-x-1"
                            onClick={() => alert(language === 'zh' ? `点赞了演示 ${demo.title}` : `Liked demo ${demo.title}`)}
                          >
                            <HeartIcon className="h-3 w-3" />
                            <span>{language === 'zh' ? '点赞' : 'Like'}</span>
                          </button>
                          <button 
                            className="px-3 py-1 bg-blue-100 text-blue-600 text-sm rounded-full hover:bg-blue-200 transition-colors"
                            onClick={() => alert(language === 'zh' ? `联系音乐人关于 ${demo.title}` : `Contacting artist about ${demo.title}`)}
                          >
                            {language === 'zh' ? '联系' : 'Contact'}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* About */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 mb-4">{t('musician.detail.about.artist')}</h3>
              <p className="text-gray-600 leading-relaxed mb-4">{musician.description}</p>
              
              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-4">
                {musician.tags.map((tag, index) => (
                  <span 
                    key={index}
                    className="px-3 py-1 bg-blue-50 text-blue-600 text-sm rounded-full font-medium"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            {/* Recent Works */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 mb-4">{t('musician.detail.recent.works')}</h3>
              <div className="space-y-4">
                {musician.songs.slice(0, 3).map((song) => (
                  <div key={song.id} className="flex items-center space-x-3">
                    <img 
                      src={song.coverUrl}
                      alt={song.title}
                      className="w-12 h-12 rounded object-cover"
                    />
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">{song.title}</h4>
                      <p className="text-xs text-gray-500">{(song.plays / 1000).toFixed(1)}k {t('musician.detail.plays')}</p>
                    </div>
                    <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                      <PlayIcon className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Recommended Musicians */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 mb-4">{t('musician.detail.you.might.like')}</h3>
              <div className="space-y-4">
                {musician.relatedMusicians.slice(0, 3).map((relatedMusician) => (
                  <div key={relatedMusician.id} className="flex items-center space-x-3">
                    <img 
                      src={relatedMusician.avatar}
                      alt={relatedMusician.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900">{relatedMusician.name}</h4>
                      <p className="text-xs text-gray-500">{(relatedMusician.followers / 1000).toFixed(1)}k {t('musician.detail.followers')}</p>
                    </div>
                    <button className="text-blue-600 text-sm hover:text-blue-800 transition-colors">
                      {t('musicians.follow')}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MusicianDetail;