import React, { useState } from 'react';
import { 
  ArrowLeftIcon, 
  CurrencyDollarIcon,
  QrCodeIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface PurchaseCoinsProps {
  onBack: () => void;
}

const PurchaseCoins: React.FC<PurchaseCoinsProps> = ({ onBack }) => {
  const [selectedPackage, setSelectedPackage] = useState(68);
  const [selectedPayment, setSelectedPayment] = useState('wechat');
  const [customAmount, setCustomAmount] = useState('');
  const [showQRCode, setShowQRCode] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  const packages = [
    { coins: 6, price: 0.60, bonus: 0, popular: false },
    { coins: 16, price: 1.60, bonus: 0, popular: false },
    { coins: 68, price: 6.80, bonus: 2, popular: true },
    { coins: 233, price: 23.30, bonus: 17, popular: false },
    { coins: 648, price: 64.80, bonus: 52, popular: false },
    { coins: 998, price: 99.80, bonus: 102, popular: false }
  ];

  const paymentMethods = [
    { 
      id: 'wechat', 
      name: '微信支付', 
      icon: '微',
      color: 'bg-green-500',
      description: '推荐使用微信支付'
    },
    { 
      id: 'alipay', 
      name: '支付宝', 
      icon: '支',
      color: 'bg-blue-500',
      description: '支持花呗分期'
    },
    { 
      id: 'bank', 
      name: '银行卡', 
      icon: '银',
      color: 'bg-gray-500',
      description: '支持各大银行卡'
    }
  ];

  const handlePurchase = () => {
    setShowQRCode(true);
    // 模拟支付成功
    setTimeout(() => {
      setPaymentSuccess(true);
      setTimeout(() => {
        onBack();
      }, 2000);
    }, 3000);
  };

  const getSelectedPackage = () => {
    if (customAmount) {
      const amount = parseFloat(customAmount);
      return {
        coins: Math.floor(amount * 10),
        price: amount,
        bonus: amount > 50 ? Math.floor(amount * 0.1) : 0,
        popular: false
      };
    }
    return packages.find(pkg => pkg.coins === selectedPackage) || packages[2];
  };

  const selectedPkg = getSelectedPackage();

  if (paymentSuccess) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">支付成功！</h2>
          <p className="text-gray-600 mb-4">
            您已成功购买 {selectedPkg.coins + selectedPkg.bonus} 金币
          </p>
          <div className="bg-green-50 rounded-lg p-4 mb-6">
            <p className="text-green-800">
              金币已到账，您可以在钱包中查看余额
            </p>
          </div>
          <p className="text-sm text-gray-500">页面将自动返回...</p>
        </div>
      </div>
    );
  }

  if (showQRCode) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="flex items-center mb-6">
            <button
              onClick={() => setShowQRCode(false)}
              className="flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              返回
            </button>
            <h2 className="text-xl font-bold text-gray-900 ml-4">扫码支付</h2>
          </div>
          
          <div className="text-center">
            <div className="bg-gray-50 rounded-lg p-8 mb-6">
              <QrCodeIcon className="h-32 w-32 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 mb-2">请使用{paymentMethods.find(m => m.id === selectedPayment)?.name}扫码支付</p>
              <div className="text-2xl font-bold text-indigo-600">
                ¥{selectedPkg.price.toFixed(2)}
              </div>
            </div>
            
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">购买金币：</span>
                <span className="font-medium">{selectedPkg.coins}</span>
              </div>
              {selectedPkg.bonus > 0 && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">赠送金币：</span>
                  <span className="font-medium text-green-600">+{selectedPkg.bonus}</span>
                </div>
              )}
              <div className="flex items-center justify-between text-sm border-t pt-2 mt-2">
                <span className="text-gray-600">总计：</span>
                <span className="font-bold text-indigo-600">{selectedPkg.coins + selectedPkg.bonus} 金币</span>
              </div>
            </div>
            
            <div className="text-sm text-gray-500 space-y-1">
              <p>• 支付完成后金币将自动到账</p>
              <p>• 如有问题请联系客服</p>
              <p>• 支付时限：10分钟</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center mb-6">
          <button
            onClick={onBack}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            返回钱包
          </button>
          <h2 className="text-xl font-bold text-gray-900 ml-4">购买金币</h2>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：选择数量 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">选择购买数量</h3>
            
            {/* 预设套餐 */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              {packages.map((pkg) => (
                <button
                  key={pkg.coins}
                  onClick={() => {
                    setSelectedPackage(pkg.coins);
                    setCustomAmount('');
                  }}
                  className={`relative p-4 rounded-lg border-2 transition-all ${
                    selectedPackage === pkg.coins && !customAmount
                      ? 'border-indigo-500 bg-indigo-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {pkg.popular && (
                    <div className="absolute -top-2 -right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs">
                      推荐
                    </div>
                  )}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{pkg.coins}</div>
                    <div className="text-sm text-gray-600">金币</div>
                    {pkg.bonus > 0 && (
                      <div className="text-xs text-green-600 mt-1">+{pkg.bonus}赠送</div>
                    )}
                    <div className="text-sm font-medium text-indigo-600 mt-2">
                      ¥{pkg.price.toFixed(2)}
                    </div>
                  </div>
                </button>
              ))}
            </div>
            
            {/* 自定义数量 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                自定义金额
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">¥</span>
                </div>
                <input
                  type="number"
                  placeholder="输入金额"
                  value={customAmount}
                  onChange={(e) => {
                    setCustomAmount(e.target.value);
                    setSelectedPackage(0);
                  }}
                  className="block w-full pl-7 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  min="0.1"
                  step="0.1"
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                最低充值0.1元，满50元赠送10%金币
              </p>
            </div>
            
            {/* 支付方式 */}
            <div>
              <h3 className="text-lg font-semibold mb-4">选择支付方式</h3>
              <div className="space-y-3">
                {paymentMethods.map((method) => (
                  <button
                    key={method.id}
                    onClick={() => setSelectedPayment(method.id)}
                    className={`w-full flex items-center p-4 rounded-lg border-2 transition-all ${
                      selectedPayment === method.id
                        ? 'border-indigo-500 bg-indigo-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className={`w-10 h-10 ${method.color} rounded-full flex items-center justify-center text-white font-bold mr-4`}>
                      {method.icon}
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-medium text-gray-900">{method.name}</div>
                      <div className="text-sm text-gray-600">{method.description}</div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
          
          {/* 右侧：订单详情 */}
          <div>
            <div className="bg-gray-50 rounded-lg p-6 sticky top-6">
              <h3 className="text-lg font-semibold mb-4">订单详情</h3>
              
              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">购买金币</span>
                  <span className="font-medium">{selectedPkg.coins}</span>
                </div>
                
                {selectedPkg.bonus > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">赠送金币</span>
                    <span className="font-medium text-green-600">+{selectedPkg.bonus}</span>
                  </div>
                )}
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">支付方式</span>
                  <span className="font-medium">
                    {paymentMethods.find(m => m.id === selectedPayment)?.name}
                  </span>
                </div>
                
                <div className="border-t pt-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">支付金额</span>
                    <span className="text-2xl font-bold text-indigo-600">
                      ¥{selectedPkg.price.toFixed(2)}
                    </span>
                  </div>
                </div>
                
                <div className="bg-blue-50 rounded-lg p-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">实际到账</span>
                    <span className="font-bold text-indigo-600">
                      {selectedPkg.coins + selectedPkg.bonus} 金币
                    </span>
                  </div>
                </div>
              </div>
              
              <button
                onClick={handlePurchase}
                disabled={!selectedPkg.price || selectedPkg.price <= 0}
                className="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <div className="flex items-center justify-center">
                  <CurrencyDollarIcon className="h-5 w-5 mr-2" />
                  立即支付
                </div>
              </button>
              
              <div className="mt-4 text-xs text-gray-500 space-y-1">
                <p>• 支付完成后金币将立即到账</p>
                <p>• 如遇问题请联系客服</p>
                <p>• 充值金币不支持退款</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PurchaseCoins;