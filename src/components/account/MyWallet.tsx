import React, { useState, useMemo } from 'react';
import { 
  Cur<PERSON>cyDollarIcon, 
  ArrowUpIcon, 
  ArrowDownIcon,
  ClockIcon,
  ShoppingCartIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

interface MyWalletProps {
  onPurchase: () => void;
}

const MyWallet: React.FC<MyWalletProps> = ({ onPurchase }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const { t } = useLanguage();

  const walletData = {
    goldCoins: 368,
    crystalCoins: 45,
    totalSpent: 1250.50,
    totalEarned: 320.00
  };

  const transactions = useMemo(() => [
    {
      id: 1,
      type: 'purchase',
      description: t('wallet.transaction.purchase.gold'),
      amount: 100,
      currency: 'gold',
      status: 'completed',
      time: '2024-01-20 15:30:00'
    },
    {
      id: 2,
      type: 'spend',
      description: t('wallet.transaction.purchase.vip'),
      amount: -50,
      currency: 'gold',
      status: 'completed',
      time: '2024-01-19 10:15:00'
    },
    {
      id: 3,
      type: 'earn',
      description: t('wallet.transaction.reward.income'),
      amount: 20,
      currency: 'crystal',
      status: 'completed',
      time: '2024-01-18 16:45:00'
    },
    {
      id: 4,
      type: 'withdraw',
      description: t('wallet.transaction.crystal.withdraw'),
      amount: -10,
      currency: 'crystal',
      status: 'pending',
      time: '2024-01-17 14:20:00'
    },
    {
      id: 5,
      type: 'purchase',
      description: t('wallet.transaction.purchase.crystal'),
      amount: 50,
      currency: 'crystal',
      status: 'completed',
      time: '2024-01-16 09:30:00'
    }
  ], [t]);

  const purchaseHistory = useMemo(() => [
    {
      id: 1,
      amount: 100,
      currency: 'gold',
      price: 10.00,
      method: 'wechat',
      time: '2024-01-20 15:30:00',
      status: 'completed'
    },
    {
      id: 2,
      amount: 50,
      currency: 'crystal',
      price: 25.00,
      method: 'alipay',
      time: '2024-01-16 09:30:00',
      status: 'completed'
    },
    {
      id: 3,
      amount: 200,
      currency: 'gold',
      price: 18.00,
      method: 'wechat',
      time: '2024-01-10 12:15:00',
      status: 'completed'
    }
  ], [t]);

  const renderOverview = () => (
    <div className="space-y-6">
      {/* 余额显示 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gradient-to-r from-yellow-500 to-orange-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-100">{t('wallet.gold.balance')}</p>
              <p className="text-3xl font-bold">{walletData.goldCoins}</p>
              <p className="text-yellow-100 text-sm mt-1">{t('wallet.gold.description')}</p>
            </div>
            <div className="w-12 h-12 bg-yellow-400 rounded-full flex items-center justify-center">
              <span className="text-yellow-800 font-bold text-lg">¥</span>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100">{t('wallet.crystal.balance')}</p>
              <p className="text-3xl font-bold">{walletData.crystalCoins}</p>
              <p className="text-purple-100 text-sm mt-1">{t('wallet.crystal.description')}</p>
            </div>
            <div className="w-12 h-12 bg-purple-400 rounded-full flex items-center justify-center">
              <span className="text-purple-800 font-bold text-lg">◆</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* 操作按钮 */}
      <div className="flex flex-wrap gap-4">
        <button
          onClick={onPurchase}
          className="flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          <ShoppingCartIcon className="h-5 w-5" />
          <span>{t('wallet.purchase.gold')}</span>
        </button>
        
        <button className="flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
          <BanknotesIcon className="h-5 w-5" />
          <span>{t('wallet.crystal.withdraw')}</span>
        </button>
        
        <button className="flex items-center space-x-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
          <ClockIcon className="h-5 w-5" />
          <span>{t('wallet.transactions')}</span>
        </button>
      </div>
      
      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold mb-4">{t('wallet.spending.statistics')}</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">{t('wallet.monthly.spending')}</span>
              <span className="text-red-600 font-medium">¥{walletData.totalSpent.toFixed(2)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">{t('wallet.monthly.income')}</span>
              <span className="text-green-600 font-medium">¥{walletData.totalEarned.toFixed(2)}</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold mb-4">{t('wallet.usage.guide')}</h3>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• {t('wallet.usage.gold.description')}</p>
            <p>• {t('wallet.usage.crystal.earn')}</p>
            <p>• {t('wallet.usage.crystal.withdraw')}</p>
            <p>• {t('wallet.usage.transaction.view')}</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTransactions = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{t('wallet.transaction.details')}</h3>
        <div className="flex items-center space-x-2">
          <select className="px-3 py-2 border border-gray-300 rounded-lg text-sm">
            <option>{t('wallet.filter.all.types')}</option>
            <option>{t('wallet.type.purchase')}</option>
            <option>{t('wallet.type.spend')}</option>
            <option>{t('wallet.type.earn')}</option>
            <option>{t('wallet.type.withdraw')}</option>
          </select>
          <select className="px-3 py-2 border border-gray-300 rounded-lg text-sm">
            <option>{t('wallet.filter.all.currencies')}</option>
            <option>{t('wallet.currency.gold')}</option>
            <option>{t('wallet.currency.crystal')}</option>
          </select>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.type')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.description')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.amount')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.status')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.time')}</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {transactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {transaction.type === 'purchase' && (
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <ArrowDownIcon className="h-4 w-4 text-blue-600" />
                        </div>
                      )}
                      {transaction.type === 'spend' && (
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                          <ArrowUpIcon className="h-4 w-4 text-red-600" />
                        </div>
                      )}
                      {transaction.type === 'earn' && (
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <ArrowDownIcon className="h-4 w-4 text-green-600" />
                        </div>
                      )}
                      {transaction.type === 'withdraw' && (
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                          <ArrowUpIcon className="h-4 w-4 text-purple-600" />
                        </div>
                      )}
                      <span className="ml-3 text-sm font-medium">
                        {transaction.type === 'purchase' ? t('wallet.type.purchase') :
                         transaction.type === 'spend' ? t('wallet.type.spend') :
                         transaction.type === 'earn' ? t('wallet.type.earn') : t('wallet.type.withdraw')}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.description}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`font-medium ${
                      transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                    </span>
                    <span className="text-gray-500 ml-1">
                      {transaction.currency === 'gold' ? t('wallet.currency.gold') : t('wallet.currency.crystal')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      transaction.status === 'completed' 
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {transaction.status === 'completed' ? t('wallet.status.completed') : t('wallet.status.pending')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {transaction.time}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderPurchaseHistory = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">{t('wallet.purchase.history')}</h3>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.amount')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.type')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.price')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.payment.method')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.status')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('wallet.table.time')}</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {purchaseHistory.map((purchase) => (
                <tr key={purchase.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {purchase.amount}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {purchase.currency === 'gold' ? t('wallet.currency.gold') : t('wallet.currency.crystal')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ¥{purchase.price.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <div className={`w-6 h-6 rounded-full mr-2 ${
                        purchase.method === 'wechat' ? 'bg-green-500' : 'bg-blue-500'
                      }`}></div>
                      {purchase.method === 'wechat' ? t('wallet.payment.wechat') : t('wallet.payment.alipay')}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                      {t('wallet.status.payment.success')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {purchase.time}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* 标签导航 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              activeTab === 'overview'
                ? 'bg-indigo-50 text-indigo-700'
                : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            <CurrencyDollarIcon className={`h-5 w-5 ${
              activeTab === 'overview' ? 'text-indigo-500' : 'text-gray-400'
            }`} />
            <span className="font-medium">{t('wallet.tab.balance')}</span>
          </button>
          
          <button
            onClick={() => setActiveTab('transactions')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              activeTab === 'transactions'
                ? 'bg-indigo-50 text-indigo-700'
                : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            <ClockIcon className={`h-5 w-5 ${
              activeTab === 'transactions' ? 'text-indigo-500' : 'text-gray-400'
            }`} />
            <span className="font-medium">{t('wallet.tab.transactions')}</span>
          </button>
          
          <button
            onClick={() => setActiveTab('purchase')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              activeTab === 'purchase'
                ? 'bg-indigo-50 text-indigo-700'
                : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            <ShoppingCartIcon className={`h-5 w-5 ${
              activeTab === 'purchase' ? 'text-indigo-500' : 'text-gray-400'
            }`} />
            <span className="font-medium">{t('wallet.tab.purchase.history')}</span>
          </button>
        </div>
      </div>
      
      {/* 内容区域 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'transactions' && renderTransactions()}
        {activeTab === 'purchase' && renderPurchaseHistory()}
      </div>
    </div>
  );
};

export default MyWallet;