import React, { useState, useEffect } from 'react';
import { MicrophoneIcon, AcademicCapIcon, ChatBubbleLeftIcon, ChartBarIcon } from '@heroicons/react/24/outline';

const MusicTutor: React.FC = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [pitchAccuracy, setPitchAccuracy] = useState(0);
  const [rhythmStability, setRhythmStability] = useState(0);
  const [recordingTime, setRecordingTime] = useState(0);
  const [aiResponse, setAiResponse] = useState('');
  const [userQuestion, setUserQuestion] = useState('');

  // Simulate real-time analysis data
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingTime(prev => prev + 1);
        setPitchAccuracy(Math.random() * 100);
        setRhythmStability(Math.random() * 100);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRecording]);

  const handleStartRecording = () => {
    setIsRecording(true);
    setRecordingTime(0);
  };

  const handleStopRecording = () => {
    setIsRecording(false);
    // Generate AI feedback after stopping
    setTimeout(() => {
      setAiResponse('分析完成：您的音准表现良好，平均准确度为 85%。建议在高音部分多加练习，注意气息控制。节奏方面整体稳定，建议使用节拍器进行进一步训练。');
    }, 1000);
  };

  const handleAskQuestion = () => {
    if (!userQuestion.trim()) return;
    
    // Simulate AI response
    setTimeout(() => {
      setAiResponse(`针对您的问题"${userQuestion}"，建议您从以下几个方面入手：1. 多听多练 2. 注意基础技巧 3. 循序渐进地提高难度。具体的练习方法我可以为您推荐一些适合的课程。`);
      setUserQuestion('');
    }, 1000);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Mock heatmap data
  const problemAreas = [
    { area: '高音区', intensity: 80, color: 'bg-red-400' },
    { area: '节拍把握', intensity: 60, color: 'bg-yellow-400' },
    { area: '音色控制', intensity: 40, color: 'bg-green-400' },
    { area: '呼吸技巧', intensity: 70, color: 'bg-orange-400' },
    { area: '情感表达', intensity: 30, color: 'bg-blue-400' },
  ];

  const recommendedCourses = [
    { title: '声乐基础训练', level: '初级', duration: '30分钟', rating: 4.8 },
    { title: '音准矫正专项', level: '中级', duration: '45分钟', rating: 4.9 },
    { title: '节奏感训练', level: '中级', duration: '25分钟', rating: 4.7 },
    { title: '高音技巧突破', level: '高级', duration: '60分钟', rating: 4.9 },
  ];

  return (
    <div className="flex gap-6 h-full">
      {/* Left Panel - Performance Analysis */}
      <div className="flex-1 space-y-6">
        {/* Recording Control */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-3 mb-4">
            <MicrophoneIcon className="w-6 h-6 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">实时演奏分析</h3>
          </div>
          
          <div className="text-center">
            <div className="mb-4">
              <div className="text-3xl font-mono text-gray-700 mb-2">
                {formatTime(recordingTime)}
              </div>
              <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
                isRecording ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-700'
              }`}>
                <div className={`w-2 h-2 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-400'}`}></div>
                {isRecording ? '录制中' : '待录制'}
              </div>
            </div>
            
            <button
              onClick={isRecording ? handleStopRecording : handleStartRecording}
              className={`px-8 py-3 rounded-lg font-medium transition-colors ${
                isRecording
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isRecording ? '停止录制' : '开始录制'}
            </button>
          </div>
        </div>

        {/* Analysis Results */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">分析结果</h3>
          
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">音准分析</span>
                <span className="text-sm text-gray-600">{Math.round(pitchAccuracy)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${pitchAccuracy}%` }}
                ></div>
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">节奏稳定性</span>
                <span className="text-sm text-gray-600">{Math.round(rhythmStability)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${rhythmStability}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Problem Heatmap */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-3 mb-4">
            <ChartBarIcon className="w-6 h-6 text-orange-600" />
            <h3 className="text-lg font-semibold text-gray-900">问题热力图</h3>
          </div>
          
          <div className="space-y-3">
            {problemAreas.map((area, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="w-20 text-sm text-gray-700">{area.area}</div>
                <div className="flex-1 bg-gray-200 rounded-full h-6 relative overflow-hidden">
                  <div 
                    className={`${area.color} h-full transition-all duration-500 flex items-center justify-end pr-2`}
                    style={{ width: `${area.intensity}%` }}
                  >
                    <span className="text-xs text-white font-medium">{area.intensity}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right Panel - AI Feedback and Courses */}
      <div className="w-96 space-y-6">
        {/* AI Feedback */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-3 mb-4">
            <ChatBubbleLeftIcon className="w-6 h-6 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">AI反馈</h3>
          </div>
          
          {aiResponse ? (
            <div className="bg-green-50 rounded-lg p-4 text-sm text-green-800 mb-4">
              {aiResponse}
            </div>
          ) : (
            <div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-600 mb-4">
              开始录制后，AI将为您提供个性化的学习建议
            </div>
          )}
        </div>

        {/* Smart Q&A */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">智能问答</h3>
          
          <div className="space-y-3">
            <textarea
              value={userQuestion}
              onChange={(e) => setUserQuestion(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none text-sm"
              placeholder="向AI导师提问，比如：如何提高高音技巧？"
            />
            <button
              onClick={handleAskQuestion}
              disabled={!userQuestion.trim()}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
            >
              提问
            </button>
          </div>
        </div>

        {/* Recommended Courses */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-3 mb-4">
            <AcademicCapIcon className="w-6 h-6 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">推荐课程</h3>
          </div>
          
          <div className="space-y-3">
            {recommendedCourses.map((course, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors cursor-pointer">
                <div className="flex justify-between items-start mb-1">
                  <h4 className="text-sm font-medium text-gray-900">{course.title}</h4>
                  <span className="text-xs text-yellow-600">★ {course.rating}</span>
                </div>
                <div className="flex justify-between items-center text-xs text-gray-600">
                  <span className="bg-gray-100 px-2 py-1 rounded">{course.level}</span>
                  <span>{course.duration}</span>
                </div>
              </div>
            ))}
          </div>
          
          <button className="w-full mt-4 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm">
            查看更多课程
          </button>
        </div>
      </div>
    </div>
  );
};

export default MusicTutor;