import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { XMarkIcon, EyeIcon, EyeSlashIcon, MusicalNoteIcon, CheckIcon } from '@heroicons/react/24/outline';
import { ApiService } from '../../services/api';
import { useLanguage } from '../../contexts/LanguageContext';
import { UserRole, USER_ROLE_LABELS } from '../../types/user';

interface RegisterProps {
  onClose?: () => void;
  onSwitchToLogin?: () => void;
}

const Register: React.FC<RegisterProps> = ({ onClose, onSwitchToLogin }) => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    role: 'user' as UserRole
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [step, setStep] = useState(1);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const navigate = useNavigate();
  const { language } = useLanguage();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (formData.password !== formData.confirmPassword) {
      setError(language === 'zh' ? '密码确认不匹配' : 'Password confirmation does not match');
      setLoading(false);
      return;
    }

    if (!acceptTerms) {
      setError(language === 'zh' ? '请同意用户协议和隐私政策' : 'Please agree to the terms of service and privacy policy');
      setLoading(false);
      return;
    }

    try {
      const { confirmPassword, ...registerData } = formData;
      await ApiService.register(registerData);
      onClose?.() || navigate('/');
    } catch (err: any) {
      setError(err.response?.data?.message || (language === 'zh' ? '注册失败' : 'Registration failed'));
    } finally {
      setLoading(false);
    }
  };

  const validatePassword = (password: string) => {
    const checks = [
      { text: '至少8个字符', valid: password.length >= 8 },
      { text: '包含大写字母', valid: /[A-Z]/.test(password) },
      { text: '包含小写字母', valid: /[a-z]/.test(password) },
      { text: '包含数字', valid: /\d/.test(password) },
    ];
    return checks;
  };

  const nextStep = () => {
    if (step === 1) {
      if (!formData.username || !formData.email) {
        setError('请填写用户名和邮箱');
        return;
      }
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        setError('请输入有效的邮箱地址');
        return;
      }
    }
    setError('');
    setStep(step + 1);
  };

  const prevStep = () => {
    setError('');
    setStep(step - 1);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md relative overflow-hidden transform scale-[0.8]">
        {/* Close button */}
        {onClose && (
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors z-10"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        )}

        {/* Header with gradient background */}
        <div className="bg-gradient-to-r from-slate-800 to-blue-600 p-3 text-white">
          <div className="flex items-center justify-center mb-2">
            <div className="relative">
              <MusicalNoteIcon className="h-6 w-6 text-white" />
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full opacity-60 animate-pulse"></div>
            </div>
            <span className="ml-2 text-xl font-bold">iBOM</span>
          </div>
          <h2 className="text-center text-lg font-bold">
            开启专业音乐制作之旅
          </h2>
          <p className="text-center text-blue-100 mt-1 text-sm">
            加入全球专业制作人社区
          </p>
          
          {/* Progress indicator */}
          <div className="flex justify-center mt-3">
            <div className="flex space-x-2">
              {[1, 2].map((stepNum) => (
                <div
                  key={stepNum}
                  className={`w-3 h-3 rounded-full transition-all ${
                    step >= stepNum ? 'bg-white' : 'bg-white/30'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        <div className="p-8">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}
            
            {step === 1 && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    用户名
                  </label>
                  <input
                    type="text"
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="输入你的用户名"
                    value={formData.username}
                    onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    邮箱地址
                  </label>
                  <input
                    type="email"
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="输入你的邮箱"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    手机号（可选）
                  </label>
                  <input
                    type="tel"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="输入你的手机号"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    账户类型
                  </label>
                  <select
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    value={formData.role}
                    onChange={(e) => setFormData({ ...formData, role: e.target.value as UserRole })}
                  >
                    {Object.entries(USER_ROLE_LABELS).map(([role, label]) => (
                      <option key={role} value={role}>
                        {label}
                      </option>
                    ))}
                  </select>
                </div>

                <button
                  type="button"
                  onClick={nextStep}
                  className="w-full bg-gradient-to-r from-blue-600 to-slate-800 hover:from-blue-700 hover:to-slate-900 text-white py-3 px-4 rounded-lg font-medium transition-all transform hover:scale-[1.02]"
                >
                  注册
                </button>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    密码
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors pr-12"
                      placeholder="创建安全密码"
                      value={formData.password}
                      onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? (
                        <EyeSlashIcon className="h-5 w-5" />
                      ) : (
                        <EyeIcon className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                  
                  {/* Password strength indicator */}
                  {formData.password && (
                    <div className="mt-2 space-y-1">
                      {validatePassword(formData.password).map((check, index) => (
                        <div key={index} className="flex items-center text-xs">
                          <CheckIcon 
                            className={`h-3 w-3 mr-1 ${check.valid ? 'text-green-500' : 'text-gray-300'}`}
                          />
                          <span className={check.valid ? 'text-green-600' : 'text-gray-500'}>
                            {check.text}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    确认密码
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors pr-12"
                      placeholder="再次输入密码"
                      value={formData.confirmPassword}
                      onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showConfirmPassword ? (
                        <EyeSlashIcon className="h-5 w-5" />
                      ) : (
                        <EyeIcon className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                  {formData.confirmPassword && formData.password !== formData.confirmPassword && (
                    <p className="mt-1 text-xs text-red-500">密码不匹配</p>
                  )}
                </div>

                <div className="flex items-start">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                    checked={acceptTerms}
                    onChange={(e) => setAcceptTerms(e.target.checked)}
                  />
                  <div className="ml-3 text-sm">
                    <span className="text-gray-600">
                      我同意{' '}
                      <a href="#" className="text-blue-600 hover:text-blue-500">
                        用户协议
                      </a>
                      {' '}和{' '}
                      <a href="#" className="text-blue-600 hover:text-blue-500">
                        隐私政策
                      </a>
                    </span>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors"
                  >
                    上一步
                  </button>
                  <button
                    type="submit"
                    disabled={loading || !acceptTerms}
                    className="flex-1 bg-gradient-to-r from-blue-600 to-slate-800 hover:from-blue-700 hover:to-slate-900 text-white py-3 px-4 rounded-lg font-medium transition-all transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {loading ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        注册中...
                      </div>
                    ) : (
                      '创建账户'
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* Login link moved to bottom right */}
            <div className="text-right mt-4">
              <p className="text-sm text-gray-600">
                已有账户？{' '}
                <button
                  onClick={onSwitchToLogin}
                  className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
                >
                  立即登录
                </button>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Register;