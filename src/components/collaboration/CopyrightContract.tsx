import React, { useState } from 'react';
import { 
  DocumentTextIcon, 
  CalendarIcon, 
  UserGroupIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  PlusIcon,
  TrashIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';

interface ContractParty {
  id: number;
  name: string;
  role: string;
  percentage: number;
  email: string;
  phone: string;
}

interface ContractTerms {
  workTitle: string;
  workType: string;
  territory: string;
  duration: string;
  startDate: string;
  endDate: string;
  paymentMethod: string;
  distributionPlatforms: string[];
  specialTerms: string;
}

const CopyrightContract: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const [contractParties, setContractParties] = useState<ContractParty[]>([
    {
      id: 1,
      name: "音乐制作人王某",
      role: "作曲",
      percentage: 40,
      email: "<EMAIL>",
      phone: "138****1234"
    },
    {
      id: 2,
      name: "作词人李某",
      role: "作词",
      percentage: 30,
      email: "<EMAIL>", 
      phone: "139****5678"
    },
    {
      id: 3,
      name: "歌手张某",
      role: "演唱",
      percentage: 20,
      email: "<EMAIL>",
      phone: "136****9012"
    },
    {
      id: 4,
      name: "混音师陈某",
      role: "混音",
      percentage: 10,
      email: "<EMAIL>",
      phone: "137****3456"
    }
  ]);

  const [contractTerms, setContractTerms] = useState<ContractTerms>({
    workTitle: "原创流行歌曲《梦想启航》",
    workType: "音乐作品",
    territory: "全球",
    duration: "永久",
    startDate: "2024-02-01",
    endDate: "",
    paymentMethod: "按收益分配",
    distributionPlatforms: ["Spotify", "Apple Music", "网易云音乐", "QQ音乐", "酷狗音乐"],
    specialTerms: ""
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [contractGenerated, setContractGenerated] = useState(false);

  const handlePartyChange = (id: number, field: keyof ContractParty, value: string | number) => {
    setContractParties(prev => 
      prev.map(party => 
        party.id === id ? { ...party, [field]: value } : party
      )
    );
  };

  const addParty = () => {
    const newId = Math.max(...contractParties.map(p => p.id)) + 1;
    setContractParties(prev => [...prev, {
      id: newId,
      name: "",
      role: "",
      percentage: 0,
      email: "",
      phone: ""
    }]);
  };

  const removeParty = (id: number) => {
    setContractParties(prev => prev.filter(party => party.id !== id));
  };

  const handleTermsChange = (field: keyof ContractTerms, value: string | string[]) => {
    setContractTerms(prev => ({ ...prev, [field]: value }));
  };

  const getTotalPercentage = () => {
    return contractParties.reduce((sum, party) => sum + party.percentage, 0);
  };

  const handleGenerateContract = async () => {
    if (getTotalPercentage() !== 100) {
      alert(t('copyright.percentage.sum.error'));
      return;
    }

    if (contractParties.some(party => !party.name || !party.role)) {
      alert(t('copyright.party.info.incomplete'));
      return;
    }

    setIsGenerating(true);
    
    // 模拟生成合约过程
    setTimeout(() => {
      setIsGenerating(false);
      setContractGenerated(true);
      alert(t('copyright.contract.success'));
    }, 3000);
  };

  const roles = [
    t('copyright.role.lyricist'), t('copyright.role.composer'), t('copyright.role.arranger'), t('copyright.role.vocalist'), t('copyright.role.mixing'), t('copyright.role.producer'), t('copyright.role.publisher'), t('copyright.role.other')
  ];

  const platforms = [
    "Spotify", "Apple Music", "Amazon Music", "YouTube Music",
    "网易云音乐", "QQ音乐", "酷狗音乐", "酷我音乐", "虾米音乐"
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* 页面头部 */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <button
              onClick={() => navigate(-1)}
              className="text-blue-600 hover:text-blue-800"
            >
              ← {t('copyright.contract.back')}
            </button>
            <DocumentTextIcon className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">{t('copyright.contract.title')}</h1>
          </div>
          <p className="text-gray-600">
            {t('copyright.contract.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：合约设置 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 作品信息 */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center gap-2">
                <InformationCircleIcon className="w-5 h-5" />
                {t('copyright.work.info')}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('copyright.work.title')} *
                  </label>
                  <input
                    type="text"
                    value={contractTerms.workTitle}
                    onChange={(e) => handleTermsChange('workTitle', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('copyright.work.type')}
                  </label>
                  <select
                    value={contractTerms.workType}
                    onChange={(e) => handleTermsChange('workType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={t('copyright.work.type.music')}>{t('copyright.work.type.music')}</option>
                    <option value={t('copyright.work.type.single')}>{t('copyright.work.type.single')}</option>
                    <option value={t('copyright.work.type.album')}>{t('copyright.work.type.album')}</option>
                    <option value={t('copyright.work.type.ep')}>{t('copyright.work.type.ep')}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('copyright.territory')}
                  </label>
                  <select
                    value={contractTerms.territory}
                    onChange={(e) => handleTermsChange('territory', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={t('copyright.territory.global')}>{t('copyright.territory.global')}</option>
                    <option value={t('copyright.territory.china')}>{t('copyright.territory.china')}</option>
                    <option value={t('copyright.territory.apac')}>{t('copyright.territory.apac')}</option>
                    <option value={t('copyright.territory.north.america')}>{t('copyright.territory.north.america')}</option>
                    <option value={t('copyright.territory.europe')}>{t('copyright.territory.europe')}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('copyright.duration')}
                  </label>
                  <select
                    value={contractTerms.duration}
                    onChange={(e) => handleTermsChange('duration', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={t('copyright.duration.permanent')}>{t('copyright.duration.permanent')}</option>
                    <option value={t('copyright.duration.5years')}>{t('copyright.duration.5years')}</option>
                    <option value={t('copyright.duration.10years')}>{t('copyright.duration.10years')}</option>
                    <option value={t('copyright.duration.20years')}>{t('copyright.duration.20years')}</option>
                    <option value={t('copyright.duration.custom')}>{t('copyright.duration.custom')}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('copyright.effective.date')}
                  </label>
                  <input
                    type="date"
                    value={contractTerms.startDate}
                    onChange={(e) => handleTermsChange('startDate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('copyright.payment.method')}
                  </label>
                  <select
                    value={contractTerms.paymentMethod}
                    onChange={(e) => handleTermsChange('paymentMethod', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={t('copyright.payment.revenue.share')}>{t('copyright.payment.revenue.share')}</option>
                    <option value={t('copyright.payment.one.time')}>{t('copyright.payment.one.time')}</option>
                    <option value={t('copyright.payment.installment')}>{t('copyright.payment.installment')}</option>
                  </select>
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('copyright.distribution.platforms')}
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {platforms.map((platform) => (
                    <label key={platform} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={contractTerms.distributionPlatforms.includes(platform)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleTermsChange('distributionPlatforms', [...contractTerms.distributionPlatforms, platform]);
                          } else {
                            handleTermsChange('distributionPlatforms', contractTerms.distributionPlatforms.filter(p => p !== platform));
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{platform}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('copyright.special.terms')}
                </label>
                <textarea
                  value={contractTerms.specialTerms}
                  onChange={(e) => handleTermsChange('specialTerms', e.target.value)}
                  rows={3}
                  placeholder={t('copyright.special.terms.placeholder')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* 参与方信息 */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <UserGroupIcon className="w-5 h-5" />
                  {t('copyright.participants.title')}
                </h2>
                <button
                  onClick={addParty}
                  className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  <PlusIcon className="w-4 h-4" />
                  {t('copyright.add.party')}
                </button>
              </div>

              <div className="space-y-4">
                {contractParties.map((party, index) => (
                  <div key={party.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="font-medium text-gray-900">{t('copyright.party.number')} {index + 1}</h3>
                      {contractParties.length > 1 && (
                        <button
                          onClick={() => removeParty(party.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('copyright.party.name')} *
                        </label>
                        <input
                          type="text"
                          value={party.name}
                          onChange={(e) => handlePartyChange(party.id, 'name', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('copyright.party.role')} *
                        </label>
                        <select
                          value={party.role}
                          onChange={(e) => handlePartyChange(party.id, 'role', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        >
                          <option value="">{t('copyright.party.select.role')}</option>
                          {roles.map((role) => (
                            <option key={role} value={role}>{role}</option>
                          ))}
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('copyright.party.percentage')} *
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={party.percentage}
                          onChange={(e) => handlePartyChange(party.id, 'percentage', parseInt(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('copyright.party.email')}
                        </label>
                        <input
                          type="email"
                          value={party.email}
                          onChange={(e) => handlePartyChange(party.id, 'email', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('copyright.party.phone')}
                        </label>
                        <input
                          type="tel"
                          value={party.phone}
                          onChange={(e) => handlePartyChange(party.id, 'phone', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-700">{t('copyright.total.percentage')}</span>
                  <span className={`font-bold ${getTotalPercentage() === 100 ? 'text-green-600' : 'text-red-600'}`}>
                    {getTotalPercentage()}%
                  </span>
                </div>
                {getTotalPercentage() !== 100 && (
                  <p className="text-sm text-red-600 mt-1">
                    {t('copyright.percentage.warning')}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* 右侧：合约预览和操作 */}
          <div className="space-y-6">
            {/* 合约状态 */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <ClockIcon className="w-5 h-5" />
                {t('copyright.contract.status')}
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${contractGenerated ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                  <span className={contractGenerated ? 'text-green-700' : 'text-gray-500'}>
                    {t('copyright.status.generation')}
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                  <span className="text-gray-500">{t('copyright.status.blockchain')}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                  <span className="text-gray-500">{t('copyright.status.signing')}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                  <span className="text-gray-500">{t('copyright.status.effective')}</span>
                </div>
              </div>
            </div>

            {/* 分成比例可视化 */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <CurrencyDollarIcon className="w-5 h-5" />
                {t('copyright.percentage.preview')}
              </h3>
              
              <div className="space-y-3">
                {contractParties.map((party) => (
                  <div key={party.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                      <span className="text-sm text-gray-700">
                        {party.name || t('copyright.party.unnamed')} ({party.role || t('copyright.role.unset')})
                      </span>
                    </div>
                    <span className="font-medium">{party.percentage}%</span>
                  </div>
                ))}
              </div>

              {getTotalPercentage() === 100 && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 text-green-700">
                    <CheckCircleIcon className="w-4 h-4" />
                    <span className="text-sm font-medium">{t('copyright.percentage.complete')}</span>
                  </div>
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('copyright.actions')}</h3>
              
              <div className="space-y-3">
                <button
                  onClick={handleGenerateContract}
                  disabled={isGenerating || getTotalPercentage() !== 100}
                  className={`w-full py-3 rounded-lg font-medium transition-colors ${
                    isGenerating || getTotalPercentage() !== 100
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isGenerating ? t('copyright.generating') : t('copyright.generate')}
                </button>
                
                {contractGenerated && (
                  <>
                    <button className="w-full py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                      {t('copyright.view.details')}
                    </button>
                    <button className="w-full py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                      {t('copyright.send.invitation')}
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CopyrightContract;